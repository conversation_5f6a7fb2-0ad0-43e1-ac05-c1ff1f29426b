import { MaterialIcons } from '@expo/vector-icons';
import { api } from 'common';
import i18n from 'i18n-js';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, StyleSheet, Text, TextInput, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { fonts } from '../common/font';
import { colors } from '../common/theme';

const RatesScreen = () => {
  const dispatch = useDispatch();
  const { t } = i18n;
  const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
  const ratelist = useSelector(state => state.ratelist);
  const settings = useSelector(state => state.settingsdata.settings);
  const [search, setSearch] = useState('');
  const [filteredRates, setFilteredRates] = useState([]);

  useEffect(() => {
    if (!ratelist.rate && !ratelist.loading) {
      dispatch(api.fetchRate());
    }
  }, [dispatch, ratelist.rate, ratelist.loading]);

  useEffect(() => {
    if (ratelist.rate && search.trim().length > 0) {
      const lowerSearch = search.toLowerCase();
      setFilteredRates(
        ratelist.rate.filter(item =>
          (item.pincode1 && item.pincode1.toLowerCase().includes(lowerSearch)) ||
          (item.pincode2 && item.pincode2.toLowerCase().includes(lowerSearch))
        )
      );
    } else {
      setFilteredRates([]);
    }
  }, [search, ratelist.rate]);

  const renderRateCard = ({ item }) => (
    <View style={styles.card}>
      {/* <View style={styles.badgeContainer}>
        <Text style={styles.badgeText}>{item.type || 'Economy'}</Text>
      </View> */}
      <View style={styles.locationContainer}>
        {/* FROM */}
        <View style={styles.locationSide}>
          <View style={[styles.dot, { backgroundColor: '#22c55e' }]} />
          <View>
            <Text style={styles.label}>FROM</Text>
            <Text style={styles.city}>{item.pincode1}</Text>
          </View>
        </View>
        {/* ARROW */}
        <View style={styles.arrowCenter}>
          <MaterialIcons name="arrow-forward" size={28} color="#a3a3a3" />
        </View>
        {/* TO */}
        <View style={[styles.locationSide ]}>
          <View style={[styles.dot, { backgroundColor: '#ef4444' }]} />
          <View>
            <Text style={styles.label}>TO</Text>
            <Text style={styles.city}>{item.pincode2}</Text>
          </View>
        </View>
      </View>
      <View style={styles.divider} />
      <View style={styles.fareRow}>
        <Text style={styles.fareLabel}>{settings?.symbol || '$'} FARE</Text>
        <Text style={styles.fareValue}>
          {settings && settings.swipe_symbol === false
            ? `${settings.symbol}${parseFloat(item.fare).toFixed(settings.decimal)}`
            : `${parseFloat(item.fare).toFixed(settings.decimal)}${settings ? settings.symbol : ''}`}
        </Text>
      </View>
    </View>
  );

  if (ratelist.loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.MAIN_COLOR} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{'Rates'}</Text>
      <TextInput
        style={styles.searchInput}
        placeholder={'Search by pincode...'}
        value={search}
        onChangeText={setSearch}
        placeholderTextColor={colors.GRAY}
        keyboardType="numeric"
      />
      {search.trim().length === 0 ? (
        <Text style={styles.emptyText}>Please enter a pincode to search.</Text>
      ) : (
        <FlatList
          data={filteredRates || []}
          renderItem={renderRateCard}
          keyExtractor={(item, index) => item.id || index.toString()}
          ListEmptyComponent={<Text style={styles.emptyText}>Enter a valid pincode</Text>}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: colors.WHITE, padding: 16 },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 16, fontFamily: fonts.Bold, color: colors.HEADER },
  searchInput: { height: 40, borderColor: colors.BORDER, borderWidth: 1, borderRadius: 8, paddingHorizontal: 12, marginBottom: 12, fontFamily: fonts.Regular, color: colors.BLACK },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  emptyText: { textAlign: 'center', marginTop: 20, color: colors.GRAY, fontSize: 16 },
  card: {
    backgroundColor: colors.WHITE,
    borderRadius: 16,
    padding: 20,
    marginVertical: 12,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    position: 'relative',
  },
  badgeContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#d1fae5',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
    zIndex: 1,
  },
  badgeText: {
    color: '#16a34a',
    fontWeight: 'bold',
    fontSize: 13,
    fontFamily: fonts.Bold,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    marginTop: 8,
  },
  locationSide: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 6,
  },
  arrowCenter: {
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  label: {
    color: '#a3a3a3',
    fontSize: 12,
    fontFamily: fonts.Regular,
    letterSpacing: 1,
    marginBottom: 2,
  },
  city: {
    color: colors.BLACK,
    fontSize: 18,
    fontFamily: fonts.Bold,
  },
  divider: {
    height: 1,
    backgroundColor: '#e5e7eb',
    marginVertical: 14,
  },
  fareRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  fareLabel: {
    color: '#a3a3a3',
    fontSize: 15,
    fontFamily: fonts.Regular,
    letterSpacing: 1,
  },
  fareValue: {
    color: colors.BLACK,
    fontSize: 28,
    fontFamily: fonts.Bold,
    fontWeight: 'bold',
  },
});

export default RatesScreen;
