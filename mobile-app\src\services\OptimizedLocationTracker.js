import * as Location from 'expo-location';
import { store } from 'common';
import { api } from 'common';

class OptimizedLocationTracker {
  constructor() {
    this.watchId = null;
    this.isTracking = false;
    this.lastKnownLocation = null;
    this.routeCache = new Map(); // Cache routes to avoid repeated API calls
    this.lastRouteUpdate = 0;
    this.routeUpdateInterval = 10 * 60 * 1000; // 10 minutes between route updates
    this.minDistanceForRouteUpdate = 1000; // 1km minimum distance for route update
    this.lastRouteOrigin = null;
  }

  // Start watching position with optimized settings
  async startWatchPosition(onLocationUpdate, onError) {
    try {
      // Check permissions first
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
        if (newStatus !== 'granted') {
          throw new Error('Location permission denied');
        }
      }

      // Clear any existing watcher
      if (this.watchId) {
        Location.clearWatchAsync(this.watchId);
      }

      // Start watching position with optimized settings
      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
          mayShowUserSettingsDialog: false,
        },
        (location) => {
          this.lastKnownLocation = {
            lat: location.coords.latitude,
            lng: location.coords.longitude,
            timestamp: Date.now(),
            accuracy: location.coords.accuracy,
            speed: location.coords.speed || 0,
          };

          // Call the callback with location data
          if (onLocationUpdate) {
            onLocationUpdate(this.lastKnownLocation);
          }

          // Update Redux store
          store.dispatch({
            type: 'UPDATE_GPS_LOCATION',
            payload: this.lastKnownLocation,
          });
        },
        (error) => {
          console.error('Location watch error:', error);
          if (onError) {
            onError(error);
          }
        }
      );

      this.isTracking = true;
      return true;
    } catch (error) {
      console.error('Failed to start location watching:', error);
      if (onError) {
        onError(error);
      }
      return false;
    }
  }

  // Stop watching position
  async stopWatchPosition() {
    if (this.watchId) {
      await Location.clearWatchAsync(this.watchId);
      this.watchId = null;
    }
    this.isTracking = false;
  }

  // Get cached route or fetch new one if needed
  async getOptimizedRoute(startLoc, destLoc, waypoints = '') {
    const routeKey = `${startLoc.lat},${startLoc.lng}_${destLoc.lat},${destLoc.lng}_${waypoints}`;
    const now = Date.now();

    // Check if we have a cached route
    if (this.routeCache.has(routeKey)) {
      const cachedRoute = this.routeCache.get(routeKey);
      const timeSinceCache = now - cachedRoute.timestamp;
      
      // Use cached route if it's less than 10 minutes old
      if (timeSinceCache < this.routeUpdateInterval) {
        return cachedRoute.data;
      }
    }

    // Check if we need a new route based on distance moved
    let shouldFetchNewRoute = true;
    if (this.lastRouteOrigin && typeof api?.GetDistance === 'function') {
      const distanceMoved = api.GetDistance(
        startLoc.lat,
        startLoc.lng,
        this.lastRouteOrigin.lat,
        this.lastRouteOrigin.lng
      ) * 1000; // Convert to meters

      // Only fetch new route if moved significantly
      shouldFetchNewRoute = distanceMoved >= this.minDistanceForRouteUpdate;
    }

    if (!shouldFetchNewRoute) {
      // Return cached route even if slightly old
      return this.routeCache.get(routeKey)?.data;
    }

    try {
      // Fetch new route
      const routeData = await api.getDirectionsApi(
        `${startLoc.lat},${startLoc.lng}`,
        `${destLoc.lat},${destLoc.lng}`,
        waypoints
      );

      // Cache the route
      this.routeCache.set(routeKey, {
        data: routeData,
        timestamp: now,
      });

      // Update last route origin
      this.lastRouteOrigin = { lat: startLoc.lat, lng: startLoc.lng };

      // Clean old cache entries (keep only last 10 routes)
      if (this.routeCache.size > 10) {
        const entries = Array.from(this.routeCache.entries());
        entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
        this.routeCache.clear();
        entries.slice(0, 10).forEach(([key, value]) => {
          this.routeCache.set(key, value);
        });
      }

      return routeData;
    } catch (error) {
      console.error('Failed to fetch route:', error);
      // Return cached route if available
      return this.routeCache.get(routeKey)?.data;
    }
  }

  // Get current location
  getCurrentLocation() {
    return this.lastKnownLocation;
  }

  // Check if tracking is active
  isTrackingActive() {
    return this.isTracking;
  }

  // Clear route cache
  clearRouteCache() {
    this.routeCache.clear();
  }
}

// Singleton instance
const optimizedLocationTracker = new OptimizedLocationTracker();
export default optimizedLocationTracker;
