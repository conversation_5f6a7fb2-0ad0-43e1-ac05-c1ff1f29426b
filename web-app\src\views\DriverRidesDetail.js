import { <PERSON>, Button, Grid, Modal, Typography } from "@mui/material";
import { makeStyles, ThemeProvider } from "@mui/styles";
import { api } from "common";
import MaterialTable from "material-table";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { bookingHistoryColumns, FONT_FAMILY, MAIN_COLOR, SECONDORY_COLOR } from "../common/sharedFunctions";
import AlertDialog from "../components/AlertDialog";
import CircularLoading from "../components/CircularLoading";
import { colors } from "../components/Theme/WebTheme";
import UsersCombo from "../components/UsersCombo";
import theme from "../styles/tableStyle";

const useStyles = makeStyles(() => ({
  root: {
    padding: 24,
    background: colors.WHITE,
    minHeight: '100vh',
    boxSizing: 'border-box',
    '@media (max-width: 900px)': {
      padding: 12,
    },
    '@media (max-width: 600px)': {
      padding: 4,
    },
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '20px',
    gap: '16px',
  },
  driverInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    marginBottom: '20px',
    padding: '16px',
    backgroundColor: '#f5f5f5',
    borderRadius: '8px',
  },
  tableContainer: {
    width: '100%',
    overflowX: 'auto',
    boxSizing: 'border-box',
    '@media (max-width: 600px)': {
      minWidth: 320,
    },
  },
  modal: {
    display: "flex",
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  paper: {
    width: 680,
    backgroundColor: colors.WHITE,
    border: `2px solid ${colors.BLACK}`,
    boxShadow: '0px 4px 20px rgba(0,0,0,0.1)',
    padding: 24,
    borderRadius: '8px',
  },
  action: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
    justifyContent: "center",
    borderRadius: "20px",
    paddingLeft: "10px",
    paddingRight: "10px",
    width: "10rem",
    minHeight: "40px"
  },
}));

const DriverRidesDetail = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const classes = useStyles();
  const { driverId } = useParams();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const mode = searchParams.get('mode') || 'active';
  const navigate = useNavigate();
  const role = useSelector((state) => state.auth.profile.usertype);
  const settings = useSelector((state) => state.settingsdata.settings);
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const userdata = useSelector((state) => state.usersdata);
  const auth = useSelector((state) => state.auth);
  const [data, setData] = useState([]);
  const [driverInfo, setDriverInfo] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [loading, setLoading] = useState(false);
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: "" });
  const [reassignModalOpen, setReassignModalOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const dispatch = useDispatch();
  const { fetchBookings, updateBooking, RequestPushMsg } = api;

  useEffect(() => {
    // Find driver information
    if (userdata.users && driverId) {
      const driver = userdata.users.find(user => 
        user.usertype === "driver" && 
        (user.id === driverId || user.uid === driverId)
      );
      setDriverInfo(driver);
    }
  }, [userdata.users, driverId]);

  useEffect(() => {
    if (bookinglistdata.bookings && driverId) {
      // Filter bookings for this specific driver depending on mode
      const allDriverBookings = bookinglistdata.bookings.filter(booking => 
        (booking.driver === driverId || booking.driver === driverInfo?.id)
      );
      if (mode === 'history') {
        setData(allDriverBookings);
      } else {
        const activeStatuses = ["ACCEPTED", "ARRIVED", "STARTED", "Pending"];
        const activeBookings = allDriverBookings.filter(booking => activeStatuses.includes(booking.status));
        setData(activeBookings);
      }
    } else {
      setData([]);
    }
  }, [bookinglistdata.bookings, driverId, driverInfo, mode]);

  useEffect(() => {
    // Get available drivers for reassignment
    if (userdata.users) {
      const drivers = userdata.users.filter(user => 
        user.usertype === "driver" && 
        user.id !== driverId &&
        ((role === "fleetadmin" && user.fleetadmin === auth.profile?.uid) || role === "admin")
      ).map(driver => ({
        firstName: driver.firstName,
        lastName: driver.lastName,
        mobile: driver.mobile,
        email: driver.email,
        uid: driver.id,
        desc: `${driver.firstName}  (${settings.AllowCriticalEditsAdmin ? driver.mobile : t("hidden_demo")}) - ${settings.AllowCriticalEditsAdmin ? driver.email : t("hidden_demo")} - ${driver.driverActiveStatus ? t('on_duty') : t('off_duty')} - ${driver.queue ? t('In Queue') : t('Available')}`,
        pushToken: driver.pushToken || "",
        carType: driver.carType,
        driverActiveStatus: driver.driverActiveStatus,
        queue: driver.queue,
      }));
      setAvailableDrivers(drivers);
    }
  }, [userdata.users, driverId, role, auth.profile?.uid, settings.AllowCriticalEditsAdmin, t]);

  const handleBack = () => {
    navigate('/assignedride?tab=1');
  };

  const handleReassign = (booking) => {
    setSelectedBooking(booking);
    setReassignModalOpen(true);
  };

  const handleReassignConfirm = () => {
    if (!selectedDriver) {
      setCommonAlert({ open: true, msg: t("please_select_driver") });
      return;
    }

    const updatedBooking = { ...selectedBooking };
    updatedBooking.driver = selectedDriver.uid;
    updatedBooking.driver_name = `${selectedDriver.firstName} ${selectedDriver.lastName}`;
    updatedBooking.driver_contact = selectedDriver.mobile;
    updatedBooking.driver_token = selectedDriver.pushToken;
    updatedBooking.carType = selectedDriver.carType;

    dispatch(updateBooking(updatedBooking));

    // Send notification to new driver
    if (selectedDriver.pushToken) {
      RequestPushMsg(selectedDriver.pushToken, {
        title: t("notification_title"),
        msg: t("new_booking_notification"),
        screen: "DriverTrips",
        channelId: settings.CarHornRepeat ? "bookings-repeat" : "bookings",
      });
    }

    setReassignModalOpen(false);
    setSelectedDriver(null);
    setSelectedBooking(null);
    setCommonAlert({ open: true, msg: t("driver_reassigned_successfully") });
  };

  const handleReassignCancel = () => {
    setReassignModalOpen(false);
    setSelectedDriver(null);
    setSelectedBooking(null);
  };

  const columns = [
    ...bookingHistoryColumns(role, settings, t, isRTL).map(col => {
      if (col.field === 'status' || col.field === 'booking_status_web') {
        return {
          ...col,
          cellStyle: rowData => ({
            backgroundColor: rowData.status === 'CANCELLED' ? colors.RED : undefined,
            color: rowData.status === 'CANCELLED' ? colors.WHITE : undefined,
            fontWeight: rowData.status === 'CANCELLED' ? 'bold' : undefined,
            borderRadius: rowData.status === 'CANCELLED' ? '10px' : undefined,
            textAlign: 'center',
            padding: 3,
          })
        };
      }
      return col;
    }),
    {
      title: t("actions"),
      field: "actions",
      editable: "never",
      render: (rowData) => (
        <Button
          variant="contained"
          disabled={rowData.status !== "ACCEPTED"}
          onClick={() => {
            if (settings.AllowCriticalEditsAdmin && (role === "admin" || role === "fleetadmin")) {
              handleReassign(rowData);
            } else {
              setCommonAlert({ open: true, msg: t("demo_mode") });
            }
          }}
          style={{
            backgroundColor: rowData.status === "ACCEPTED" ? colors.Action_Button_Back : colors.GRAY,
            color: colors.Header_Background,
            fontFamily: FONT_FAMILY,
            fontSize: '0.8rem',
            padding: '4px 8px',
            minWidth: '80px',
            opacity: rowData.status === "ACCEPTED" ? 1 : 0.6,
          }}
        >
          {t("reassign")}
        </Button>
      ),
    }
  ];

  return (
    <div className={classes.root}>
      <ThemeProvider theme={theme}>
        <div>
          <Button
            variant="text" 
            onClick={handleBack} 
          >
            <Typography
              style={{
                margin: "10px 10px 0 5px",
                textAlign: isRTL === "rtl" ? "right" : "left",
                fontWeight: "bold",
                color: MAIN_COLOR,
                fontFamily: FONT_FAMILY,
              }}
            >
              {`<<- ${t("go_back")}`}
            </Typography>
          </Button>
        </div>

        <Box className={classes.tableContainer}>
          <MaterialTable
            title={t("Driver_Rides_Detail")}
            style={{
              direction: isRTL === "rtl" ? "rtl" : "ltr",
              borderRadius: "8px",
              boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
              minWidth: 320,
            }}
            columns={columns}
            data={data}
            onRowClick={(evt, selectedRow) => setSelectedRow(selectedRow.tableData.id)}
            options={{
              pageSize: 100,
              pageSizeOptions: [100, 500, { value: data.length, label: t("All") }],
              toolbarbuttonalignment: "right",
              exportButton: true,
              maxColumnSort: "all_columns",
              rowStyle: rowData => ({
                backgroundColor: selectedRow === rowData.tableData.id ? colors.ROW_SELECTED : colors.WHITE
              }),
              maxBodyHeight: "calc(100vh - 199.27px)",
              headerStyle: {
                position: "sticky",
                top: "0px",
                backgroundColor: SECONDORY_COLOR,
                color: colors.Black,
                fontWeight: "bold ",
                textAlign: "center",
                zIndex: 1,
                border: `1px solid ${colors.TABLE_BORDER}`,
              },
              cellStyle: {
                border: `1px solid ${colors.TABLE_BORDER}`,
                textAlign: "center",
                margin: "auto",
                minWidth: 100,
                fontSize: '0.95rem',
                padding: 4,
              },
            }}
            localization={{
              toolbar: {
                searchPlaceholder: t("search"),
                exportTitle: t("export"),
                exportCSVName: t("export"),
              },
              header: {
                actions: t("actions"),
              },
              pagination: {
                labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                firstTooltip: t("first_page_tooltip"),
                previousTooltip: t("previous_page_tooltip"),
                nextTooltip: t("next_page_tooltip"),
                lastTooltip: t("last_page_tooltip"),
              },
              body: {
                emptyDataSourceMessage: t("No rides to display"),
              },
            }}
          />
        </Box>

        {/* Reassign Modal */}
        <Modal
          open={reassignModalOpen}
          onClose={handleReassignCancel}
          className={classes.modal}
        >
          <div className={classes.paper}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography
                  component="h1"
                  variant="h5"
                  style={{ 
                    textAlign: isRTL === "rtl" ? "right" : "left", 
                    fontFamily: FONT_FAMILY,
                    marginBottom: '16px'
                  }}
                >
                  {t("reassign_ride")}
                </Typography>
                {selectedBooking && (
                  <Typography variant="body1" style={{ fontFamily: FONT_FAMILY, marginBottom: '16px' }}>
                    {t("booking_ref")}: {selectedBooking.reference}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12}>
                <UsersCombo
                  placeholder={t("select_new_driver")}
                  users={availableDrivers}
                  value={selectedDriver}
                  onChange={(event, newValue) => {
                    setSelectedDriver(newValue);
                  }}
                />
              </Grid>
              {selectedDriver && (
                <Grid item xs={12}>
                  <div style={{ 
                    padding: '12px', 
                    backgroundColor: '#f5f5f5', 
                    borderRadius: '8px',
                    marginTop: '8px'
                  }}>
                    <Typography variant="body2" style={{ fontFamily: FONT_FAMILY, fontWeight: 'bold', marginBottom: '4px' }}>
                      {t("selected_driver_info")}:
                    </Typography>
                    <Typography variant="body2" style={{ fontFamily: FONT_FAMILY, marginBottom: '2px' }}>
                      {t("name")}: {selectedDriver.firstName} {selectedDriver.lastName}
                    </Typography>
                    <Typography variant="body2" style={{ fontFamily: FONT_FAMILY, marginBottom: '2px' }}>
                      {t("mobile")}: {settings.AllowCriticalEditsAdmin ? selectedDriver.mobile : t("hidden_demo")}
                    </Typography>
                    <Typography variant="body2" style={{ fontFamily: FONT_FAMILY, marginBottom: '2px' }}>
                      {t("email")}: {settings.AllowCriticalEditsAdmin ? selectedDriver.email : t("hidden_demo")}
                    </Typography>
                    <Typography variant="body2" style={{ fontFamily: FONT_FAMILY, marginBottom: '2px' }}>
                      {t("status")}: 
                      <span style={{ 
                        color: selectedDriver.driverActiveStatus ? colors.GREEN : colors.RED,
                        fontWeight: 'bold',
                        marginLeft: '4px'
                      }}>
                        {selectedDriver.driverActiveStatus ? t('Active') : t('Inactive')}
                      </span>
                    </Typography>
                    <Typography variant="body2" style={{ fontFamily: FONT_FAMILY }}>
                      {t("queue")}: 
                      <span style={{ 
                        color: selectedDriver.queue ? colors.YELLOW : colors.GREEN,
                        fontWeight: 'bold',
                        marginLeft: '4px'
                      }}>
                        {selectedDriver.queue ? t('In Queue') : t('Available')}
                      </span>
                    </Typography>
                  </div>
                </Grid>
              )}
              <Grid item xs={12} style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                <Button
                  onClick={handleReassignCancel}
                  variant="contained"
                  style={{ backgroundColor: colors.RED, fontFamily: FONT_FAMILY }}
                >
                  {t("cancel")}
                </Button>
                <Button
                  onClick={handleReassignConfirm}
                  variant="contained"
                  color="primary"
                  style={{ backgroundColor: colors.GREEN, fontFamily: FONT_FAMILY }}
                  disabled={!selectedDriver || !selectedDriver.driverActiveStatus}
                >
                  {t("reassign")}
                </Button>
              </Grid>
            </Grid>
          </div>
        </Modal>

        <AlertDialog open={commonAlert.open} onClose={() => setCommonAlert({ open: false, msg: "" })}>
          {commonAlert.msg}
        </AlertDialog>
        {loading && <CircularLoading />}
      </ThemeProvider>
    </div>
  );
};

export default DriverRidesDetail; 