import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { api } from 'common';
import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import i18n from 'i18n-js';
import React, { useState } from 'react';
import { Alert, Dimensions, Linking, Modal, Platform, Share, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Icon } from 'react-native-elements';
import { useDispatch, useSelector } from 'react-redux';
import { fonts } from '../common/font';
import { MAIN_COLOR, SECONDORY_COLOR, appConsts } from '../common/sharedFunctions';
import { colors } from '../common/theme';

const { width, height } = Dimensions.get('window');

export default function CommonMenuBar() {
    const navigation = useNavigation();
    const dispatch = useDispatch();
    const [menuVisible, setMenuVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const auth = useSelector(state => state.auth);
    const settings = useSelector(state => state.settingsdata.settings);
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    const { signOff, updateProfile, editSos } = api;

    const menuList = [
        // { name: t('profile_setting_menu'), navigationName: 'Profile', icon: 'account-cog-outline', type: 'material-community' },
        // { name: t('documents'), navigationName: 'editUser', icon: 'description', type: 'materialIcons' },
        // { name: t('incomeText'), navigationName: 'MyEarning', icon: 'attach-money', type: 'materialIcons' },
        // { name: auth.profile && auth.profile && auth.profile.usertype == "driver" ? t('convert_to_rider') : t('convert_to_driver'), navigationName: 'Convert', icon: 'account-convert-outline', type: 'material-community' },
        // { name: t('cars'), navigationName: 'Cars', icon: 'car-cog', type: 'material-community' },
        // { name: t('refer_earn'), navigationName: 'Refer', icon: 'cash-outline', type: 'ionicon' },
        // { name: t('sos'), navigationName: 'Sos', icon: 'radio-outline', type: 'ionicon' },
        // { name: t('push_notification_title'), navigationName: 'Notifications', icon: 'notifications-outline', type: 'ionicon' },
        // { name: t('complain'), navigationName: 'Complain', icon: 'chatbox-ellipses-outline', type: 'ionicon' },
        { name: t('settings_title'), navigationName: 'Settings', icon: 'settings-outline', type: 'ionicon' },
        { name: t('about_us_menu'), navigationName: 'About', icon: 'info', type: 'entypo' },
        { name: t('logout'), icon: 'logout', navigationName: 'Logout', type: 'antdesign' }
    ];

    const StopBackgroundLocation = async () => {
        TaskManager.getRegisteredTasksAsync().then((res) => {
            if (res.length > 0) {
                for (let i = 0; i < res.length; i++) {
                    if (res[i].taskName == 'background-location-task') {
                        Location.stopLocationUpdatesAsync('background-location-task');
                        break;
                    }
                }
            }
        });
    };

    const sos = () => {
        Alert.alert(
            t('panic_text'),
            t('panic_question'),
            [
                {
                    text: t('cancel'),
                    onPress: () => { },
                    style: 'cancel'
                },
                {
                    text: t('ok'), onPress: async () => {
                        let call_link = Platform.OS == 'android' ? 'tel:' + settings.panic : 'telprompt:' + settings.panic;
                        Linking.openURL(call_link);

                        let obj = {};
                        obj.bookingId = null,
                            obj.user_name = auth.profile && auth.profile && auth.profile.firstName ? auth.profile.firstName + " " + auth.profile.lastName : null;
                        obj.contact = auth.profile && auth.profile && auth.profile.mobile ? auth.profile.mobile : null;
                        obj.user_type = auth.profile && auth.profile && auth.profile.usertype ? auth.profile.usertype : null;
                        obj.complainDate = new Date().getTime();
                        dispatch(editSos(obj, "Add"));
                    }
                }
            ],
            { cancelable: false }
        )
    }

    const convert = () => {
        Alert.alert(
            t('convert_button'),
            auth.profile && auth.profile.usertype == "driver" ? t('convert_to_rider') : t('convert_to_driver'),
            [
                {
                    text: t('cancel'),
                    onPress: () => { },
                    style: 'cancel',
                },
                {
                    text: t('ok'), onPress: async () => {
                        let userData = {
                            approved: (auth.profile && auth.profile.usertype == "driver" ? true : auth.profile && auth.profile.adminApprovedTrue == true ? true : settings && settings.driver_approval ? false : true),
                            usertype: auth.profile && auth.profile.usertype == "driver" ? "customer" : "driver",
                            queue: (auth.profile && auth.profile.queue === true) ? true : false,
                            driverActiveStatus: false
                        }
                        dispatch(updateProfile(userData));
                        setTimeout(() => {
                            if (userData.usertype == 'driver') {
                                dispatch(api.fetchBookings());
                                dispatch(api.fetchTasks());
                                dispatch(api.fetchCars());
                            } else {
                                StopBackgroundLocation();
                                dispatch(api.fetchAddresses());
                                dispatch(api.fetchBookings());
                            }
                        }, 3000);
                    }
                }
            ],
            { cancelable: false }
        )
    }

    const refer = () => {
        settings.bonus > 0 ?
            Share.share({
                message: t('share_msg') + settings.code + ' ' + settings.bonus + ".\n" + t('code_colon') + auth.profile.referralId + "\n" + t('app_link') + (Platform.OS == "ios" ? settings.AppleStoreLink : settings.PlayStoreLink)
            })
            :
            Share.share({
                message: t('share_msg_no_bonus') + "\n" + t('app_link') + (Platform.OS == "ios" ? settings.AppleStoreLink : settings.PlayStoreLink)
            })
    }

    const logOff = () => {
        auth && auth.profile && auth.profile.usertype == 'driver' ? StopBackgroundLocation() : null;
        setLoading(true);
        if (auth && auth.profile && auth.profile.usertype === 'driver') { StopBackgroundLocation() };

        setTimeout(() => {
            if (auth && auth.profile && auth.profile.pushToken) {
                dispatch(updateProfile({ pushToken: null }));
            }
            dispatch(signOff());
        }, 1000);
    }

    const handleMenuPress = (item) => {
        setMenuVisible(false);
        
        if (item.navigationName === 'Sos') {
            sos();
        } else if (item.navigationName === 'Refer') {
            refer();
        } else if (item.navigationName === 'Logout') {
            logOff('Logout');
        } else if (item.navigationName === 'Convert') {
            convert();
        } else {
            navigation.navigate(item.navigationName);
        }
    };

    const shouldShowMenuItem = (item) => {
        if (auth.profile && auth.profile.usertype == "customer" && (item.navigationName == "Cars" || item.navigationName == "MyEarning")) {
            return false;
        }
        else if (auth.profile && auth.profile.usertype == "customer" && item.navigationName == "Convert") {
            return false;
        }
        else if (auth.profile && (auth.profile.usertype == "driver") && (item.navigationName == "Sos") && !(settings && settings.panic && settings.panic.length > 0)) {
            return false;
        } else if (auth.profile && auth.profile.usertype == "customer" && (item.navigationName == "Sos") && appConsts.hasOptions) {
            return false;
        } else if (auth.profile && auth.profile.usertype == "customer" && (item.navigationName == "editUser") && !(settings && ((settings.bank_fields && settings.RiderWithDraw) || settings.imageIdApproval))) {
            return false;
        } else if (auth.profile && auth.profile.usertype == "driver" && (item.navigationName == "editUser") && !(settings && (settings.bank_fields || settings.imageIdApproval || settings.license_image_required))) {
            return false;
        }
        return true;
    };

    return (
        <>
            {/* Menu Button */}
            <TouchableOpacity 
                style={styles.menuButton}
                onPress={() => setMenuVisible(true)}
            >
                <Icon 
                    name="menu" 
                    type="material" 
                    color={colors.BLACK} 
                    size={28}
                />
            </TouchableOpacity>

            {/* Menu Modal */}
            <Modal
                visible={menuVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setMenuVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setMenuVisible(false)}
                >
                    <View style={styles.menuContainer}>
                        <View style={styles.menuHeader}>
                            <Text style={styles.menuTitle}>Menu</Text>
                            <TouchableOpacity onPress={() => setMenuVisible(false)}>
                                <Icon name="close" type="material" color={colors.BLACK} size={24} />
                            </TouchableOpacity>
                        </View>
                        
                        {menuList.map((option, index) => {
                            if (!shouldShowMenuItem(option)) {
                                return null;
                            }
                            
                            return (
                                <TouchableOpacity
                                    key={index}
                                    style={styles.menuItem}
                                    onPress={() => handleMenuPress(option)}
                                >
                                    <View style={styles.menuItemIcon}>
                                        <Icon 
                                            name={option.icon} 
                                            type={option.type} 
                                            color={colors.BLACK} 
                                            size={26}
                                        />
                                    </View>
                                    <View style={styles.menuItemContent}>
                                        {loading && option.navigationName === 'Logout' ?
                                            <Text style={styles.menuItemText}>Loading...</Text>
                                            : <Text style={styles.menuItemText}>{option.name}</Text>
                                        }
                                    </View>
                                    <View style={styles.menuItemArrow}>
                                        <MaterialIcons 
                                            name={isRTL ? "keyboard-arrow-left" : "keyboard-arrow-right"} 
                                            size={30} 
                                            color={MAIN_COLOR} 
                                        />
                                    </View>
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </TouchableOpacity>
            </Modal>
        </>
    );
}

const styles = StyleSheet.create({
    menuButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 50 : 30,
        left: 0,
        zIndex: 1000,
        // backgroundColor: 'rgba(255, 255, 255, 0.2)',
        // borderRadius: 25,
        width: 50,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
        // borderWidth: 1,
        // borderColor: 'rgba(255, 255, 255, 0.3)',
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 2,
        // },
        // shadowOpacity: 0.25,
        // shadowRadius: 3.84,
        // elevation: 5,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    menuContainer: {
        width: width * 0.7,
        height: height,
        backgroundColor: colors.WHITE,
        paddingTop: Platform.OS === 'ios' ? 50 : 30,
    },
    menuHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderBottomColor: colors.BORDER_BACKGROUND,
    },
    menuTitle: {
        fontSize: 20,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderBottomWidth: 0.5,
        borderBottomColor: colors.BORDER_BACKGROUND,
        height: 60,
    },
    menuItemIcon: {
        padding: 6,
        marginHorizontal: 5,
        backgroundColor: SECONDORY_COLOR,
        borderRadius: 10,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
        width: 50,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    menuItemContent: {
        flex: 1,
        height: '100%',
        justifyContent: 'center',
    },
    menuItemArrow: {
        height: '100%',
        width: 50,
        alignItems: 'center',
        justifyContent: 'center',
    },
    menuItemText: {
        color: colors.BLACK,
        fontFamily: fonts.Regular,
        fontSize: 16,
    },
}); 