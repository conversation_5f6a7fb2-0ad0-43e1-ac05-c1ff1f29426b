import { AppState } from 'react-native';
import { store } from 'common';

class AppStateManager {
  constructor() {
    this.currentState = AppState.currentState;
    this.subscription = null;
    this.isInitialized = false;
    this.foregroundHandlers = [];
    this.backgroundHandlers = [];
    this.initialize();
  }

  initialize() {
    if (this.isInitialized) return;
    
    this.subscription = AppState.addEventListener('change', (nextAppState) => {
      console.log(`[AppStateManager] State Change: ${this.currentState} → ${nextAppState}`);
      
      if (this.currentState.match(/inactive|background/) && nextAppState === 'active') {
        this.handleForeground();
      } else if (this.currentState === 'active' && nextAppState.match(/inactive|background/)) {
        this.handleBackground();
      }
      
      this.currentState = nextAppState;
    });
    
    this.isInitialized = true;
    console.log('[AppStateManager] Initialized');
  }

  handleForeground() {
    console.log('[AppStateManager] App returned to foreground');
    
    // Delay to ensure app is fully active
    setTimeout(() => {
      try {
        // Execute all foreground handlers
        this.foregroundHandlers.forEach(handler => {
          try {
            handler();
          } catch (error) {
            console.error('[AppStateManager] Foreground handler error:', error);
          }
        });
        
        // Check and recover app state
        this.recoverAppState();
      } catch (error) {
        console.error('[AppStateManager] Error handling foreground:', error);
      }
    }, 1000);
  }

  handleBackground() {
    console.log('[AppStateManager] App going to background');
    
    // Execute all background handlers
    this.backgroundHandlers.forEach(handler => {
      try {
        handler();
      } catch (error) {
        console.error('[AppStateManager] Background handler error:', error);
      }
    });
  }

  async recoverAppState() {
    try {
      const state = store.getState();
      const auth = state.auth;
      
      // Recover driver state if needed
      if (auth.profile?.usertype === 'driver' && auth.profile?.driverActiveStatus) {
        console.log('[AppStateManager] Recovering driver state');
        
        // Check if location tracking is active
        const gps = state.gpsdata;
        if (gps.error) {
          console.log('[AppStateManager] GPS error detected, attempting recovery');
          // Dispatch a recovery action
          store.dispatch({
            type: 'RECOVER_GPS_STATE',
            payload: { timestamp: Date.now() }
          });
        }
        
        // Check if there are active bookings
        const activeBooking = state.bookinglistdata?.tracked;
        if (activeBooking) {
          console.log('[AppStateManager] Active booking found, ensuring proper state');
          // You can add specific recovery logic here
        }
      }
    } catch (error) {
      console.error('[AppStateManager] Error recovering app state:', error);
    }
  }

  addForegroundHandler(handler) {
    this.foregroundHandlers.push(handler);
  }

  addBackgroundHandler(handler) {
    this.backgroundHandlers.push(handler);
  }

  removeForegroundHandler(handler) {
    const index = this.foregroundHandlers.indexOf(handler);
    if (index > -1) {
      this.foregroundHandlers.splice(index, 1);
    }
  }

  removeBackgroundHandler(handler) {
    const index = this.backgroundHandlers.indexOf(handler);
    if (index > -1) {
      this.backgroundHandlers.splice(index, 1);
    }
  }

  cleanup() {
    if (this.subscription) {
      this.subscription.remove();
      this.subscription = null;
    }
    this.foregroundHandlers = [];
    this.backgroundHandlers = [];
    this.isInitialized = false;
    console.log('[AppStateManager] Cleaned up');
  }

  getCurrentState() {
    return this.currentState;
  }

  isActive() {
    return this.currentState === 'active';
  }

  isBackground() {
    return this.currentState === 'background';
  }
}

// Create singleton instance
const appStateManager = new AppStateManager();

export default appStateManager;
