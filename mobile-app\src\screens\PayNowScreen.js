import { Ionicons } from '@expo/vector-icons';
import i18n from 'i18n-js';
import moment from 'moment/min/moment-with-locales';
import React, { useEffect, useState } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { fetchBookings } from '../../../common/src/actions/bookinglistactions'; // adjust path as needed
import { fonts } from '../common/font';
import { MAIN_COLOR } from '../common/sharedFunctions';
import { colors } from '../common/theme';

export default function PayNowScreen({ navigation }) {
    const { t } = i18n;
    const auth = useSelector(state => state.auth);
    const dispatch = useDispatch();
    const bookinglistdata = useSelector(state => state.bookinglistdata);
    const [loading, setLoading] = useState(false);
    const [unpaidRides, setUnpaidRides] = useState([]);

    useEffect(() => {
        dispatch(fetchBookings());
    }, [dispatch]);

    useEffect(() => {
        if (bookinglistdata && bookinglistdata.bookings) {
            const unpaid = bookinglistdata.bookings.filter(
                ride => ride.status === 'STARTED'  && ride.payment_mode === 'cash'
            );
            setUnpaidRides(unpaid);
        }
    }, [bookinglistdata]);

    const handleRidePayment = (ride) => {
        if (!ride) return;

        // Create a booking object that matches what PaymentDetails expects
        const booking = {
            id: ride.id,
            trip_cost: ride.trip_cost || 0,
            pickup: ride.pickup || { add: '' },
            drop: ride.drop || { add: '' },
            trip_start_time: ride.trip_start_time || '',
            startTime: ride.startTime || new Date(),
            status: ride.status || 'PAYMENT_PENDING',
            payment_mode: 'card',
            discount: 0,
            promo_applied: false,
            promo_details: null,
            payableAmount: ride.trip_cost || 0
        };

        navigation.navigate('PaymentScreen', {
            booking: booking
        });
    };

    const renderRideItem = ({ item }) => (
        <TouchableOpacity 
            style={styles.rideItem}
            onPress={() => handleRidePayment(item)}
        >
            <View style={styles.rideHeader}>
                <View style={styles.locationContainer}>
                    <View style={styles.locationRow}>
                        <Ionicons name="location-outline" size={24} color={colors.BALANCE_GREEN} />
                        <Text style={styles.locationText}>{item.pickup?.add || ''}</Text>
                    </View>
                    <View style={styles.locationRow}>
                        <Ionicons name="location-outline" size={24} color={colors.BUTTON_ORANGE} />
                        <Text style={styles.locationText}>{item.drop?.add || ''}</Text>
                    </View>
                </View>
                <View style={styles.rideDetails}>
                    <View style={styles.timeContainer}>
                        <Text style={styles.timeText}>
                            {item.trip_start_time || ''}
                        </Text>
                        <Text style={styles.dateText}>
                            {item.startTime ? moment(item.startTime).format('ll') : ''}
                        </Text>
                    </View>
                    <View style={styles.amountContainer}>
                        <Text style={styles.amountText}>
                            ${Number(item.trip_cost || 0).toFixed(2)}
                        </Text>
                        <Text style={styles.paymentStatus}>
                            {'Payment pending'}
                        </Text>
                    </View>
                </View>
            </View>
            <TouchableOpacity 
                style={styles.payButton}
                onPress={() => handleRidePayment(item)}
            >
                <Text style={styles.payButtonText}>{'Pay Now'}</Text>
            </TouchableOpacity>
        </TouchableOpacity>
    );

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.headerTitle}>{'Unpaid Rides'}</Text>
            </View>
            
            <FlatList
                data={unpaidRides}
                renderItem={renderRideItem}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.listContainer}
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>{'no_unpaid_rides'}</Text>
                    </View>
                }
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    header: {
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: colors.GREY,
    },
    headerTitle: {
        fontSize: 24,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    listContainer: {
        padding: 15,
    },
    rideItem: {
        backgroundColor: colors.WHITE,
        borderRadius: 10,
        padding: 15,
        marginBottom: 10,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    rideHeader: {
        marginBottom: 10,
    },
    locationContainer: {
        marginBottom: 10,
    },
    locationRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 5,
    },
    locationText: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginLeft: 10,
    },
    rideDetails: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    timeContainer: {
        alignItems: 'flex-start',
    },
    timeText: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    dateText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    amountContainer: {
        alignItems: 'flex-end',
    },
    amountText: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: MAIN_COLOR,
    },
    paymentStatus: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BUTTON_ORANGE,
    },
    payButton: {
        backgroundColor: MAIN_COLOR,
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
    },
    payButtonText: {
        color: colors.WHITE,
        fontSize: 16,
        fontFamily: fonts.Bold,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 50,
    },
    emptyText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        textAlign: 'center',
    },
});
