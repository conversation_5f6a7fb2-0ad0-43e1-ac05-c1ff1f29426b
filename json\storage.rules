rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /cars/{id} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /cartypes/{id} {
      allow read;
      allow write: if request.auth != null;
    }
    match /users/{uid}/profileImage {
      allow read: if request.auth != null;
      allow write;
    }
    match /users/{uid}/license {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /users/{uid}/licenseBack {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /bookings/{bookingId}/pickup_image {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /bookings/{bookingId}/deliver_image {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /users/{uid}/verifyIdImage {
      allow read: if request.auth != null;
       allow write: if request.auth != null;
    } 
    match /users/{uid}/carInsuranceImage {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /users/{uid}/RegistrationImage {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /users/{uid}/BackgroundcheckImage {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    match /users/{uid}/SafetylineinspectionImage {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
  }
}    
