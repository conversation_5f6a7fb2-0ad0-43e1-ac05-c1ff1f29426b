import { Box, Grid, Typography } from '@mui/material';
import { FONT_FAMILY } from 'common/sharedFunctions';
import { useTranslation } from "react-i18next";
import { useSelector } from 'react-redux';
import HandsOnMobile from '../assets/img/handsonmobile.jpg';
import useStyles from '../styles/styles';

const DownloadApp = () => {
  const classes = useStyles();
  const settings = useSelector(state => state.settingsdata.settings);
  const { t } = useTranslation();

  return (
    <Box className={classes.aboutUsContainer} style={{marginBottom: -15}}>
      <Grid container spacing={3} className={classes.gridContainer}>
        <Grid item xs={12} md={5}>
          <img src={HandsOnMobile} alt="My Team" className={classes.largeImage} />
        </Grid>

        <Grid item xs={12} md={6}>
          {/* <Typography variant="h3" fontWeight={700} fontFamily={FONT_FAMILY} className={classes.title}> */}
          {/* {t('mobile_apps_on_store')} */}
          {/* {t('Mobile app available soon...')}
          </Typography> */}
          {/* <Typography fontFamily={FONT_FAMILY} className={classes.aboutUsSubtitle}>
          {t('app_store_deception1')}
          </Typography> */}
          {settings && settings.AppleStoreLink?
            <a
              href="https://apps.apple.com/us/app/taxi2fly/id6740841450?platform=iphone"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src={require("../assets/img/appstore.png").default} alt="Apple Store Link"/>
            </a>
            :null}
            <span style={{marginRight: '5px'}}></span>
            {settings && settings.PlayStoreLink?
            <a
              href="https://play.google.com/store/apps/details?id=com.taxibook.app&pcampaignid=web_share"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src={require("../assets/img/playstore.png").default} alt="Playstore Link"/>
            </a>
            :null}
        </Grid>
      </Grid>
    </Box>
  );
};

export default DownloadApp;