import { useNavigation } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { Animated, Dimensions, Image, ImageBackground, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { fonts } from '../common/font';
import { colors } from '../common/theme';
import { CommonMenuBar } from '../components';

var { width, height } = Dimensions.get('window');

export default function HomeScreen() {
    const navigation = useNavigation();
    const [displayText, setDisplayText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isDeleting, setIsDeleting] = useState(false);
    const text = "Your Ride, Your Way";
    const [carAnim] = useState(new Animated.Value(-width));
    
    // Animation value for the black overlay using useRef
    // const overlayOpacity = useRef(new Animated.Value(0)).current;

    // useEffect(() => {
    //     let animationTimeout;
        
    //     const startOverlayAnimation = () => {
    //         Animated.sequence([
    //             // Apply black overlay slowly
    //             Animated.timing(overlayOpacity, {
    //                 toValue: 1,
    //                 duration: 2000,
    //                 useNativeDriver: true,
    //             }),
    //             // Remove black overlay slowly
    //             Animated.timing(overlayOpacity, {
    //                 toValue: 0,
    //                 duration: 2000,
    //                 useNativeDriver: true,
    //             }),
    //         ]).start(() => {
    //             // Repeat the animation after a delay
    //             animationTimeout = setTimeout(startOverlayAnimation, 2000);
    //         });
    //     };

    //     startOverlayAnimation();

    //     // Cleanup function
    //     return () => {
    //         if (animationTimeout) {
    //             clearTimeout(animationTimeout);
    //         }
    //     };
    // }, [overlayOpacity]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            if (!isDeleting) {
                if (currentIndex < text.length) {
                    setDisplayText(prev => prev + text[currentIndex]);
                    setCurrentIndex(prev => prev + 1);
                } else {
                    // Start deleting after a pause
                    setTimeout(() => setIsDeleting(true), 1000);
                }
            } else {
                if (currentIndex > 0) {
                    setDisplayText(prev => prev.slice(0, -1));
                    setCurrentIndex(prev => prev - 1);
                } else {
                    // Start typing again after a pause
                    setIsDeleting(false);
                    setTimeout(() => {}, 500);
                }
            }
        }, isDeleting ? 100 : 200); // Faster deletion, slower typing

        Animated.timing(carAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
        }).start();

        return () => {
            clearTimeout(timeout);
        };
    }, [currentIndex, isDeleting, carAnim]);

    const handleBookNow = () => {
        navigation.navigate('Reserve');
    };

    return ( 
            <View style={styles.container}>
            <ImageBackground
            source={require('../../assets/images/homepageBgImg.png')}
            style={styles.bgImage}
            resizeMode="cover"
        >
                {/* Header */}
                <View style={styles.header}>
                    {/* Left: Hamburger Menu */}
                    <CommonMenuBar />
                    {/* Center: Logo */}
                    <Image
                        source={require('../../assets/images/logo.png')}
                        style={styles.headerImage}
                        resizeMode="contain"
                    />
                    {/* Right: Country */}
                    <Text style={styles.countryText}>Chicago</Text>
                </View>

                {/* Main Content */}
                <View style={styles.mainContent}>
                    {/* Car Image with animation */}
                    <Animated.Image
                        source={require('../../assets/images/top_view_car.png')}
                        style={[
                            styles.carImage,
                            { transform: [{ translateX: carAnim }] }
                        ]}
                        resizeMode="contain"
                    />
                    {/* Big TAXI Text */}
                    <Text style={styles.taxiText}>{displayText}</Text>
                    {/* Subtitle */}
                    <Text style={styles.subtitle}> 
                        Book your ride instantly with{'\n'}comfort and safety.
                    </Text>
                </View>

                {/* Book Button */}
                <View style={styles.buttonContainer}>
                    <TouchableOpacity style={styles.bookButton} onPress={handleBookNow}>
                        <Text style={styles.bookButtonText}>Reserve</Text>
                    </TouchableOpacity>
                </View>
            </ImageBackground>
            </View> 
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
        width: '100%',
        height: '100%',
    },
    imagebg: {
        flex: 1,
        width: width,
        height: height,
    },
    centerContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    bookNowButton: {
        backgroundColor: 'transparent',
        width: 200,
        height: 50,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.WHITE,
    },
    buttonTitleStyle: {
        fontFamily: fonts.Bold,
        fontSize: 14,
    },
    typingText: {
        color: colors.WHITE,
        fontSize: 32,
        fontFamily: fonts.Bold,
        marginBottom: 20,
        paddingHorizontal: 20,
        paddingVertical: 20,
        textAlign: 'center',
        textShadowColor: 'rgba(0, 0, 0, 0.75)',
        textShadowOffset: { width: -1, height: 1 },
        textShadowRadius: 10
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'black',
        pointerEvents: 'none',
        zIndex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 24,
        paddingTop: Platform.OS === 'ios' ? 50 : 30,
        backgroundColor: 'transparent',
    },
    menuIcon: {
        width: 28,
        height: 28,
    },
    headerImage: {
        marginLeft:20,
        width: 50,
        height: 50,
    },
    countryText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        fontFamily: 'DancingScript-Bold',
    },
    mainContent: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    carImage: {
        width: 250,
        height: 130,
        marginBottom: 20,
    },
    taxiText: {
        fontSize: 28,
        fontFamily: 'DancingScript-Bold',
        color: '#333',
        letterSpacing: 4,
        marginBottom: 10,
    },
    subtitle: {
        fontSize: 20,
        fontFamily: 'DancingScript-Bold',
        color: '#444',
        textAlign: 'center',
        marginBottom: 30,
    },
    buttonContainer: {
        alignItems: 'center',
        marginBottom: 80,
        position: 'absolute',
        bottom: 120,
        left: 0,
        right: 0,
        zIndex: 10,
    },
    bookButton: {
        backgroundColor: '#222',
        width: 120,
        height: 120,
        borderRadius: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 4,
        borderColor: '#fff',
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 6,
    },
    bookButtonText: {
        color: '#fff',
        fontSize: 20,
        fontFamily: 'DancingScript-Bold',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    bgImage: {
        flex: 1,
        width: '100%',
        height: '100%',
        justifyContent: 'flex-start',
        backgroundColor: colors.WHITE,
    },
}); 