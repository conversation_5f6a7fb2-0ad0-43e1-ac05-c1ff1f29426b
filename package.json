{"name": "exicubeapps", "version": "4.3.0", "description": "Exicube App Solutions", "scripts": {"app": "yarn workspace mobile-app start", "app:publish": "cd mobile-app && eas update", "app:build-ios": "cd mobile-app && eas build --platform ios --profile production", "app:build-android": "cd mobile-app && eas build --platform android --profile production", "app:build-android-apk": "cd mobile-app && eas build --platform android --profile preview", "app:build-ios-sim": "cd mobile-app && eas build -p ios --profile simulator", "app:build-dev-client": "cd mobile-app && eas build --profile development", "app:submit": "cd mobile-app && eas submit", "web": "yarn workspace web-app start", "web:build": "yarn workspace web-app build", "web:deploy": "yarn workspace web-app build && firebase deploy --only hosting", "deploy": "yarn workspace web-app build && firebase deploy && exicube initialize", "postinstall": "patch-package && exicube install"}, "workspaces": ["mobile-app", "web-app", "functions", "common"], "author": "Exicube App Solutions (OPC) Private Limited", "private": true, "dependencies": {"@expo/metro-config": "~0.18.11", "concurrently": "7.0.0", "crypto-js": "^4.1.1", "exicube-cli": "1.9.0", "expo-apple-authentication": "~6.4.2", "expo-asset": "~10.0.10", "expo-build-properties": "^0.12.5", "expo-dev-client": "~4.0.29", "expo-font": "~12.0.10", "expo-image-picker": "~15.1.0", "expo-notifications": "^0.28.19", "expo-splash-screen": "^0.27.7", "expo-updates": "^0.25.27", "firebase-tools": "^13.0.1", "fs-extra": "10.0.0", "node-fetch": "2.6.7", "open": "^8.4.0", "patch-package": "6.4.7", "react-native": "0.74.5", "react-native-safe-area-context": "4.10.5", "xml2json": "^0.12.0", "@expo/metro-runtime": "~3.2.3", "@react-native-community/cli": "^x.x.x"}, "resolutions": {"@expo/config-plugins": "8.0.4", "react-native-safe-area-context": "4.10.5", "@expo/metro-config": "~0.18.11"}}