#!/usr/bin/env node

/**
 * Complete build fix script for Kotlin compilation and 16KB support
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Starting complete build fix...');

function runCommand(command, description, options = {}) {
  console.log(`📋 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit', 
      cwd: process.cwd(),
      ...options 
    });
    console.log(`✅ ${description} completed`);
    return result;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    if (!options.continueOnError) {
      throw error;
    }
  }
}

function cleanBuildArtifacts() {
  console.log('🧹 Cleaning build artifacts...');
  
  const pathsToClean = [
    'node_modules',
    '.expo',
    'android',
    'ios',
    '.gradle'
  ];
  
  pathsToClean.forEach(pathToClean => {
    const fullPath = path.join(process.cwd(), pathToClean);
    if (fs.existsSync(fullPath)) {
      console.log(`🗑️ Removing ${pathToClean}...`);
      try {
        fs.rmSync(fullPath, { recursive: true, force: true });
      } catch (error) {
        console.warn(`⚠️ Could not remove ${pathToClean}:`, error.message);
      }
    }
  });
}

function updatePackageJson() {
  console.log('📦 Checking package.json...');
  const packagePath = path.join(process.cwd(), 'package.json');
  
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Ensure compatible versions
    const updates = {
      "expo": "~51.0.14",
      "expo-build-properties": "~0.12.3"
    };
    
    let hasUpdates = false;
    Object.entries(updates).forEach(([pkg, version]) => {
      if (packageJson.dependencies[pkg] !== version) {
        console.log(`📝 Updating ${pkg} to ${version}`);
        packageJson.dependencies[pkg] = version;
        hasUpdates = true;
      }
    });
    
    if (hasUpdates) {
      fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
      console.log('✅ Package.json updated');
    }
  }
}

function main() {
  try {
    console.log('🚀 Starting complete build fix process...');
    
    // Step 1: Clean everything
    cleanBuildArtifacts();
    
    // Step 2: Update package.json if needed
    updatePackageJson();
    
    // Step 3: Install dependencies
    runCommand('npm install', 'Installing dependencies');
    
    // Step 4: Fix Expo dependencies
    runCommand('npx expo install --fix', 'Fixing Expo dependencies');
    
    // Step 5: Clear Expo cache
    runCommand('npx expo r -c', 'Clearing Expo cache', { continueOnError: true });
    
    // Step 6: Prebuild with clean
    runCommand('npx expo prebuild --clean --platform android', 'Prebuilding Android');
    
    console.log('🎉 Build fix completed successfully!');
    console.log('📱 Next steps:');
    console.log('1. Try building: eas build --platform android --profile production');
    console.log('2. If still failing, try: eas build --platform android --profile production --clear-cache');
    console.log('3. For 16KB support, we\'ll add it after basic build works');
    
  } catch (error) {
    console.error('❌ Build fix failed:', error.message);
    console.log('\n🔍 Troubleshooting suggestions:');
    console.log('1. Check EAS CLI version: npx eas-cli@latest');
    console.log('2. Try building with lower Android target (34 instead of 35)');
    console.log('3. Check for any custom native code conflicts');
    console.log('4. Verify all dependencies are compatible with Expo 51');
    process.exit(1);
  }
}

main();
