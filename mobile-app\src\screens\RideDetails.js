import { <PERSON>t<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ontisto, Ionicons, MaterialCommunityIcons, Octicons } from '@expo/vector-icons';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import i18n from 'i18n-js';
import moment from 'moment/min/moment-with-locales';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    Image,
    Linking,
    Modal,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { Avatar } from 'react-native-elements';
import MapView, { Marker, Polyline, PROVIDER_GOOGLE } from 'react-native-maps';
import StarRating from 'react-native-star-rating-widget';
import { useSelector } from 'react-redux';
import { fonts } from '../common/font';
import { MAIN_COLOR } from '../common/sharedFunctions';
import { colors } from '../common/theme';
import Button from '../components/Button';
var { width, height } = Dimensions.get('window');
// import { MaterialCommunityIcons } from '@expo/vector-icons';
export default function RideDetails(props) {

    const { data, fromPutOutTab } = props.route.params;
    const paramData = data;
    const settings = useSelector(state => state.settingsdata.settings);
    const auth = useSelector(state => state.auth);
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    //const isRTL = true;
    const [coords, setCoords] = useState([]);
    const [role, setRole] = useState();
    const [userInfoModalStatus, setUserInfoModalStatus] = useState(false);

    const goToBooking = (id) => {
        if (paramData.status == 'PAYMENT_PENDING') {
            props.navigation.navigate('PaymentDetails', { booking: paramData });
        } else {
            props.navigation.replace('BookedCab', { bookingId: id });
        }
    };

    useEffect(() => {
        console.log('fromPutOutTab', fromPutOutTab);
        if (auth.profile && auth.profile.usertype) {
            setRole(auth.profile.usertype);
        } else {
            setRole(null);
        }
    }, [auth.profile]);

    const onPressCall = (phoneNumber) => {
        if (['PAYMENT_PENDING', 'NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(paramData.status) != -1) {
            let call_link = Platform.OS == 'android' ? 'tel:' + phoneNumber : 'telprompt:' + phoneNumber;
            Linking.openURL(call_link);
        } else {
            Alert.alert(t('alert'), t('booking_is') + paramData.status + "." + t('not_call'));
        }
    }

    const onChatAction = () => {
        if (['PAYMENT_PENDING', 'NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(paramData.status) != -1) {
            props.navigation.navigate("onlineChat", { bookingId: paramData.id })
        } else {
            Alert.alert(t('alert'), t('booking_is') + paramData.status + "." + t('not_chat'));
        }
    }

    const onAlert = (item) => {
        Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_call'));
    }

    const onChatAlert = (item) => {
        Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_chat'));
    }

    const UserInfoModal = () => {
        return (
            <Modal
                animationType="fade"
                transparent={true}
                visible={userInfoModalStatus}
                onRequestClose={() => {
                    setUserInfoModalStatus(false);
                }}
            >
                <View style={styles.centeredView}>
                    <View style={styles.modalView}>
                        <View style={{ width: '100%' }}>
                            {paramData && paramData.deliveryPersonPhone ?
                                <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                    <Text style={styles.textStyleBold}>{t('senderPersonPhone')}</Text>
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 10 }}>
                                        <Ionicons name="call" size={24} color={colors.BLACK} />
                                        <Text style={styles.textStyle} onPress={() => onPressCall(paramData.deliveryPersonPhone)}> {paramData.deliveryPersonPhone} </Text>
                                    </View>
                                </View>
                                : null}
                            {paramData && paramData.customer_contact ?
                                <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                    <Text style={styles.textStyleBold}>{t('senderPersonPhone')}</Text>
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 10 }}>
                                        <Ionicons name="call" size={24} color={colors.BLACK} />
                                        <Text style={styles.textStyle} onPress={() => onPressCall(paramData.customer_contact)}> {paramData.customer_contact} </Text>
                                    </View>
                                </View>
                                : null}
                        </View>
                        <TouchableOpacity
                            loading={false}
                            onPress={() => setUserInfoModalStatus(false)}
                            style={styles.modalBtn}
                        >
                            <Text style={styles.textStyle}>{t('ok')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

        )
    }


    useEffect(() => {
        let arr = [];
        arr.push(paramData.coords[0]);
        if (paramData && paramData.waypoints) {
            for (let i = 0; i < paramData.waypoints.length; i++) {
                arr.push({ latitude: paramData.waypoints[i].lat, longitude: paramData.waypoints[i].lng })
            }
        }
        arr.push(paramData.coords[1]);
        setCoords(arr);
    }, []);

    const downloadReceipt = async () => {
        // Function to get short address - only shorten airport names
        const getShortAddress = (fullAddress) => {
            if (!fullAddress) return 'N/A';
            
            // Check for common airport names and shorten them
            const airportMappings = {
                "O'Hare": "O'Hare",
                "O'Hare International Airport": "O'Hare",
                "Chicago O'Hare International Airport": "O'Hare",
                "Midway": "Midway", 
                "Midway International Airport": "Midway",
                "Chicago Midway International Airport": "Midway",
                "ORD": "O'Hare",
                "MDW": "Midway"
            };
            
            // Check if the address contains any airport names
            for (const [fullName, shortName] of Object.entries(airportMappings)) {
                if (fullAddress.toLowerCase().includes(fullName.toLowerCase())) {
                    return shortName;
                }
            }
            
            // If no airport found, return the original address
            return fullAddress;
        };

        // Function to get trip type description
        const getTripType = () => {
            // Check if it's a round trip based on waypoints or trip type
            if (paramData.waypoints && paramData.waypoints.length > 0) {
                return "Round Trip";
            }
            // You can also check for a specific trip type field if available
            if (paramData.trip_type) {
                return paramData.trip_type === 'round' ? "Round Trip" : "One Way Trip";
            }
            // Default to One Way Trip
            return "One Way Trip";
        };

        // Helper: Should we include tips in the calculation?
        const shouldIncludeTips = () => {
            // Do not include tips if status is PENDING
            return !(paramData.status && paramData.status.toUpperCase() === 'PENDING');
        };

        // Calculate price/amount for receipt
        const getReceiptAmount = () => {
            if (paramData.trip_cost) {
                if (shouldIncludeTips()) {
                    // For complete rides, subtract tips from trip cost
                    if (paramData.status && paramData.status.toUpperCase() === 'COMPLETE') {
                        return (parseFloat(paramData.trip_cost) - parseFloat(paramData.tips || 0)).toFixed(settings.decimal);
                    } else {
                        return (parseFloat(paramData.trip_cost) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal);
                    }
                } else {
                    return parseFloat(paramData.trip_cost).toFixed(settings.decimal);
                }
            } else {
                if (shouldIncludeTips()) {
                    return (paramData.tips || 0).toFixed(settings.decimal);
                } else {
                    return "0.00";
                }
            }
        };

        // Calculate tips for receipt
        const getReceiptTips = () => {
            if (shouldIncludeTips()) {
                return paramData.tips && parseFloat(paramData.tips) > 0
                    ? `${settings.symbol} ${(parseFloat(paramData.tips)).toFixed(settings.decimal)}`
                    : '';
            } else {
                return '';
            }
        };

        const html = `
<html>
<head>
  <meta charset="utf-8" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Arial', sans-serif;
      background: #f5f5f5;
      color: #333;
      line-height: 1.4;
    }
    
    .receipt-container {
      max-width: 800px;
      margin: 20px auto;
      background: white;
      display: flex;
      min-height: 600px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .left-panel {
      width: 30%;
      background: #FFD700;
      padding: 30px 25px;
      position: relative;
      overflow: hidden;
    }
    
    .left-panel::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        radial-gradient(circle at 20px 20px, rgba(0,0,0,0.1) 2px, transparent 2px),
        radial-gradient(circle at 60px 40px, rgba(0,0,0,0.1) 1.5px, transparent 1.5px),
        radial-gradient(circle at 40px 80px, rgba(0,0,0,0.1) 1px, transparent 1px);
      background-size: 80px 80px, 120px 120px, 60px 60px;
      opacity: 0.3;
    }
    
    .right-panel {
      width: 70%;
      background: white;
      padding: 30px 25px;
      position: relative;
    }
    
    .right-panel::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background-image: 
        radial-gradient(circle at 20px 20px, #000 1px, transparent 1px),
        radial-gradient(circle at 40px 40px, #000 1px, transparent 1px);
      background-size: 40px 40px;
      opacity: 0.1;
    }
    
    .right-panel::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 80px;
      height: 80px;
      background-image: 
        radial-gradient(circle at 15px 15px, #000 1px, transparent 1px),
        radial-gradient(circle at 35px 35px, #000 1px, transparent 1px);
      background-size: 30px 30px;
      opacity: 0.1;
    }
    
    .logo-section {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      position: relative;
      z-index: 1;
    }
    
    .logo-circle {
      width: 60px;
      height: 60px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      border: 2px solid #000;
    }
    
    .logo-image {
      width: 40px;
      height: 40px;
      object-fit: contain;
    }
    
    .company-name {
      font-size: 28px;
      font-weight: bold;
      color: #000;
      letter-spacing: 1px;
    }
    
    .company-name .highlight {
      background: #000;
      color: #FFD700;
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    .invoice-title {
      position: absolute;
      top: 30px;
      right: 25px;
      font-size: 24px;
      font-weight: bold;
      color: #000;
      z-index: 1;
    }
    
    .contact-section {
      margin-bottom: 30px;
      position: relative;
      z-index: 1;
    }
    
    .section-label {
      font-size: 14px;
      font-weight: bold;
      color: #000;
      margin-bottom: 10px;
      text-transform: uppercase;
    }
    
    .contact-info {
      font-size: 12px;
      color: #000;
      margin-bottom: 5px;
    }
    
    .invoice-details {
      position: relative;
      z-index: 1;
    }
    
    .detail-row {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .detail-label {
      font-size: 12px;
      color: #FFD700;
      font-weight: bold;
      width: 80px;
      text-transform: uppercase;
    }
    
    .detail-value {
      background: #f0f0f0;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      color: #333;
      font-weight: 600;
      min-width: 60px;
      text-align: center;
    }
    
    .date-boxes {
      display: flex;
      gap: 5px;
    }
    
    .date-box {
      background: #f0f0f0;
      padding: 8px 10px;
      border-radius: 4px;
      font-size: 12px;
      color: #333;
      font-weight: 600;
      min-width: 40px;
      text-align: center;
    }
    
    .charges-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      position: relative;
      z-index: 1;
    }
    
    .charges-table th {
      background: #FFD700;
      color: #000;
      padding: 12px 8px;
      font-size: 12px;
      font-weight: bold;
      text-align: left;
      border-bottom: 2px solid #000;
    }
    
    .charges-table td {
      padding: 12px 8px;
      font-size: 12px;
      border-bottom: 1px solid #ddd;
    }
    
    .summary-section {
      margin-top: 20px;
      position: relative;
      z-index: 1;
    }
    
    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .summary-label {
      font-size: 12px;
      color: #000;
      font-weight: bold;
    }
    
    .summary-value {
      background: #f0f0f0;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      color: #333;
      font-weight: bold;
      min-width: 80px;
      text-align: center;
    }
    
    .total-row {
      border-top: 2px solid #000;
      padding-top: 10px;
      margin-top: 10px;
    }
    
    .total-row .summary-label,
    .total-row .summary-value {
      font-size: 14px;
      font-weight: bold;
    }
    
    .tips-row {
      margin-top: 15px;
    }
    
    .tips-row .summary-value {
      background: #f0f0f0;
      border: 1px dashed #ccc;
    }
    
    @media print {
      body {
        background: white;
      }
      .receipt-container {
        box-shadow: none;
        margin: 0;
      }
    }
  </style>
</head>
<body>
  <div class="receipt-container">
    <div class="left-panel">
      <div class="logo-section">
        <div class="logo-circle">
          <img src="https://taxi2fly.net/static/media/TAXI2FLY.df319997.svg" alt="TAXI2FLY Logo" class="logo-image" />
        </div>
        <div class="company-name">TAXI<span class="highlight">2</span>FLY</div>
      </div>
      
      <div class="contact-section">
        <div class="section-label">FROM</div>
        <div class="contact-info">+1 (669) 226-9975</div>
        <div class="contact-info"><EMAIL></div>
        <div class="contact-info">taxi2fly.net</div>
      </div>
      
      <div class="contact-section">
        <div class="section-label">BILL TO</div>
        <div class="contact-info">${paramData.customer_name || 'N/A'}</div>
        <div class="contact-info">${paramData.customer_contact || 'N/A'}</div>
        <div class="contact-info">${paramData.customer_email || 'N/A'}</div>
      </div>
    </div>
    
    <div class="right-panel">
      <div class="invoice-title">INVOICE</div>
      
      <div class="invoice-details">
        <div class="detail-row">
          <div class="detail-label">Invoice #</div>
          <div class="detail-value">${paramData.reference || paramData.id}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">DATE</div>
          <div class="date-boxes">
            <div class="date-box">${moment(paramData.tripdate).format('MM')}</div>
            <div class="date-box">${moment(paramData.tripdate).format('DD')}</div>
            <div class="date-box">${moment(paramData.tripdate).format('YYYY')}</div>
          </div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">FROM</div>
          <div class="detail-value">${getShortAddress(paramData.pickup.add)}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">TO</div>
          <div class="detail-value">${getShortAddress(paramData.drop.add)}</div>
        </div>
      </div>
      
      <table class="charges-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Quantity</th>
            <th>PRICE</th>
            <th>AMOUNT</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>${getTripType()} To ${getShortAddress(paramData.drop.add)}</td>
            <td>01</td>
            <td>${settings.symbol} ${getReceiptAmount()}</td>
            <td>${settings.symbol} ${getReceiptAmount()}</td>
          </tr>
        </tbody>
      </table>
      
      <div class="summary-section">
        <div class="summary-row tips-row">
          <div class="summary-label">TIPS</div>
          <div class="summary-value">${getReceiptTips()}</div>
        </div>
        
        <div class="summary-row total-row">
          <div class="summary-label">TOTAL:</div>
          <div class="summary-value">${settings.symbol} ${paramData.customer_paid ? (parseFloat(paramData.customer_paid) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal) : parseFloat(paramData.tips || 0).toFixed(settings.decimal)}</div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
`;
 
        try {
            const { uri } = await Print.printToFileAsync({ html });
            await Sharing.shareAsync(uri, { UTI: '.pdf', mimeType: 'application/pdf' });
        } catch (error) {
            Alert.alert(
                t('download_receipt'),
                t('receipt_download_failed')
            );
        }
    };

    // Helper function to convert 24-hour time to 12-hour AM/PM format
    const convertToAMPM = (timeString) => {
        if (!timeString) return '';
        
        try {
            // Parse the time string (assuming format like "14:30" or "14:30:00")
            const [hours, minutes] = timeString.split(':');
            const hour = parseInt(hours, 10);
            const minute = parseInt(minutes, 10);
            
            // Convert to 12-hour format
            const period = hour >= 12 ? 'PM' : 'AM';
            const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
            const displayMinute = minute.toString().padStart(2, '0');
            
            return `${displayHour}:${displayMinute} ${period}`;
        } catch (error) {
            // If parsing fails, return the original string
            return timeString;
        }
    };

    return (
        <View style={[styles.mainView]}>
            <ScrollView>

                <View style={styles.vew}>

                    {/* Reference with copy option */}
                    {paramData.reference ? (
                        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', margin: 10 }}>
                            <Text style={{ fontFamily: fonts.Bold, fontSize: 16, color: colors.HEADER, marginRight: 8 }}>{t('booking_ref')}: {paramData.reference}</Text>
                            <TouchableOpacity
                                onPress={async () => {
                                    await Clipboard.setStringAsync(paramData.reference);
                                    Alert.alert(t('copied'), t('reference_copied'));
                                }}
                                style={{ padding: 4 }}
                            >
                            </TouchableOpacity>
                        </View>
                    ) : null}
                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1 }}>
                        <View style={{ flexDirection: 'column', width: width - 20 }}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                <View style={{ width: 30, alignItems: 'center' }}>
                                    <Ionicons name="location-outline" size={24} color={colors.GREEN} />
                                    <View style={[styles.hbox2, { flex: 1, minHeight: 5 }]} />
                                </View>
                                <View style={{ width: width - 70, marginBottom: 10 }}>
                                    <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{paramData.pickup.add} </Text>
                                </View>
                            </View>
                            {paramData && paramData.waypoints && paramData.waypoints.length > 0 ? paramData.waypoints.map((point, index) => {
                                return (
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                        <View style={{ width: 30, alignItems: 'center' }}>
                                            {/* <Ionicons name="location-outline" size={24} color={colors.BOX_BG} /> */}

                                            <View style={styles.multiAddressChar}>
                                                <Text style={{ fontWeight: 'bold', fontSize: 10, color: colors.BLACK }}>{String.fromCharCode(65 + index)}</Text>
                                            </View>

                                            <View style={[styles.hbox2, { flex: 1 }]} />
                                        </View>
                                        <View style={{ width: width - 70, marginBottom: 10 }}>
                                            <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{point.add}</Text>
                                        </View>
                                    </View>
                                )
                            })
                                : null}
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                <View style={{ width: 30, alignItems: 'center' }}>
                                    <Ionicons name="location-outline" size={24} color={colors.RED} />
                                </View>
                                <View style={{ width: width - 70 }}>
                                    <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{paramData.drop.add}</Text>
                                </View>
                            </View>
                        </View>
                    </View>

                    <View style={{ flexDirection: 'column', flex: 1, minHeight: 60 }}>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, minHeight: 60 }}>
                        <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Text style={{ fontFamily: fonts.Bold, fontSize: 24, color: MAIN_COLOR, opacity: 0.7 }}>{settings.symbol}</Text>
                                <Text style={styles.textStyleBold}>
                                    {paramData && paramData.trip_cost > 0 
                                        ? (
                                            paramData.status && paramData.status.toUpperCase() === 'PENDING'
                                                ? parseFloat(paramData.trip_cost).toFixed(settings.decimal)
                                                : paramData.status && paramData.status.toUpperCase() === 'COMPLETE'
                                                    ? (parseFloat(paramData.trip_cost) - parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                                    : (parseFloat(paramData.trip_cost) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                        )
                                        : paramData && paramData.estimate 
                                            ? (
                                                paramData.status && paramData.status.toUpperCase() === 'PENDING'
                                                    ? parseFloat(paramData.estimate).toFixed(settings.decimal)
                                                    : paramData.status && paramData.status.toUpperCase() === 'COMPLETE'
                                                        ? (parseFloat(paramData.estimate) - parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                                        : (parseFloat(paramData.estimate) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                            )
                                            : (
                                                paramData.status && paramData.status.toUpperCase() === 'PENDING'
                                                    ? "0.00"
                                                    : (paramData.tips || 0).toFixed(settings.decimal)
                                            )
                                    }
                                </Text>
                            </View>
                            <View style={[styles.hbox2, { minHeight: 5, width: 1, margin: 2 }]} />
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Fontisto name="map" size={26} color={MAIN_COLOR} style={{ opacity: 0.8 }} />
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <Text style={styles.textStyleBold}>{paramData && paramData.distance > 0 ? parseFloat(paramData.distance).toFixed(settings.decimal) : 0}</Text>
                                    <Text style={styles.textStyle}> {settings && settings.convert_to_mile ? t("mile") : t("km")} </Text>
                                </View>

                            </View>
                        </View>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, minHeight: 60 }}>
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row', borderBottomWidth: 0 }]}>
                                <Octicons name="clock" size={26} color={MAIN_COLOR} style={{ opacity: 0.8 }} />
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <Text style={styles.textStyleBold}>{(paramData && paramData.total_trip_time && paramData.total_trip_time > 0 ? parseFloat(paramData.total_trip_time / 60).toFixed(0) == 0 ? "1" : parseFloat(paramData.total_trip_time / 60).toFixed(0) : parseFloat(paramData.estimateTime / 60).toFixed(0))}</Text>
                                    <Text style={styles.textStyle}> {t("mins")} </Text>
                                </View>
                            </View>
                            <View style={[styles.hbox2, { minHeight: 5, width: 1, margin: 2 }]} />
                            <View style={[styles.clock, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                {paramData && paramData.trip_start_time && paramData.trip_end_time ?
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                        <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                            <Ionicons name="location-outline" size={28} color={colors.GREEN} />
                                            <View>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <Text style={styles.textStyleBold}>{convertToAMPM(paramData.trip_start_time)}</Text>
                                                </View>
                                                <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8, fontFamily: fonts.Regular }}>{paramData.startTime ? moment(paramData.startTime).format('ll') : ''}</Text>
                                            </View>
                                        </View>
                                        <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                            <Ionicons name="location-outline" size={28} color={colors.RED} />
                                            <View>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <Text style={styles.textStyleBold}>{convertToAMPM(paramData.trip_end_time)}</Text>
                                                </View>
                                                <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8, fontFamily: fonts.Regular }}>{paramData.endTime ? moment(paramData.endTime).format('ll') : ''}</Text>
                                            </View>
                                        </View>
                                    </View>
                                    : paramData && paramData.trip_start_time ?
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                            <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                                <Ionicons name="location-outline" size={28} color={colors.GREEN} />
                                                <View>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <Text style={styles.textStyleBold}>{convertToAMPM(paramData.trip_start_time)}</Text>
                                                    </View>
                                                    <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{paramData.startTime ? moment(paramData.startTime).format('ll') : ''}</Text>
                                                </View>
                                            </View>
                                            <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                                <Ionicons name="location-outline" size={28} color={colors.RED} />
                                                <Image source={require('../../assets/images/clock.gif')} style={{ width: 25, height: 25, alignSelf: 'center', resizeMode: 'center' }} />
                                            </View>
                                        </View>
                                        :
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                            {fromPutOutTab ? (
                                                <Text style={styles.textStyleBold}>
                                                    {
                                                        (() => {
                                                            const driverId = auth.profile && auth.profile.uid;
                                                            if (
                                                                paramData.putoutdrivers &&
                                                                Array.isArray(paramData.putoutdrivers) &&
                                                                driverId
                                                            ) {
                                                                const driverPutout = paramData.putoutdrivers.find(
                                                                    driver => driver.driver === driverId
                                                                );
                                                                return driverPutout && driverPutout.putout_res
                                                                    ? driverPutout.putout_res
                                                                    : t('putout_no_reason');
                                                            }
                                                            return t('putout_no_reason');
                                                        })()
                                                    }
                                                </Text>
                                            ) : (
                                                <Text style={styles.textStyleBold}>
                                                    {paramData && paramData.reason ? paramData.reason : t(paramData.status).toUpperCase()}
                                                </Text>
                                            )}
                                        </View>
                                }
                            </View>
                        </View>
                    </View>

                    <View>
                        {paramData && (paramData.flightNumber || paramData.returnFlightNumber || paramData.noteText) ? (
                            <View style={{ marginTop: 10 }}>
                                {paramData.flightNumber ? (
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginVertical: 4 }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>Flight:</Text>
                                        <Text style={[styles.textStyle, { marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0, textAlign: isRTL ? 'right' : 'left' }]}>{paramData.flightNumber}</Text>
                                    </View>
                                ) : null}
                                {/* {paramData.returnFlightNumber ? (
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginVertical: 4 }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>Return Flight:</Text>
                                        <Text style={[styles.textStyle, { marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0, textAlign: isRTL ? 'right' : 'left' }]}>{paramData.returnFlightNumber}</Text>
                                    </View>
                                ) : null} */}
                                {paramData.noteText ? (
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'flex-start', marginVertical: 4 }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>Note:</Text>
                                        <Text style={[styles.textStyle, { fontStyle: 'italic', color: '#666', marginLeft: isRTL ? 0 : 6, marginRight: isRTL ? 6 : 0, textAlign: isRTL ? 'right' : 'left' }]}>{paramData.noteText}</Text>
                                    </View>
                                ) : null}
                            </View>
                        ) : null}
                    </View>

                    {paramData ?
                        <View style={[styles.driverDetails, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', flex: 1 }}>
                                {paramData ?
                                    (!(paramData.driver_image == '' || paramData.driver_image == null || paramData.driver_image == 'undefined') && auth.profile.usertype == 'customer') ?
                                        <Avatar
                                            size="medium"
                                            rounded
                                            source={{ uri: paramData.driver_image }}
                                            activeOpacity={0.7}
                                        />
                                        :
                                        (!(paramData.customer_image == '' || paramData.customer_image == null || paramData.customer_image == 'undefined') && auth.profile.usertype == 'driver') ?
                                            <Avatar
                                                size="medium"
                                                rounded
                                                source={{ uri: paramData.customer_image }}
                                                activeOpacity={0.7}
                                            />
                                            : paramData.driver_name != '' ?

                                                <Avatar
                                                    size="medium"
                                                    rounded
                                                    source={require('../../assets/images/profilePic.png')}
                                                    activeOpacity={0.7}
                                                /> : null
                                    : null}
                                <View style={[styles.userView, { flex: 1, marginHorizontal: 5 }]}>
                                    {paramData && paramData.driver_name != '' && auth.profile.usertype == 'customer' ? <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{paramData.driver_name ? paramData.driver_name : t('no_name')}</Text> : null}

                                    {paramData && paramData.customer_name != '' && auth.profile.usertype == 'driver' ? <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{paramData.customer_name ? paramData.customer_name : t('no_name')}</Text> : null}

                                    {paramData && paramData.rating > 0 && paramData.driver_name && auth.profile.usertype == 'customer' ?
                                        <View>
                                            <StarRating
                                                maxStars={5}
                                                starSize={15}
                                                enableHalfStar={true}
                                                color={MAIN_COLOR}
                                                emptyColor={MAIN_COLOR}
                                                rating={paramData && paramData.rating ? parseFloat(paramData.rating) : 0}
                                                style={[isRTL ? { marginRight: 0, transform: [{ scaleX: -1 }] } : { marginLeft: -8 }]}
                                                onChange={() => {
                                                    //console.log('hello')
                                                }}
                                            />
                                        </View>
                                        : null}
                                </View>
                            </View>
                            {paramData && ((paramData.driver_contact || paramData.customer_contact)) && !fromPutOutTab ?
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                                    <TouchableOpacity onPress={() => (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(paramData.status) != -1) ? role == 'customer' ?
                                        onPressCall(paramData.driver_contact) : (paramData.otherPersonPhone && paramData.otherPersonPhone.length > 0 ? onPressCall(paramData.otherPersonPhone) : onPressCall(paramData.customer_contact)) : onAlert(paramData)}
                                        style={{ backgroundColor: MAIN_COLOR, height: 40, width: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', margin: 3 }}>
                                        <Ionicons name="call" size={24} color={colors.WHITE} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(paramData.status) != -1) ? onChatAction(paramData, paramData.id) : onChatAlert(paramData)} style={{ backgroundColor: MAIN_COLOR, height: 40, width: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', margin: 3 }}>
                                        <Ionicons name="chatbubble-ellipses-sharp" size={24} color={colors.WHITE} />
                                    </TouchableOpacity>
                                </View>
                                : null}
                        </View>
                        : null}

                    {paramData && paramData.otherPerson && paramData.otherPersonPhone ?
                        <View style={{ flexDirection: 'column', flex: 1 }}>
                            <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left', }]}>{t('otherPerson_title')}</Text>
                            <View style={{ flexDirection: 'column', flex: 1, marginBottom: 10, marginHorizontal: 2 }}>
                                {paramData.vehicleModel ?
                                    <View style={{ alignItems: 'center', flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 0 }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}>{t('name')}</Text>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}> : </Text>
                                        <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left" }]}>{paramData.otherPerson}</Text>
                                    </View>
                                    : null}
                                {paramData.vehicleModel ?
                                    <View style={{ alignItems: 'center', flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 5 }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}>{t('phone')}</Text>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}> : </Text>
                                        <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left" }]}>{paramData.otherPersonPhone}</Text>
                                    </View>
                                    : null}
                            </View>
                        </View>
                        : null}

                    {!fromPutOutTab && paramData && paramData.carType ?
                        <View style={{ flexDirection: 'column', marginBottom: 10 }}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, alignItems: 'center' }}>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left', }]}> {isRTL ? ": " : null}{t('car_details_title')} {isRTL ? null : " :"}</Text>
                                <View style={{ alignItems: 'center', flexDirection: isRTL ? 'row-reverse' : 'row', marginHorizontal: 10 }}>
                                    <Image source={paramData && paramData.carImage ? { uri: paramData.carImage } : require('../../assets/images/microBlackCar.png')} style={{ width: 40, height: 40, alignSelf: 'center', resizeMode: 'center', transform: [isRTL ? { scaleX: -1 } : { scaleX: 1 }] }} />
                                    {paramData.carType ?
                                        <View style={{ alignItems: 'center', marginHorizontal: 10 }}>
                                            <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}>{paramData.carType}</Text>
                                        </View>
                                        : null}
                                </View>
                            </View>

                            <View style={[styles.vehicleDetails, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                {paramData.vehicleModel ?
                                    <View style={{ width: width / 3.2, alignItems: 'center' }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}>{t('vehicle_model')}</Text>
                                        <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left" }]}>{paramData.vehicleModel}</Text>
                                    </View>
                                    : null}

                                {paramData.vehicleMake ?
                                    <View style={[styles.hbox2, { minHeight: 35 }]} />
                                    : null}
                                {paramData.vehicleMake ?
                                    <View style={{ width: width / 3.2, marginHorizontal: 3, alignItems: 'center' }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}>{t('vehicle_make')}</Text>
                                        <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left" }]}>{paramData.vehicleMake}</Text>
                                    </View>
                                    : null}
                                {paramData.vehicle_number ?
                                    <View style={[styles.hbox2, { minHeight: 35 }]} />
                                    : null}
                                {paramData.vehicle_number ?
                                    <View style={{ width: width / 3.2, marginHorizontal: 3, alignItems: 'center' }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left" }]}>{t('vehicle_number')}</Text>
                                        <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left" }]}>{paramData.vehicle_number}</Text>
                                    </View>
                                    :
                                    <View style={{ flex: 1 }}>
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", marginBottom: 5 }]}> {t('car_no_not_found')}</Text>
                                    </View>
                                }
                            </View>
                        </View>
                        : null}

                    {paramData && ['PENDING', 'PAID', 'COMPLETE'].indexOf(paramData.status) != -1 ?
                        <View style={{ height: 'auto', marginTop: 10 }}>
                            <View style={[styles.billDetails, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{t('bill_details_title')}</Text>
                                {paramData && paramData.payment_mode && paramData.status ?
                                    <View style={{ flexDirection: isRTL ? "row-reverse" : "row" }}>
                                        {paramData.payment_mode == 'cash' ?
                                            <MaterialCommunityIcons name="cash" size={28} color={colors.BLACK} />
                                            : paramData.payment_mode == 'card' ?
                                                <Feather name="credit-card" size={24} color="black" />
                                                :
                                                <AntDesign name="wallet" size={24} color="black" />
                                        }
                                        <View style={{ backgroundColor: colors.GREEN, padding: 3, borderRadius: 3, alignSelf: 'center', marginHorizontal: 5 }}>
                                            <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left', color: colors.WHITE, paddingHorizontal: 5 }]}>{t(paramData.status)}</Text>
                                        </View>
                                    </View>
                                    : null}
                            </View>

                            <View style={[styles.payRow, { flexDirection: isRTL ? "row-reverse" : "row", marginTop: 10, paddingBottom: 5 }]}>
                                <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >{t("your_trip")}</Text>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >
                                    {settings && settings.swipe_symbol && settings.symbol ?
                                        (
                                            paramData.status && paramData.status.toUpperCase() === 'PENDING'
                                                ? (parseFloat(paramData.trip_cost).toFixed(settings.decimal)) + " " + (settings.symbol)
                                                : paramData.status && paramData.status.toUpperCase() === 'COMPLETE'
                                                    ? (parseFloat(paramData.trip_cost) - parseFloat(paramData.tips || 0)).toFixed(settings.decimal) + " " + (settings.symbol)
                                                    : (parseFloat(paramData.trip_cost) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal) + " " + (settings.symbol)
                                        )
                                        :
                                        (
                                            paramData.status && paramData.status.toUpperCase() === 'PENDING'
                                                ? (settings.symbol) + " " + (parseFloat(paramData.trip_cost).toFixed(settings.decimal))
                                                : paramData.status && paramData.status.toUpperCase() === 'COMPLETE'
                                                    ? (settings.symbol) + " " + (parseFloat(paramData.trip_cost) - parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                                    : (settings.symbol) + " " + (parseFloat(paramData.trip_cost) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                        )
                                    }
                                </Text>
                            </View>
                            {paramData && paramData.tips && parseFloat(paramData.tips) > 0 && paramData.status && paramData.status.toUpperCase() !== 'PENDING' ? (
                                <View style={[styles.payRow, { flexDirection: isRTL ? "row-reverse" : "row", paddingBottom: 5 }]}>
                                    <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]}>{t("tips")}</Text>
                                    <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]}>
                                        {settings && settings.swipe_symbol && settings.symbol ?
                                            (parseFloat(paramData.tips).toFixed(settings.decimal)) + " " + (settings.symbol)
                                            :
                                            (settings.symbol) + " " + (parseFloat(paramData.tips).toFixed(settings.decimal))
                                        }
                                    </Text>
                                </View>
                            ) : null}

                            <View style={[styles.payRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                                <View style={{ flexDirection: isRTL ? "row-reverse" : "row", alignItems: 'baseline', alignSelf: 'center' }}>
                                    <Text style={[styles.textStyle, { textAlign: isRTL ? 'right' : 'left' }]}>{t('discount')}</Text>
                                    <Text style={[styles.textStyle, { textAlign: isRTL ? 'right' : 'left', fontSize: 10 }]}> {t('promo_apply')}</Text>
                                </View>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50, color: colors.RED }]} >
                                    {settings && settings.swipe_symbol && settings.symbol ?
                                        (isRTL ? '' : "-") + " " + (paramData && paramData.discount ? parseFloat(paramData.discount).toFixed(settings.decimal) : "0.00") + " " + (settings.symbol) + (isRTL ? "-" : '')
                                        :
                                        (isRTL ? '' : "-") + (settings.symbol) + " " + (paramData && paramData.discount ? parseFloat(paramData.discount).toFixed(settings.decimal) : "0.00") + " " + (isRTL ? "-" : '')
                                    }
                                </Text>
                            </View>


                            {paramData && paramData.cardPaymentAmount ? paramData.cardPaymentAmount > 0 ?
                                <View style={[styles.payRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                                    <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >{t("CardPaymentAmount")}</Text>
                                    <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >
                                        {settings && settings.swipe_symbol && settings.symbol ?
                                            (parseFloat(paramData.cardPaymentAmount).toFixed(settings.decimal)) + " " + (settings.symbol)
                                            :
                                            (settings.symbol) + " " + (parseFloat(paramData.cardPaymentAmount).toFixed(settings.decimal))
                                        }
                                    </Text>
                                </View>
                                : null : null}

                            {paramData && paramData.cashPaymentAmount ? paramData.cashPaymentAmount > 0 ?
                                <View style={[styles.payRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                                    <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >{t("CashPaymentAmount")}</Text>
                                    <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >
                                        {settings && settings.swipe_symbol && settings.symbol ?
                                            (parseFloat(paramData.cashPaymentAmount) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal) + " " + (settings.symbol)
                                            :
                                            (settings.symbol) + " " + (parseFloat(paramData.cashPaymentAmount) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                        }
                                    </Text>
                                </View>
                                : null : null}

                            {paramData && paramData.usedWalletMoney ? paramData.usedWalletMoney > 0 ?
                                <View style={[styles.payRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
                                    <Text style={[styles.textStyle, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >{t("WalletPayment")}</Text>
                                    <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >
                                        {settings && settings.swipe_symbol && settings.symbol ?
                                            (parseFloat(paramData.usedWalletMoney) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal) + " " + (settings.symbol)
                                            :
                                            (settings.symbol) + " " + (parseFloat(paramData.usedWalletMoney) + parseFloat(paramData.tips || 0)).toFixed(settings.decimal)
                                        }
                                    </Text>
                                </View>
                                : null : null}

                            <View style={{ flexDirection: isRTL ? "row-reverse" : "row", justifyContent: "space-between", marginHorizontal: 10 }}>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >{t("Customer_paid")}</Text>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? "right" : "left", lineHeight: 50 }]} >
                                    {settings && settings.swipe_symbol && settings.symbol ?
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{(paramData && paramData.customer_paid ? parseFloat(paramData.customer_paid) + parseFloat(paramData.tips || 0) : parseFloat(paramData.tips || 0)).toFixed(settings.decimal)} {settings.symbol}</Text>
                                        :
                                        <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{settings.symbol} {(paramData && paramData.customer_paid ? parseFloat(paramData.customer_paid) + parseFloat(paramData.tips || 0) : parseFloat(paramData.tips || 0)).toFixed(settings.decimal)}</Text>
                                    }
                                    <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>
                                    </Text>
                                </Text>
                            </View>
                        </View>
                        : null}

                    {paramData.feedback ?
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                            <View>
                                <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left', }]}> {isRTL ? ": " : null}{t('feedback')} {isRTL ? null : " :"}</Text>
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{paramData.feedback}</Text>
                            </View>
                        </View>
                        : null}

                    {paramData && paramData.status === 'COMPLETE' ? (
                        <Button
                            title={t('download_receipt')}
                            loading={false}
                            buttonStyle={[styles.textStyleBold, { color: colors.WHITE }]}
                            style={{ backgroundColor: MAIN_COLOR, marginTop: 10, height: 50 }}
                            btnClick={downloadReceipt}
                        />
                    ) : null}

                    <View style={styles.mapView}>
                        <View style={styles.mapcontainer}>
                            {paramData && paramData.pickup && paramData.pickup.lat && paramData.pickup.lng && paramData.drop && paramData.drop.lat && paramData.drop.lng ? (
                                <MapView style={styles.map}
                                    provider={PROVIDER_GOOGLE}
                                    region={{
                                        latitude: ((paramData.pickup.lat + paramData.drop.lat) / 2),
                                        longitude: ((paramData.pickup.lng + paramData.drop.lng) / 2),
                                        latitudeDelta: 0.3,
                                        longitudeDelta: 0.3
                                    }}
                                >
                                    <Marker
                                        coordinate={{ latitude: paramData ? (paramData.pickup.lat) : 0.00, longitude: paramData ? (paramData.pickup.lng) : 0.00 }}
                                        title={t('marker_title_1')}
                                        description={paramData ? paramData.pickup.add : null}>
                                        <Image source={require("../../assets/images/green_pin.png")} style={{ height: 35, width: 35 }} />
                                    </Marker>
                                    {paramData.waypoints && paramData.waypoints.length > 0 ? paramData.waypoints.map((point, index) => {
                                        return (
                                            <Marker
                                                coordinate={{ latitude: point.lat, longitude: point.lng }}
                                                title={t('marker_title_3')}
                                                description={point.add}
                                                key={index}
                                            >
                                                <Image source={require("../../assets/images/rsz_2red_pin.png")} style={{ height: 35, width: 35 }} />
                                            </Marker>
                                        )
                                    })
                                        : null}
                                    <Marker
                                        coordinate={{ latitude: (paramData.drop.lat), longitude: (paramData.drop.lng) }}
                                        title={t('marker_title_2')}
                                        description={paramData.drop.add}>
                                        <Image source={require("../../assets/images/rsz_2red_pin.png")} style={{ height: 35, width: 35 }} />
                                    </Marker>
                                    {paramData.coords ?
                                        <Polyline
                                            coordinates={paramData.coords}
                                            strokeWidth={4}
                                            strokeColor={colors.INDICATOR_BLUE}
                                            geodesic={true}
                                        />
                                        : null}
                                </MapView>
                            ) : (
                                <View style={[styles.map, { justifyContent: 'center', alignItems: 'center' }]}>
                                    <Text style={{ fontFamily: fonts.Regular, color: colors.BLACK }}>
                                        Loading ride details...
                                    </Text>
                                </View>
                            )}
                        </View>
                    </View>
                    {(paramData && paramData.status && auth.profile && auth.profile.uid &&
                        (((['PAYMENT_PENDING', 'NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING', 'PAID'].indexOf(paramData.status) != -1) && auth.profile.usertype == 'customer') ||
                            ((['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED'].indexOf(paramData.status) != -1) && auth.profile.usertype == 'driver'))) ?
                        <Button
                            title={paramData.status == 'PAID' ? t('add_to_review') : paramData.status == 'PAYMENT_PENDING' ? t('paynow_button') : t('go_to_booking')}
                            loading={false}
                            loadingColor={{ color: colors.GREEN }}
                            buttonStyle={[styles.textStyleBold, { color: colors.WHITE }]}
                            style={{ backgroundColor: MAIN_COLOR, marginTop: 10, height: 50 }}
                            btnClick={() => { goToBooking(paramData.id) }}
                        />
                        : null}
                </View>
            </ScrollView>
            {UserInfoModal()}
        </View>
    )

}

const styles = StyleSheet.create({
    vew: {
        borderRadius: 5,
        backgroundColor: colors.WHITE,
        shadowColor: colors.BUTTON_RIGHT,
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.4,
        shadowRadius: 2,
        elevation: 3,
        margin: 5,
        padding: 5
    },
    mapView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 180,
        marginTop: 10,
        borderRadius: 5,
        overflow: 'hidden'
    },
    mapcontainer: {
        flex: 1,
        width: width - 20,
        justifyContent: 'center',
        alignItems: 'center'
    },
    map: {
        flex: 1,
        ...StyleSheet.absoluteFillObject,
    },
    userView: {
        flexDirection: 'column'
    },
    mainView: {
        flex: 1,
        backgroundColor: colors.WHITE
    },
    hbox2: {
        width: 1,
        backgroundColor: MAIN_COLOR
    },
    centeredView: {
        flex: 1,
        justifyContent: "center",
        backgroundColor: colors.BACKGROUND
    },
    modalView: {
        margin: 20,
        backgroundColor: "white",
        borderRadius: 20,
        padding: 35,
        alignItems: "flex-start",
        shadowColor: colors.BLACK,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    textContainerStyle: {
        flexDirection: 'column',
        marginBottom: 12,
    },
    multiAddressChar: {
        height: 20,
        width: 20,
        borderWidth: 1,
        backgroundColor: colors.SECONDARY,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center'
    },
    textStyle: {
        fontSize: 15,
        fontFamily: fonts.Regular
    },
    textStyleBold: {
        fontSize: 15,
        fontFamily: fonts.Bold
    },
    VehicleDetails: {
        width: "100%"
    },
    callView: {
        backgroundColor: MAIN_COLOR,
        height: 40,
        width: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        margin: 3
    },
    modalBtn: {
        flexDirection: 'row',
        alignSelf: 'center',
        borderWidth: 1,
        minWidth: 80,
        padding: 5,
        justifyContent: 'center',
        borderRadius: 10
    },
    details: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        borderBottomWidth: .6,
        borderBottomColor: MAIN_COLOR
    },
    section: {
        flex: 1,
        justifyContent: 'space-evenly',
        alignItems: 'center'
    },
    clock: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        minHeight: 60
    },
    driverDetails: {
        flex: 1,
        alignItems: 'center',
        marginTop: 10,
        paddingVertical: 10
    },
    vehicleDetails: {
        flex: 1,
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBlockColor: colors.FOOTERTOP,
        paddingBottom: 10
    },
    billDetails: {
        justifyContent: 'space-between',
        alignItems: 'center',
        marginHorizontal: 10
    },
    payRow: {
        justifyContent: "space-between",
        borderBottomWidth: 1,
        borderBottomColor: colors.FOOTERTOP,
        marginHorizontal: 10
    }
}); 