import { Picker } from '@react-native-picker/picker';
import { CommonActions, useFocusEffect } from '@react-navigation/native';
import { api, FirebaseContext } from 'common';
import { ActivityAction, startActivityAsync } from 'expo-intent-launcher';
import * as Location from 'expo-location';
import i18n from 'i18n-js';
import React, { useContext, useEffect, useRef, useState } from 'react';
import {
    Alert,
    Animated,
    Dimensions,
    Linking,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TextInput,
    View
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { getDirectionsApi } from '../../../common/src/other/GoogleAPIFunctions';
import { fonts } from "../common/font";
import BookingModal, { appConsts, MAIN_COLOR, validateBookingObj } from '../common/sharedFunctions';
import { colors } from '../common/theme';
import Button from '../components/Button';
import { OptionModal } from '../components/OptionModal';
var { height, width } = Dimensions.get('window');

const hasNotch = Platform.OS === 'ios' && !Platform.isPad && !Platform.isTVOS && ((height === 780 || width === 780) || (height === 812 || width === 812) || (height === 844 || width === 844) || (height === 852 || width === 852) || (height === 896 || width === 896) || (height === 926 || width === 926) || (height === 932 || width === 932))

export default function Reserve(props) {
    const {
        fetchAddressfromCoords,
        fetchDrivers,
        updateTripPickup,
        updateTripDrop,
        updatSelPointType,
        getDistanceMatrix,
        MinutesPassed,
        updateTripCar,
        getEstimate,
        clearEstimate,
        addBooking,
        clearBooking,
        clearTripPoints,
        GetDistance,
        updateProfile,
        updateProfileWithEmail,
        checkUserExists,
        storeAddresses
    } = api;
    const dispatch = useDispatch();
    const { config } = useContext(FirebaseContext);
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;

    const auth = useSelector(state => state.auth);
    const settings = useSelector(state => state.settingsdata.settings);
    const cars = useSelector(state => state.cartypes.cars);
    const tripdata = useSelector(state => state.tripdata);
    const usersdata = useSelector(state => state.usersdata);
    const estimatedata = useSelector(state => state.estimatedata);
    const providers = useSelector(state => state.paymentmethods.providers);
    const gps = useSelector(state => state.gpsdata);
    const activeBookings = useSelector(state => state.bookinglistdata.active);
    const addressdata = useSelector(state => state.addressdata);
    const [datePickerOpen, setDatePickerOpen] = useState(false)
    const latitudeDelta = 0.0922;
    const longitudeDelta = 0.0421;

    const [allCarTypes, setAllCarTypes] = useState([]);
    const [freeCars, setFreeCars] = useState([]);
    const [pickerConfig, setPickerConfig] = useState({
        selectedDateTime: new Date(),
        dateModalOpen: false,
        dateMode: 'date'
    });
    const [region, setRegion] = useState(null);
    const [optionModalStatus, setOptionModalStatus] = useState(false);
    const [bookingDate, setBookingDate] = useState(null);
    const [bookingModalStatus, setBookingModalStatus] = useState(false);
    const [bookLoading, setBookLoading] = useState(false);
    const [bookLaterLoading, setBookLaterLoading] = useState(false);
    const [initDate, setInitDate] = useState(null);

    const instructionInitData = {
        otherPerson: "",
        otherPersonPhone: "",
        pickUpInstructions: "",
        deliveryInstructions: "",
        parcelTypeIndex: 0,
        optionIndex: 0,
        parcelTypeSelected: null,
        optionSelected: null
    };
    const [instructionData, setInstructionData] = useState(instructionInitData);
    const bookingdata = useSelector(state => state.bookingdata);
    const [locationRejected, setLocationRejected] = useState(false);
    const mapRef = useRef();
    const [dragging, setDragging] = useState(0);

    const animation = useRef(new Animated.Value(4)).current;
    const [isEditing, setIsEditing] = useState(false);
    const [touchY, setTouchY] = useState();

    const [bookingType, setBookingType] = useState(false);
    const intVal = useRef();

    const [profile, setProfile] = useState();
    const [checkType, setCheckType] = useState(false);
    const pageActive = useRef();
    const [drivers, setDrivers] = useState();
    const [roundTrip, setRoundTrip] = useState(false);
    const [tripInstructions, setTripInstructions] = useState('');
    const [payment_mode, setPaymentMode] = useState(0);
    const [radioProps, setRadioProps] = useState([]);
    const [checkTerm, setCheckTerm] = useState(false);
    const [bookModelLoading, setBookModelLoading] = useState(false);
    const [term, setTerm] = useState(false);
    const [deliveryWithBid, setDeliveryWithBid] = useState(false);
    const [otherPerson, setOtherPerson] = useState(false)

    const [pickupInput, setPickupInput] = useState('');
    const [dropInput, setDropInput] = useState('');
    const [selectedCarType, setSelectedCarType] = useState(null);

    const [tripType, setTripType] = useState('ONE WAY TRIP');
    const [flightNumber, setFlightNumber] = useState('');
    const [noteText, setNoteText] = useState('');
    const [returnDate, setReturnDate] = useState(null);
    const [returnFlightNumber, setReturnFlightNumber] = useState('');

    const [tripDate, setTripDate] = useState(null);
    const [tripDatePickerOpen, setTripDatePickerOpen] = useState(false);

    const [activeDatePicker, setActiveDatePicker] = useState(null); // 'trip' | 'return' | null

    const farelistdata = useSelector(state => state.farelist.fare);
    // console.log("farelistdata",farelistdata);

    useEffect(() => {
        if (settings && settings.bookingFlow) {
            setDeliveryWithBid(settings.bookingFlow == "2" ? true : false)
        }
    }, [settings])

    const profileInitData = {
        firstName: auth && auth.profile && auth.profile.firstName ? auth.profile.firstName : "",
        lastName: auth && auth.profile && auth.profile.lastName ? auth.profile.lastName : "",
        email: auth && auth.profile && auth.profile.email ? auth.profile.email : "",
    };
    const [profileData, setProfileData] = useState(profileInitData);
    const [bookingOnWait, setBookingOnWait] = useState();

    const addresses = useSelector(state => state.locationdata.addresses);

    useEffect(() => {
        if (auth.profile) {
            setTimeout(() => {
                setTerm(true)
            }, 2000);
            setCheckTerm(auth.profile.term ? true : false)
            if (bookingOnWait) {
                finaliseBooking(bookingOnWait);
                setBookingOnWait(null);
                setBookModelLoading(false);
            }
        }
    }, [auth.profile, bookingOnWait])

    useEffect(() => {
        if (settings && providers) {
            let arr = [];
            let val = 0;
            if (!settings.disable_online && providers && providers.length > 0) {
                arr.push({ label: t('card'), value: val, cat: 'card' });
                val++;
            }
            if (!settings.disable_cash) {
                arr.push({ label: t('cash'), value: val, cat: 'cash' });
            }
            setRadioProps(arr);
        }
    }, [settings, providers]);

    useEffect(() => {
        if (usersdata.drivers) {
            const freeDrivers = usersdata.drivers.filter(d => !d.queue)
            let arr = [];
            for (let i = 0; i < freeDrivers.length; i++) {
                let driver = freeDrivers[i];
                if (!driver.carType) {
                    let carTypes = allCarTypes;
                    for (let i = 0; i < carTypes.length; i++) {
                        let temp = { ...driver, carType: carTypes[i].name };
                        arr.push(temp);
                    }
                } else {
                    arr.push(driver);
                }
            }
            setDrivers(arr);
        }
    }, [usersdata.drivers]);

    useEffect(() => {
        if (auth.profile && auth.profile.uid) {
            setProfile(auth.profile);
        } else {
            setProfile(null);
        }
    }, [auth.profile]);

    useEffect(() => {
        if (tripdata.drop && tripdata.drop.add) {
            setIsEditing(true);
        }
    }, [tripdata]);

    useEffect(() => easing => {
        Animated.timing(animation, {
            toValue: !isEditing ? 4 : 0,
            duration: 300,
            useNativeDriver: false,
            easing
        }).start();
    }, [isEditing]);

    useEffect(() => {
        if (cars) {
            resetCars();
        }
    }, [cars]);

    useEffect(() => {
        if (tripdata.pickup && drivers) {
            getDrivers();
        }
        if (tripdata.pickup && !drivers) {
            resetCars();
            setFreeCars([]);
        }
    }, [drivers, tripdata.pickup]);

    useEffect(() => {
        if (estimatedata.estimate) {
            if (!bookingdata.loading) {
                setBookingModalStatus(true);
            }
            setBookLoading(false);
            setBookLaterLoading(false);
        }
        if (estimatedata.error && estimatedata.error.flag) {
            setBookLoading(false);
            setBookLaterLoading(false);
            Alert.alert(estimatedata.error.msg);
            dispatch(clearEstimate());
        }
    }, [estimatedata.estimate, estimatedata.error, estimatedata.error.flag]);

    useEffect(() => {
        if (tripdata.selected && tripdata.selected == 'pickup' && tripdata.pickup && tripdata.pickup.source == 'search' && mapRef.current) {
            if (!locationRejected) {
                setTimeout(() => {
                    mapRef.current.animateToRegion({
                        latitude: tripdata.pickup.lat,
                        longitude: tripdata.pickup.lng,
                        latitudeDelta: latitudeDelta,
                        longitudeDelta: longitudeDelta
                    });
                }, 1000);
            } else {
                setRegion({
                    latitude: tripdata.pickup.lat,
                    longitude: tripdata.pickup.lng,
                    latitudeDelta: latitudeDelta,
                    longitudeDelta: longitudeDelta
                });
            }
        }
        if (tripdata.selected && tripdata.selected == 'drop' && tripdata.drop && tripdata.drop.source == 'search' && mapRef.current) {
            if (!locationRejected) {
                setTimeout(() => {
                    mapRef.current.animateToRegion({
                        latitude: tripdata.drop.lat,
                        longitude: tripdata.drop.lng,
                        latitudeDelta: latitudeDelta,
                        longitudeDelta: longitudeDelta
                    });
                }, 1000)
            } else {
                setRegion({
                    latitude: tripdata.drop.lat,
                    longitude: tripdata.drop.lng,
                    latitudeDelta: latitudeDelta,
                    longitudeDelta: longitudeDelta
                });
            }
        }
    }, [tripdata.selected, tripdata.pickup, tripdata.drop, mapRef.current]);

    useEffect(() => {
        if (bookingdata.booking) {
            const bookingStatus = bookingdata.booking.mainData.status;
            setTimeout(() => {
                props.navigation.dispatch(
                    CommonActions.reset({
                        index: 0,
                        routes: [
                            {
                                name: 'TabRoot',
                                state: {
                                    index: 0,
                                    routes: [
                                        { name: 'RideList' }
                                    ]
                                }
                            }
                        ]
                    })
                );
                dispatch(clearEstimate());
                dispatch(clearBooking());
                dispatch(clearTripPoints());
            }, 1000);
        }
        if (bookingdata.error && bookingdata.error.flag) {
            Alert.alert(bookingdata.error.msg);
            dispatch(clearBooking());
        }
        if (bookingdata.loading) {
            setBookLoading(true);
            setBookLaterLoading(true);
        }
    }, [bookingdata.booking, bookingdata.loading, bookingdata.error, bookingdata.error.flag]);

    useEffect(() => {
        if (gps.location) {
            if (gps.location.lat && gps.location.lng) {
                setDragging(0);
                if (region) {
                    mapRef.current.animateToRegion({
                        latitude: gps.location.lat,
                        longitude: gps.location.lng,
                        latitudeDelta: latitudeDelta,
                        longitudeDelta: longitudeDelta
                    });
                }
                else {
                    setRegion({
                        latitude: gps.location.lat,
                        longitude: gps.location.lng,
                        latitudeDelta: latitudeDelta,
                        longitudeDelta: longitudeDelta
                    });
                }
                updateAddresses({
                    latitude: gps.location.lat,
                    longitude: gps.location.lng
                }, region ? 'gps' : 'init');
            } else {
                setLocationRejected(true);
            }
        }
    }, [gps.location]);


    useEffect(() => {
        if (region && mapRef.current) {
            if (Platform.OS == 'ios') {
                mapRef.current.animateToRegion({
                    latitude: region.latitude,
                    longitude: region.longitude,
                    latitudeDelta: latitudeDelta,
                    longitudeDelta: longitudeDelta
                });
            }
        }
    }, [region, mapRef.current]);

    const resetCars = () => {
        if (cars) {
            let carWiseArr = [];
            const sorted = cars.sort((a, b) => a.pos - b.pos);
            for (let i = 0; i < sorted.length; i++) {
                let temp = { ...sorted[i], minTime: '', available: false, active: false };
                carWiseArr.push(temp);
            }
            setAllCarTypes(carWiseArr);
        }
    }

    const resetActiveCar = () => {
        let carWiseArr = [];
        const sorted = allCarTypes.sort((a, b) => a.pos - b.pos);
        for (let i = 0; i < sorted.length; i++) {
            let temp = { ...sorted[i], active: false };
            carWiseArr.push(temp);
        }
        setAllCarTypes(carWiseArr);
    }

    const locateUser = async () => {
        if (tripdata.selected == 'pickup') {
            let tempWatcher = await Location.watchPositionAsync({
                accuracy: Location.Accuracy.Balanced
            }, location => {
                dispatch({
                    type: 'UPDATE_GPS_LOCATION',
                    payload: {
                        lat: location.coords.latitude,
                        lng: location.coords.longitude
                    }
                });
                tempWatcher.remove();
            })
        }
    }


    const setAddresses = async (pos, res, source) => {
        if (res) {
            if (tripdata.selected == "pickup") {
                dispatch(
                    updateTripPickup({
                        lat: pos.latitude,
                        lng: pos.longitude,
                        add: res,
                        source: source,
                    })
                );
                if (source == "init") {
                    dispatch(
                        updateTripDrop({
                            lat: pos.latitude,
                            lng: pos.longitude,
                            add: null,
                            source: source,
                        })
                    );
                }
            } else {
                dispatch(
                    updateTripDrop({
                        lat: pos.latitude,
                        lng: pos.longitude,
                        add: res,
                        source: source,
                    })
                );
            }
        }
    };

    const updateAddresses = async (pos, source) => {
        let latlng = pos.latitude + "," + pos.longitude;
        if (!pos.latitude) return;
        let res = '';
        let found = false;
        let savedAddresses = [];

        try {
            const value = addresses;
            if (value !== null) {
                savedAddresses = JSON.parse(value);
                for (let i = 0; i < savedAddresses.length; i++) {
                    let distance = GetDistance(pos.latitude, pos.longitude, savedAddresses[i].lat, savedAddresses[i].lng);
                    if (distance < 0.25) {
                        res = savedAddresses[i].description;
                        found = true;
                        break;
                    }
                }
            }
        } catch (error) {
            found = false;
        }

        if (found) {
            setAddresses(pos, res, source);
        } else {
            fetchAddressfromCoords(latlng).then((add) => {
                if (add) {
                    savedAddresses.push({
                        lat: pos.latitude,
                        lng: pos.longitude,
                        description: add
                    });
                    storeAddresses(savedAddresses);
                    setAddresses(pos, add, source);
                }
            });
        }
    };

    const onRegionChangeComplete = (newregion, gesture) => {
        if ((tripdata.pickup && tripdata.pickup.source == 'mapSelect') || (tripdata.drop && tripdata.drop.source == 'mapSelect')) {
            if (gesture && gesture.isGesture) {
                updateAddresses({
                    latitude: newregion.latitude,
                    longitude: newregion.longitude
                }, 'mapSelect');
            }
        }
    }

    const selectCarType = (value, key) => {
        let carTypes = allCarTypes;
        for (let i = 0; i < carTypes.length; i++) {
            carTypes[i].active = false;
            if (carTypes[i].name == value.name) {
                carTypes[i].active = true;
                let instObj = { ...instructionData };
                if (Array.isArray(carTypes[i].parcelTypes)) {
                    instObj.parcelTypeSelected = carTypes[i].parcelTypes[0];
                    instObj.parcelTypeIndex = 0;
                }
                if (Array.isArray(carTypes[i].options)) {
                    instObj.optionSelected = carTypes[i].options[0];
                    instObj.optionIndex = 0;
                }
                setInstructionData(instObj);
            } else {
                carTypes[i].active = false;
            }
        }
        dispatch(updateTripCar(value));
    }

    const getDrivers = async () => {
        if (tripdata.pickup) {
            let availableDrivers = [];
            let arr = {};
            let startLoc = tripdata.pickup.lat + ',' + tripdata.pickup.lng;

            let distArr = [];
            let allDrivers = [];
            for (let i = 0; i < drivers.length; i++) {
                let driver = { ...drivers[i] };
                let distance = GetDistance(tripdata.pickup.lat, tripdata.pickup.lng, driver.location.lat, driver.location.lng);
                if (settings.convert_to_mile) {
                    distance = distance / 1.609344;
                }
                if (distance < ((settings && settings.driverRadius) ? settings.driverRadius : 10)) {
                    driver["distance"] = distance;
                    allDrivers.push(driver);
                }
            }

            const sortedDrivers = settings.useDistanceMatrix ? allDrivers.slice(0, 25) : allDrivers;

            if (sortedDrivers.length > 0) {
                let driverDest = "";
                for (let i = 0; i < sortedDrivers.length; i++) {
                    let driver = { ...sortedDrivers[i] };
                    driverDest = driverDest + driver.location.lat + "," + driver.location.lng
                    if (i < (sortedDrivers.length - 1)) {
                        driverDest = driverDest + '|';
                    }
                }

                if (settings.useDistanceMatrix) {
                    distArr = await getDistanceMatrix(startLoc, driverDest);
                } else {
                    for (let i = 0; i < sortedDrivers.length; i++) {
                        distArr.push({ timein_text: ((sortedDrivers[i].distance * 2) + 1).toFixed(0) + ' min', found: true })
                    }
                }


                for (let i = 0; i < sortedDrivers.length; i++) {
                    let driver = { ...sortedDrivers[i] };
                    if (distArr[i].found && cars) {
                        driver.arriveTime = distArr[i];
                        for (let i = 0; i < cars.length; i++) {
                            if (cars[i].name == driver.carType) {
                                driver.carImage = cars[i].image;
                            }
                        }
                        let carType = driver.carType;
                        if (carType && carType.length > 0) {
                            if (arr[carType] && arr[carType].sortedDrivers) {
                                arr[carType].sortedDrivers.push(driver);
                                if (arr[carType].minDistance > driver.distance) {
                                    arr[carType].minDistance = driver.distance;
                                    arr[carType].minTime = driver.arriveTime.timein_text;
                                }
                            } else {
                                arr[carType] = {};
                                arr[carType].sortedDrivers = [];
                                arr[carType].sortedDrivers.push(driver);
                                arr[carType].minDistance = driver.distance;
                                arr[carType].minTime = driver.arriveTime.timein_text;
                            }
                        } else {
                            let carTypes = allCarTypes;
                            for (let i = 0; i < carTypes.length; i++) {
                                let carType = carTypes[i];
                                if (arr[carType]) {
                                    arr[carType].sortedDrivers.push(driver);
                                    if (arr[carType].minDistance > driver.distance) {
                                        arr[carType].minDistance = driver.distance;
                                        arr[carType].minTime = driver.arriveTime.timein_text;
                                    }
                                } else {
                                    arr[carType] = {};
                                    arr[carType].sortedDrivers = [];
                                    arr[carType].sortedDrivers.push(driver);
                                    arr[carType].minDistance = driver.distance;
                                    arr[carType].minTime = driver.arriveTime.timein_text;
                                }
                            }
                        }
                        availableDrivers.push(driver);
                    }
                }
            }

            let carWiseArr = [];
            if (cars) {
                for (let i = 0; i < cars.length; i++) {
                    let temp = { ...cars[i] };
                    if (arr[cars[i].name]) {
                        temp['nearbyData'] = arr[cars[i].name].drivers;
                        temp['minTime'] = arr[cars[i].name].minTime;
                        temp['available'] = true;
                    } else {
                        temp['minTime'] = '';
                        temp['available'] = false;
                    }
                    temp['active'] = (tripdata.carType && (tripdata.carType.name == cars[i].name)) ? true : false;
                    carWiseArr.push(temp);
                }
            }

            setFreeCars(availableDrivers);
            setAllCarTypes(carWiseArr);
        }
    }

    const tapAddress = (selection) => {
        if (selection === tripdata.selected) {
            let savedAddresses = [];
            let allAddresses = profile.savedAddresses;
            for (let key in allAddresses) {
                savedAddresses.push(allAddresses[key]);
            }
            if (selection == 'drop') {
                props.navigation.navigate('Search', { locationType: "drop", addParam: savedAddresses });
            } else {
                props.navigation.navigate('Search', { locationType: "pickup", addParam: savedAddresses });
            }
        } else {
            setDragging(0);
            dispatch(updatSelPointType(selection));
        }
    };

    const onPressBookLater = () => {
        setCheckType(false);
        // For testing: hardcode tripDate and returnDate
        // const now = new Date();
        // const testTripDate = new Date(now.getTime() + 4 * 60 * 60 * 1000); // 4 hours from now
        // const testReturnDate = new Date(now.getTime() + 6 * 60 * 60 * 1000); // 6 hours from now
        // setTripDate(testTripDate);
        // setReturnDate(testReturnDate);


        if (!(profile.mobile && profile.mobile.length > 6)) {
            Alert.alert(t('alert'), t('mobile_need_update'));
            props.navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'Profile', params: { fromPage: 'Map' } }] }));
        } else {
            if ((settings && settings.imageIdApproval && auth.profile.verifyId && auth.profile.verifyIdImage) || (settings && !settings.imageIdApproval)) {
                if (auth.profile.approved) {
                    if (tripdata.pickup && tripdata.drop && tripdata.drop.add) {
                        if (tripdata.carType) {


                             // Bypass tripDate selection: default to now + 2h 10m if not set
                            //  const defaultTripDate = new Date(Date.now() + ((2 * 60 + 10) * 60 * 1000));
                            //  const selectedTripDate = tripDate || defaultTripDate;
                            //  if (!tripDate) setTripDate(defaultTripDate);
                             // Bypass tripDate selection: default to now + 2h 10m if not set
 

                            // Add validation for tripDate 
                            if (!tripDate) {
                                Alert.alert(t('alert'), t('please_select_trip_date'));
                                return;
                            }


                            // Bypass return tripDate selection: default to now + 2h 10m if not set
                            //  const defaultReturnDate = new Date(Date.now() + ((2 * 60 + 10) * 60 * 1000));
                            //  const selectedReturnDate = returnDate || defaultReturnDate;
                            //  if (!returnDate) setReturnDate(defaultReturnDate);
                             // Bypass returnDate selection: default to now + 2h 10m if not set
                            
                             // Add validation for returnDate if it's a round trip
                            if (tripType === 'ROUND TRIP' && !returnDate) {
                                Alert.alert(t('alert'), t('please_select_return_date'));
                                return;
                            }

                            // Skip flight number validations when pickup is ORD or MDW
                            const skipFlightValidation = isPickupAtORDorMDW();
                            console.log("skipFlightValidation",skipFlightValidation); 
                            // Add validation for flight number
                            if (skipFlightValidation && (!flightNumber || flightNumber.trim() === '')) {
                                Alert.alert(t('alert'), t('please_enter_flight_number'));
                                return;
                            }

                            // Add validation for return flight number if it's a round trip
                            if (tripType === 'ROUND TRIP' && !skipFlightValidation && (!returnFlightNumber || returnFlightNumber.trim() === '')) {
                                Alert.alert(t('alert'), t('please_enter_return_flight_number'));
                                return;
                            }

                            // Use the selected tripDate instead of hardcoded value
                            // const tripDate = testTripDate;
                            handleDateConfirm(tripDate, tripdata);
                        } else {
                            Alert.alert(t('alert'), t('car_type_blank_error'))
                        }
                    } else {
                        Alert.alert(t('alert'), t('drop_location_blank_error'))
                    }
                } else {
                    Alert.alert(t('alert'), t('admin_contact'))
                }
            } else {
                Alert.alert(
                    t('alert'),
                    t('verifyid_error'),
                    [
                        { text: t('cancel'), onPress: () => { }, style: 'cancel' },
                        {
                            text: t('ok'), onPress: () =>
                                props.navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'editUser', params: { fromPage: 'Map' } }] }))
                        }
                    ],
                    { cancelable: false }
                );
            }
        }
    }

    const hideDatePicker = () => {
        setDatePickerOpen(false);
    };

    const handleDateConfirm = (date, tripdata) => {
        setInitDate(date);
        setDatePickerOpen(false);
        setBookLaterLoading(true);
        setTimeout(async () => {
            let date1;
            try {
                let res = await fetch(`https://${config.projectId}.web.app/getservertime`, { method: 'GET', headers: { 'Content-Type': 'application/json' } });
                const json = await res.json();
                if (json.time) {
                    date1 = json.time;
                } else {
                    date1 = new Date().getTime();
                }
            } catch (err) {
                date1 = new Date().getTime();
            }

            const date2 = new Date(date);
            const diffTime = date2 - date1;
            const diffMins = diffTime / (1000 * 60);

            if (diffMins < 15) {
                setBookLaterLoading(false);
                Alert.alert(
                    t('alert'),
                    t('past_booking_error'),
                    [
                        { text: t('ok'), onPress: () => { } }
                    ],
                    { cancelable: true }
                );
            } else {
                setBookingDate(date);
                setBookingType(true);
                if (appConsts.hasOptions) {
                    setOptionModalStatus(true);
                    setBookLaterLoading(false);
                } else {
                    // 1. Extract postal codes - prioritize postal codes stored in location data
                    let pickUpPostal = tripdata.pickup.postalCode || extractPostalCode(tripdata.pickup.add);
                    let dropUpPostal = tripdata.drop.postalCode || extractPostalCode(tripdata.drop.add);

                    // If no postal codes found in location data, try to extract from address
                    if (!pickUpPostal) {
                        pickUpPostal = extractPostalCode(tripdata.pickup.placeDetails?.formatted_address || tripdata.pickup.add);
                    }
                    if (!dropUpPostal) {
                        dropUpPostal = extractPostalCode(tripdata.drop.placeDetails?.formatted_address || tripdata.drop.add);
                    }

                    console.log('Pickup postal code:', pickUpPostal);
                    console.log('Drop postal code:', dropUpPostal);

                    // 2. Find filtered fare
                    let baseFare = null;
                    if (pickUpPostal && dropUpPostal) {
                        const filteredFare = farelistdata.find(
                            fare => (fare.pincode1 === pickUpPostal && fare.pincode2 === dropUpPostal) ||
                                (fare.pincode1 === dropUpPostal && fare.pincode2 === pickUpPostal)
                        );
                        if (filteredFare) {
                            baseFare = filteredFare.fare;
                        } else {
                            setBookLoading(false);
                            setBookLaterLoading(false);
                            Alert.alert(t('alert'), t('booking_not_available_for_location'));
                            return;
                        }
                    } else {
                        setBookLoading(false);
                        setBookLaterLoading(false);
                        Alert.alert(t('alert'), t('postal_code_error_booking_not_available'));
                        return;
                    }

                    // 3. Get route details
                    let routeDetails = await getRouteDetails(tripdata.pickup.add, tripdata.drop.add);
                    if (!routeDetails) {
                        setBookLoading(false);
                        setBookLaterLoading(false);
                        Alert.alert(t('alert'), 'Route not found');
                        return;
                    }

                    // 4. Prepare estimate request
                    let estimateRequest = {
                        pickup: {
                            ...tripdata.pickup,
                            coords: {
                                lat: tripdata.pickup?.lat,
                                lng: tripdata.pickup?.lng
                            },
                            description: tripdata.pickup?.add || tripdata.pickup?.description,
                            postalCode: pickUpPostal
                        },
                        drop: {
                            ...tripdata.drop,
                            coords: {
                                lat: tripdata.drop?.lat,
                                lng: tripdata.drop?.lng
                            },
                            description: tripdata.drop?.add || tripdata.drop?.description,
                            postalCode: dropUpPostal
                        },
                        carDetails: tripdata.carType,
                        instructionData: instructionData,
                        routeDetails: routeDetails,
                        baseFare: baseFare
                    };

                    setBookLoading(true);
                    setBookLaterLoading(true);
                    try {
                        await dispatch(getEstimate(estimateRequest));
                    } catch (error) {
                        Alert.alert(t('alert'), t('estimate_error'));
                    } finally {
                        setBookLoading(false);
                        setBookLaterLoading(false);
                    }
                }
            }
        }, 1000);

    };

    // Update the extractPostalCode function to handle postal codes from location data
    function extractPostalCode(address) {
        if (!address || typeof address !== 'string') return '';
        
        // First try to extract from the address string
        const match = address.match(/\b\d{5}(?:-\d{4})?\b/);
        if (match) return match[0];
        
        return '';
    }

    // Helper to get route details (replace with your actual implementation)
    const getRouteDetails = async (pickup, drop) => {
        try {
            // Accepts either address string or object with lat/lng
            let startLoc, destLoc;
            if (typeof pickup === 'object' && pickup.lat && pickup.lng) {
                startLoc = pickup.lat + ',' + pickup.lng;
            } else if (typeof pickup === 'string') {
                startLoc = pickup;
            } else {
                return null;
            }
            if (typeof drop === 'object' && drop.lat && drop.lng) {
                destLoc = drop.lat + ',' + drop.lng;
            } else if (typeof drop === 'string') {
                destLoc = drop;
            } else {
                return null;
            }
            const result = await getDirectionsApi(startLoc, destLoc);
            if (result && result.distance_in_km && result.time_in_secs && result.polylinePoints) {
                return {
                    distance_in_km: result.distance_in_km,
                    time_in_secs: result.time_in_secs,
                    polylinePoints: result.polylinePoints
                };
            } else {
                return null;
            }
        } catch (error) {
            console.log('getRouteDetails error:', error);
            return null;
        }
    };

    // Update the handleGetEstimate function to use postal codes from location data
    const handleGetEstimate = async () => {
        setOptionModalStatus(false);

        if (!tripdata.pickup || !tripdata.drop || !tripdata.carType) {
            Alert.alert(t('alert'), t('select_proper'));
            setBookLoading(false);
            setBookLaterLoading(false);
            return;
        }

        // 1. Extract postal codes - prioritize postal codes stored in location data
        let pickUpPostal = tripdata.pickup.postalCode || extractPostalCode(tripdata.pickup.add);
        let dropUpPostal = tripdata.drop.postalCode || extractPostalCode(tripdata.drop.add);

        // If no postal codes found in location data, try to extract from address
        if (!pickUpPostal) {
            pickUpPostal = extractPostalCode(tripdata.pickup.placeDetails?.formatted_address || tripdata.pickup.add);
        }
        if (!dropUpPostal) {
            dropUpPostal = extractPostalCode(tripdata.drop.placeDetails?.formatted_address || tripdata.drop.add);
        }

        console.log('Pickup postal code:', pickUpPostal);
        console.log('Drop postal code:', dropUpPostal);

        // 2. Find filtered fare
        let baseFare = null;
        if (pickUpPostal && dropUpPostal && farelistdata) {
            const filteredFare = farelistdata.find(
                fare => (fare.pincode1 === pickUpPostal && fare.pincode2 === dropUpPostal) ||
                    (fare.pincode1 === dropUpPostal && fare.pincode2 === pickUpPostal)
            );
            if (filteredFare) {
                baseFare = filteredFare.fare;
                console.log('Found fare for route:', filteredFare);
            } else {
                Alert.alert(t('alert'), t('booking_not_available_for_location'));
                setBookLoading(false);
                setBookLaterLoading(false);
                return;
            }
        } else {
            Alert.alert(t('alert'), t('postal_code_error_booking_not_available'));
            setBookLoading(false);
            setBookLaterLoading(false);
            return;
        }

        // 3. Get route details
        let routeDetails = await getRouteDetails(tripdata.pickup, tripdata.drop);
        if (!routeDetails) {
            Alert.alert(t('alert'), t('route_not_found'));
            setBookLoading(false);
            setBookLaterLoading(false);
            return;
        }

        // 4. Prepare estimate request
        let estimateRequest = {
            pickup: {
                ...tripdata.pickup,
                coords: {
                    lat: tripdata.pickup?.lat,
                    lng: tripdata.pickup?.lng
                },
                description: tripdata.pickup?.add || tripdata.pickup?.description,
                postalCode: pickUpPostal
            },
            drop: {
                ...tripdata.drop,
                coords: {
                    lat: tripdata.drop?.lat,
                    lng: tripdata.drop?.lng
                },
                description: tripdata.drop?.add || tripdata.drop?.description,
                postalCode: dropUpPostal
            },
            carDetails: tripdata.carType,
            instructionData: instructionData,
            routeDetails: routeDetails,
            baseFare: baseFare
        };

        setBookLoading(true);
        setBookLaterLoading(true);
        try {
            await dispatch(getEstimate(estimateRequest));
        } catch (error) {
            Alert.alert(t('alert'), t('estimate_error'));
        } finally {
            setBookLoading(false);
            setBookLaterLoading(false);
        }
    };

    const handleParcelTypeSelection = (value) => {
        setInstructionData({
            ...instructionData,
            parcelTypeIndex: value,
            parcelTypeSelected: tripdata.carType.parcelTypes[value]
        });
    }

    const handleOptionSelection = (value) => {
        setInstructionData({
            ...instructionData,
            optionIndex: value,
            optionSelected: tripdata.carType.options[value]
        });
    }

    const onModalCancel = () => {
        setInstructionData(instructionInitData);
        setTripInstructions("");
        setRoundTrip(false);
        dispatch(updateTripCar(null));
        setBookingModalStatus(false);
        setOptionModalStatus(false);
        resetActiveCar();
        setBookLoading(false);
        setBookLaterLoading(false);
        dispatch(clearEstimate());
        setBookModelLoading(false);
    }

    const finaliseBooking = (bookingData) => {
        dispatch(addBooking(bookingData));
        setInstructionData(instructionInitData);
        setBookingModalStatus(false);
        setOptionModalStatus(false);
        resetCars();
        setTripInstructions("");
        setRoundTrip(false);
        resetCars();
    }

    const bookNow = async (tipsValue = '', preview = false) => {
        // Add validation for tripDate 
        // For testing: hardcode tripDate and returnDate
        // const now = new Date();
        // const testTripDate = new Date(now.getTime() + 4 * 60 * 60 * 1000); // 4 hours from now
        // const testReturnDate = new Date(now.getTime() + 6 * 60 * 60 * 1000); // 6 hours from now
        // setTripDate(testTripDate);
        // setReturnDate(testReturnDate);
        // // Use hardcoded values
        // const tripDateToUse = testTripDate;
        // const returnDateToUse = testReturnDate;
        if (!tripDate) {
            Alert.alert(t('alert'), t('please_select_trip_date'));
            return;
        }
        if (tripType === 'ROUND TRIP' && !returnDate) {
            Alert.alert(t('alert'), t('please_select_return_date'));
            return;
        }
        setBookModelLoading(true);
        const addBookingObj = {
            pickup: {
                ...estimatedata.estimate.pickup,
                coords: {
                    lat: estimatedata.estimate.pickup?.lat,
                    lng: estimatedata.estimate.pickup?.lng
                },
                description: estimatedata.estimate.pickup?.add || estimatedata.estimate.pickup?.description
            },
            drop: {
                ...estimatedata.estimate.drop,
                coords: {
                    lat: estimatedata.estimate.drop?.lat,
                    lng: estimatedata.estimate.drop?.lng
                },
                description: estimatedata.estimate.drop?.add || estimatedata.estimate.drop?.description
            },
            carDetails: estimatedata.estimate.carDetails,
            userDetails: auth.profile,
            estimate: estimatedata.estimate,
            tripdate: tripDate.getTime(),
            // tripdate: tripDateToUse.getTime(),
            bookLater: true,
            settings: settings,
            booking_type_admin: false,
            deliveryWithBid: deliveryWithBid ? deliveryWithBid : false,
            payment_mode: radioProps[payment_mode].cat,
            flightNumber: flightNumber,
            tips: tipsValue,
            noteText: noteText,
            roundTrip: tripType === 'ROUND TRIP' ? 'true' : 'false',
            returnDate: tripType === 'ROUND TRIP' ? returnDate.getTime() : null, 
            // returnDate: tripType === 'ROUND TRIP' ? returnDateToUse.getTime() : null,
            returnFlightNumber: tripType === 'ROUND TRIP' ? returnFlightNumber : null,
            ...(tripType === 'ROUND TRIP' && {
                returnDate: returnDate.getTime(),
                // returnDate: returnDateToUse.getTime(),
            }),
        };
        if (auth && auth.profile && auth.profile.firstName && auth.profile.firstName.length > 0 && auth.profile.lastName && auth.profile.lastName.length > 0 && auth.profile.email && auth.profile.email.length > 0) {
            const result = await validateBookingObj(t, addBookingObj, instructionData, settings, bookingType, roundTrip, tripInstructions, tripdata, drivers, otherPerson);
            if (result.error) {
                Alert.alert(
                    t('alert'),
                    result.msg,
                    [
                        { text: t('ok'), onPress: () => { setBookModelLoading(false) } }
                    ],
                    { cancelable: true }
                );
            } else {
                props.navigation.navigate('FinalConfirm', { bookingData: result.addBookingObj });
                setBookModelLoading(false);
            }
        } else {
            setBookModelLoading(true);
            const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            if (/\S/.test(profileData.firstName) && /\S/.test(profileData.lastName) && auth) {
                const userData = {
                    firstName: profileData.firstName,
                    lastName: profileData.lastName
                }
                if (auth.profile.email) {
                    const result = await validateBookingObj(t, addBookingObj, instructionData, settings, bookingType, roundTrip, tripInstructions, tripdata, drivers, otherPerson);
                    let bookingData = result.addBookingObj;
                    bookingData.userDetails.firstName = profileData.firstName;
                    bookingData.userDetails.lastName = profileData.lastName;
                    setBookingOnWait(bookingData);
                    setTimeout(() => {
                        dispatch(updateProfile(userData));
                    }, 200)
                } else {
                    if (re.test(profileData.email)) {
                        checkUserExists({ email: profileData.email }).then(async (res) => {
                            if (res.users && res.users.length > 0) {
                                Alert.alert(t('alert'), t('user_exists'));
                                setBookModelLoading(false);
                            } else if (res.error) {
                                Alert.alert(t('alert'), t('email_or_mobile_issue'));
                                setBookModelLoading(false);
                            } else {
                                const result = await validateBookingObj(t, addBookingObj, instructionData, settings, bookingType, roundTrip, tripInstructions, tripdata, drivers, otherPerson);
                                if (result.error) {
                                    Alert.alert(
                                        t('alert'),
                                        result.msg,
                                        [
                                            { text: t('ok'), onPress: () => { setBookModelLoading(false) } }
                                        ],
                                        { cancelable: true }
                                    );
                                } else {
                                    profileData['uid'] = auth.profile.uid;
                                    let bookingData = result.addBookingObj;
                                    bookingData.userDetails.firstName = profileData.firstName;
                                    bookingData.userDetails.lastName = profileData.lastName;
                                    bookingData.userDetails.email = profileData.email;
                                    setBookingOnWait(bookingData);
                                    setTimeout(() => {
                                        dispatch(updateProfileWithEmail(profileData));
                                    }, 200)
                                }
                            }
                        });
                    } else {
                        Alert.alert(t('alert'), t('proper_email'));
                        setBookModelLoading(false);
                    }
                }
            } else {
                Alert.alert(t('alert'), t('proper_input_name'));
                setBookModelLoading(false);
            }
        }
        if (preview) {
            return addBookingObj;
        }
    };

    useEffect(() => {
        const unsubscribe = props.navigation.addListener('focus', () => {
            pageActive.current = true;
            dispatch(fetchDrivers('app'));
            if (intVal.current == 0) {
                intVal.current = setInterval(() => {
                    dispatch(fetchDrivers('app'));
                }, 30000);
            }
        });
        return unsubscribe;
    }, [props.navigation, intVal.current]);

    useEffect(() => {
        const unsubscribe = props.navigation.addListener('blur', () => {
            pageActive.current = false;
            intVal.current ? clearInterval(intVal.current) : null;
            intVal.current = 0;
        });
        return unsubscribe;
    }, [props.navigation, intVal.current]);

    useEffect(() => {
        pageActive.current = true;
        const interval = setInterval(() => {
            dispatch(fetchDrivers('app'));
        }, 30000);
        intVal.current = interval;
        return () => {
            clearInterval(interval);
            intVal.current = 0;
        };
    }, []);

    useFocusEffect(
        React.useCallback(() => {
            // Reset modal state when Reserve screen is focused
            setBookingModalStatus(false);
        }, [])
    );

    const changePermission = async () => {
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status != 'granted') {
            if (Platform.OS == 'ios') {
                Linking.openSettings()
            } else {
                startActivityAsync(ActivityAction.LOCATION_SOURCE_SETTINGS);
            }
        }
    }
    const onTermAccept = () => {
        if (checkTerm == false) {
            dispatch(updateProfile({ term: true }));
        }
    }
    const onTermLink = async () => {
        Linking.openURL(settings.CompanyTermCondition).catch(err => console.error("Couldn't load page", err));
    }

    const onMapSelectComplete = () => {
        if ((tripdata.pickup && tripdata.pickup.source == 'mapSelect') || (tripdata.drop && tripdata.drop.source == 'mapSelect')) {
            if (tripdata.selected === 'pickup') {
                dispatch(updateTripPickup({ ...tripdata.pickup, source: "region-change" }))
            } else {
                dispatch(updateTripDrop({ ...tripdata.drop, source: "region-change" }))
            }
        }
    }

    useEffect(() => {
        if (!farelistdata) {
            dispatch(api.fetchFare());
        }
    }, [dispatch, farelistdata]);

    function isPickupAtORDorMDW() {
        const pickup = tripdata.pickup;
        if (!pickup) return false;

        const addr = (pickup.add || pickup.description || (pickup.placeDetails && pickup.placeDetails.formatted_address) || '').toLowerCase();
        if (addr.includes("o'hare") || addr.includes('ohare') || addr.includes('ord') || addr.includes('midway') || addr.includes('mdw')) {
            return true;
        }

        if (pickup.lat && pickup.lng) {
            const ord = { lat: 41.9742, lng: -87.9073 };
            const mdw = { lat: 41.7868, lng: -87.7522 };
            const d1 = GetDistance(pickup.lat, pickup.lng, ord.lat, ord.lng);
            const d2 = GetDistance(pickup.lat, pickup.lng, mdw.lat, mdw.lng);
            return d1 <= 5 || d2 <= 5;
        }

        return false;
    }

    return (
        <View style={styles.container}>
            <StatusBar hidden={true} />

            {/* Common Menu Bar */}
            {/* <CommonMenuBar /> */}
            <ScrollView
                contentContainerStyle={{ padding: 20 }}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
            >
                <Text style={{ color: colors.HEADER, fontFamily: fonts.Bold, fontSize: 20, alignSelf: 'center', marginTop: Platform.OS == 'android' ? (__DEV__ ? 10 : 10) : (hasNotch ? 48 : 20) }}>{t("book_ride")}</Text>

                <View style={[styles.addressBar, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <View style={styles.ballandsquare}>
                        {((tripdata.pickup && tripdata.pickup.source == 'mapSelect') || (tripdata.drop && tripdata.drop.source == 'mapSelect')) ?
                            (tripdata.pickup && tripdata.pickup.source == 'mapSelect') ? <View style={styles.hbox1} /> : <View style={styles.hbox3} /> :
                            <><View style={styles.hbox1} /><View style={styles.hbox2} /><View style={styles.hbox3} /></>
                        }
                    </View>

                    {((tripdata.pickup && tripdata.pickup.source == 'mapSelect') || (tripdata.drop && tripdata.drop.source == 'mapSelect')) ?
                        <View style={[styles.addressStyle1, isRTL ? { paddingRight: 10 } : { paddingLeft: 10 }, { borderBottomWidth: 0 }]}>
                            {(tripdata.pickup && tripdata.pickup.source == 'mapSelect') ?
                                <Text numberOfLines={1} style={[styles.textStyle, tripdata.selected == 'pickup' ? { fontSize: 18 } : { fontSize: 14 }, { textAlign: isRTL ? "right" : "left" }]}>{tripdata.pickup && tripdata.pickup.add ? tripdata.pickup.add : t('map_screen_where_input_text')}</Text>
                                :
                                <Text numberOfLines={1} style={[styles.textStyle, tripdata.selected == 'drop' ? { fontSize: 18 } : { fontSize: 14 }, { textAlign: isRTL ? "right" : "left" }]}>{tripdata.drop && tripdata.drop.add ? tripdata.drop.add : t('map_screen_drop_input_text')}</Text>
                            }
                        </View>
                        :
                        <View style={[styles.contentStyle, isRTL ? { paddingRight: 10 } : { paddingLeft: 10 }, { height: 100 }]}>
                            <TouchableOpacity onPress={() => tapAddress('pickup')} style={styles.addressStyle1}>
                                <Text numberOfLines={1} style={[styles.textStyle, tripdata.selected == 'pickup' ? { fontSize: 18 } : { fontSize: 14 }, { textAlign: isRTL ? "right" : "left" }]}>{tripdata.pickup && tripdata.pickup.add ? tripdata.pickup.add : t('map_screen_where_input_text')}</Text>
                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => tapAddress('drop')} style={styles.addressStyle2}>
                                <Text numberOfLines={1} style={[styles.textStyle, tripdata.selected == 'drop' ? { fontSize: 18 } : { fontSize: 14 }, { textAlign: isRTL ? "right" : "left" }]}>{tripdata.drop && tripdata.drop.add ? tripdata.drop.add : t('map_screen_drop_input_text')}</Text>
                            </TouchableOpacity>
                        </View>
                    }
                </View>
                <Text style={{ marginTop: 14, fontFamily: fonts.Bold }}>{t('car_type')}</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginVertical: 10 }}>
                    {allCarTypes.map((car, idx) => (
                        <TouchableOpacity
                            key={car.name}
                            style={{
                                padding: 10,
                                marginRight: 10,
                                borderWidth: selectedCarType && selectedCarType.name === car.name ? 2 : 1,
                                borderColor: selectedCarType && selectedCarType.name === car.name ? MAIN_COLOR : colors.BORDER_BACKGROUND,
                                borderRadius: 8,
                                backgroundColor: colors.WHITE
                            }}
                            onPress={() => {
                                setSelectedCarType(car);
                                selectCarType(car, idx);
                            }}
                        >
                            <Text style={{ fontFamily: fonts.Medium }}>{car.displayName || car.name}</Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
                <Text style={{ marginTop: 15, fontFamily: fonts.Bold }}>{t('Trip_Type')}</Text>
                <View style={{ marginVertical: 10, borderWidth: 1, borderColor: colors.BORDER_BACKGROUND, borderRadius: 8, backgroundColor: colors.WHITE }}>
                    <Picker
                        selectedValue={tripType}
                        onValueChange={(itemValue) => {
                            setTripType(itemValue);
                            setRoundTrip(itemValue === 'ROUND TRIP');
                        }}
                        style={{ height: 50, width: '100%' }}
                        itemStyle={{ fontFamily: fonts.Medium }}
                    >
                        <Picker.Item label={t('ONE_WAY_TRIP')} value="ONE WAY TRIP" />
                        <Picker.Item label={t('ROUND_TRIP')} value="ROUND TRIP" />
                    </Picker>
                </View>

                <Text style={{ fontFamily: fonts.Bold, marginTop: 10 }}>{t('Trip_Date_&_Time')}</Text>
                <TouchableOpacity
                    style={{
                        borderWidth: 1,
                        borderColor: colors.BORDER_BACKGROUND,
                        borderRadius: 8,
                        padding: 10,
                        marginBottom: 10,
                        backgroundColor: colors.WHITE
                    }}
                    onPress={() => setActiveDatePicker('trip')}
                >
                    <Text style={{ fontFamily: fonts.Regular }}>
                        {tripDate ? new Date(tripDate).toLocaleString() : t('Select_Trip_Date_&_Time')}
                    </Text>
                </TouchableOpacity>

                {tripType === 'ROUND TRIP' && (
                    <>
                        <Text style={{ fontFamily: fonts.Bold, marginTop: 10 }}>{t('ReturnDate')}</Text>
                        <TouchableOpacity
                            style={{
                                borderWidth: 1,
                                borderColor: colors.BORDER_BACKGROUND,
                                borderRadius: 8,
                                padding: 10,
                                marginBottom: 10,
                                backgroundColor: colors.WHITE
                            }}
                            onPress={() => setActiveDatePicker('return')}
                        >
                            <Text style={{ fontFamily: fonts.Regular }}>
                                {returnDate ? new Date(returnDate).toLocaleString() : t('select_return_date')}
                            </Text>
                        </TouchableOpacity>
                    </>
                )}

                <TextInput
                    style={styles.textInput}
                    placeholder={t('Flight_Number')}
                    value={flightNumber}
                    onChangeText={setFlightNumber}
                />

                {tripType === 'ROUND TRIP' && (
                    <TextInput
                        style={styles.textInput}
                        placeholder={t('Return_Flight_Number')}
                        value={returnFlightNumber}
                        onChangeText={setReturnFlightNumber}
                    />
                )}

                <TextInput
                    style={[styles.textInput, { minHeight: 80, textAlignVertical: 'top' }]}
                    placeholder={t('Note')}
                    value={noteText}
                    onChangeText={setNoteText}
                    multiline={true}
                    numberOfLines={4}
                />

                <View style={{ flex: 1, margin: 2, borderRadius: 10, height: 55 }}>
                    <Button
                        title={t('book_later')}
                        loading={bookLaterLoading}
                        loadingColor={{ color: colors.WHITE }}
                        buttonStyle={styles.buttonTitleStyle}
                        btnClick={onPressBookLater}
                        style={{ backgroundColor: MAIN_COLOR, height: '100%', padding: 2 }}
                    />
                </View>
                {/* <View style={[styles.buttonBar, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    {bookLoading || bookLaterLoading ?
                        <View style={{ flex: 1, borderRadius: 10, height: 55, margin: 3, justifyContent: 'center', borderWidth: 1, borderColor: MAIN_COLOR }}>
                            <ActivityIndicator color={MAIN_COLOR} size='large' />
                        </View>
                        :
                        <View style={[styles.buttonBar, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                            <View style={{ flex: 1, margin: 2, borderRadius: 10, height: 55 }}>
                                <Button
                                    title={t('book_later')}
                                    loading={bookLaterLoading}
                                    loadingColor={{ color: colors.BLACK }}
                                    buttonStyle={styles.buttonTitleStyle}
                                    btnClick={onPressBookLater}
                                    style={{ backgroundColor: SECONDORY_COLOR, height: '100%',width: "90%" , padding: 2 }}
                                /> 
                            </View>
                        </View>
                    }
                </View>   */}
                {/* <View style={styles.menuIcon}>
                    <ImageBackground source={require('../../assets/images/white-grad6.png')} style={{ height: '100%', width: '100%' }}>
                        <Text style={{ color: colors.HEADER, fontFamily: fonts.Bold, fontSize: 20, alignSelf: 'center', marginTop: Platform.OS == 'android' ? (__DEV__ ? 20 : 40) : (hasNotch ? 48 : 20) }}>{((tripdata.pickup && tripdata.pickup.source == 'mapSelect') || (tripdata.drop && tripdata.drop.source == 'mapSelect')) ? t("drag_map") : t("book_ride")}</Text>
                    </ImageBackground>
                </View> */}

            </ScrollView>

            <OptionModal
                settings={settings}
                tripdata={tripdata}
                instructionData={instructionData}
                optionModalStatus={optionModalStatus}
                onPressCancel={onModalCancel}
                handleGetEstimate={handleGetEstimate}
                handleParcelTypeSelection={handleParcelTypeSelection}
                handleOptionSelection={handleOptionSelection}
            />
            <BookingModal
                settings={settings}
                tripdata={tripdata}
                estimate={estimatedata.estimate}
                instructionData={instructionData}
                setInstructionData={setInstructionData}
                tripInstructions={tripInstructions}
                setTripInstructions={setTripInstructions}
                roundTrip={roundTrip}
                setRoundTrip={setRoundTrip}
                bookingModalStatus={bookingModalStatus}
                bookNow={(tipsValue) => bookNow(tipsValue, true)}
                onPressCancel={onModalCancel}
                payment_mode={payment_mode}
                setPaymentMode={setPaymentMode}
                radioProps={radioProps}
                profileData={profileData}
                setProfileData={setProfileData}
                auth={auth}
                bookModelLoading={bookModelLoading}
                deliveryWithBid={deliveryWithBid}
                setDeliveryWithBid={setDeliveryWithBid}
                otherPerson={otherPerson}
                setOtherPerson={setOtherPerson}
                navigation={props.navigation}
            />
            <DatePicker
                modal
                open={!!activeDatePicker}
                date={
                    activeDatePicker === 'trip'
                        ? (tripDate ? new Date(tripDate) : new Date())
                        : (returnDate ? new Date(returnDate) : new Date())
                }
                onConfirm={date => {
                    if (activeDatePicker === 'trip') setTripDate(date);
                    if (activeDatePicker === 'return') setReturnDate(date);
                    setActiveDatePicker(null);
                }}
                onCancel={() => setActiveDatePicker(null)}
                minimumDate={new Date()}
                theme='light'
                mode="datetime"
            />
        </View>
    );

}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    menuIcon: {
        height: 100,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: 0,
    },
    menuIconButton: {
        flex: 1,
        height: 50,
        width: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center'
    },
    topTitle: {
        height: 50,
        width: 165,
        backgroundColor: colors.WHITE,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,
        elevation: 2,
        borderTopRightRadius: 25,
        borderBottomRightRadius: 25,
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        bottom: 180
    },
    topTitle1: {
        height: 50,
        width: 165,
        backgroundColor: colors.WHITE,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,
        elevation: 2,
        borderTopLeftRadius: 25,
        borderBottomLeftRadius: 25,
        justifyContent: 'center',
        position: 'absolute',
        right: 0,
        bottom: 180
    },
    mapcontainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    mapViewStyle: {
        flex: 1,
        ...StyleSheet.absoluteFillObject,
    },
    mapFloatingPinView: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'transparent'
    },
    mapFloatingPin: {
        height: 40
    },
    buttonBar: {
        height: 70,
        width: width,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    buttonContainer: {
        height: 50,
        borderRadius: 10
    },
    buttonStyle: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center'
    },
    buttonTitleStyle: {
        fontFamily: fonts.Bold,
        fontSize: 14,
        color: '#fff',
    },
    locationButtonView: {
        position: 'absolute',
        height: Platform.OS == 'ios' ? 55 : 42,
        width: Platform.OS == 'ios' ? 55 : 42,
        bottom: 180,
        right: 10,
        backgroundColor: '#fff',
        borderRadius: Platform.OS == 'ios' ? 30 : 3,
        elevation: 2,
        shadowOpacity: 0.3,
        shadowRadius: 3,
        shadowOffset: {
            height: 0,
            width: 0
        },
    },
    locateButtonStyle: {
        height: Platform.OS == 'ios' ? 55 : 42,
        width: Platform.OS == 'ios' ? 55 : 42,
        alignItems: 'center',
        justifyContent: 'center',
    },
    addressBar: {
        marginTop: 20,
        backgroundColor: colors.WHITE,
        paddingLeft: 10,
        paddingRight: 10,
        shadowColor: 'black',
        shadowOffset: { width: 2, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 5,
        borderRadius: 8,
        elevation: 3
    },
    ballandsquare: {
        width: 12,
        alignItems: 'center',
        justifyContent: 'center'
    },
    hbox1: {
        height: 12,
        width: 12,
        borderRadius: 6,
        backgroundColor: colors.GREEN_DOT
    },
    hbox2: {
        height: 36,
        width: 1,
        backgroundColor: colors.MAP_TEXT
    },
    hbox3: {
        height: 12,
        width: 12,
        backgroundColor: colors.DULL_RED
    },
    contentStyle: {
        justifyContent: 'center',
        width: width - 74
    },
    addressStyle1: {
        borderBottomColor: colors.BLACK,
        borderBottomWidth: 1,
        height: 48,
        width: width - 84,
        justifyContent: 'center',
        paddingTop: 2
    },
    addressStyle2: {
        height: 48,
        width: width - 84,
        justifyContent: 'center',
    },
    textStyle: {
        fontFamily: fonts.Regular,
        fontSize: 14,
        color: '#000'
    },
    fullCarView: {
        position: 'absolute',
        bottom: 60,
        width: width - 10,
        height: 170,
        marginLeft: 5,
        marginRight: 5,
        alignItems: 'center',
        marginBottom: 10
    },
    fullCarScroller: {
        width: width - 10,
        height: 160,
        flexDirection: 'row'
    },
    cabDivStyle: {
        backgroundColor: colors.WHITE,
        width: (width - 40) / 3,
        height: '95%',
        alignItems: 'center',
        marginHorizontal: 5,
        shadowColor: 'black',
        shadowOffset: { width: 0, height: 5 },
        shadowOpacity: 0.5,
        shadowRadius: 3,
        borderRadius: 8,
        elevation: 3
    },
    imageStyle: {
        height: 50,
        width: '100%',
        marginVertical: 15,
        padding: 5,
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center',
        paddingBottom: 5
    },
    imageStyle1: {
        height: 40,
        width: 50 * 1.8
    },
    textViewStyle: {
        height: 50,
        alignItems: 'center',
        flexDirection: 'column',
        justifyContent: 'center',
    },
    text1: {
        fontFamily: fonts.Bold,
        fontSize: 14,
        color: colors.BLACK
    },
    text2: {
        fontFamily: fonts.Bold,
        fontSize: 11,
        color: colors.BORDER_TEXT
    },
    carShow: {
        width: '100%',
        justifyContent: 'center',
        backgroundColor: colors.BACKGROUND_PRIMARY,
        position: 'absolute',
        bottom: 60,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        alignItems: 'center'
    },
    bar: {
        width: 100,
        height: 6
    },
    carContainer: {
        justifyContent: "space-between",
        width: width - 30,
        minHeight: 70,
        marginBottom: 5,
        marginLeft: 15,
        marginRight: 15,
        backgroundColor: colors.WHITE,
        borderRadius: 6,
        borderWidth: 1,
        borderColor: colors.BORDER_BACKGROUND,
        alignItems: 'center',
        shadowColor: colors.BLACK,
        shadowOffset: {
            width: 0,
            height: 3,
        },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
    },
    bodyContent: {
        flex: 1
    },
    titleStyles: {
        fontSize: 14,
        color: colors.HEADER,
        paddingBottom: 2,
        fontFamily: fonts.Bold
    },
    subtitleStyle: {
        fontSize: 12,
        color: colors.BALANCE_ADD,
        lineHeight: 16,
        paddingBottom: 2
    },
    priceStyle: {
        color: colors.BALANCE_ADD,
        fontFamily: fonts.Bold,
        fontSize: 12,
        lineHeight: 14,
    },
    cardItemImagePlace: {
        width: 70,
        height: 45,
        margin: 5,
        resizeMode: 'contain'
    },
    cardItemImageBox: {
        width: 80,
        height: 50,
        margin: 10,
        resizeMode: 'center'
    },
    alrt1: {
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: "70%",
    },
    alrt: {
        width: width - 40,
        height: 60,
        padding: 10,
        borderWidth: 1,
        borderColor: colors.BORDER_BACKGROUND,
        borderRadius: 5,
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    checkButtonStyle: {
        backgroundColor: colors.GREEN,
        width: 85,
        height: 40,
        borderColor: colors.TRANSPARENT,
        borderWidth: 0,
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center'
    },
    checkButtonTitle: {
        fontSize: 12,
        fontFamily: fonts.Medium,
        color: colors.WHITE
    },
    textInput: {
        borderWidth: 1,
        borderColor: colors.BORDER_BACKGROUND,
        borderRadius: 8,
        padding: 10,
        marginBottom: 10,
        fontFamily: fonts.Regular,
        fontSize: 16,
        backgroundColor: colors.WHITE
    },
});