import i18n from 'i18n-js';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    StyleSheet,
    View
} from 'react-native';
import { useSelector } from 'react-redux';
import { colors } from '../common/theme';
import { RideList } from '../components';
export default function RideListPage(props) {
    const bookings = useSelector(state => state.bookinglistdata.bookings);
    const settings = useSelector(state => state.settingsdata.settings);
    const auth = useSelector(state => state.auth);
    const fromBooking  = props.route.params?props.route.params: null;
    const [bookingData,setBookingData] = useState([]);
    const initialTabIndex = props.route.params && typeof props.route.params.tabIndex === 'number'
        ? props.route.params.tabIndex
        : 0;
    const [tabIndex, setTabIndex] = useState(initialTabIndex);
    const { t } = i18n;
    useEffect(()=>{
        if(bookings){
            setBookingData(bookings);
            if(fromBooking){
                const lastStatus = bookings[0].status;
                if(!(lastStatus == 'COMPLETE' && lastStatus == 'CANCELLED')) setTabIndex(0);
                if(lastStatus == 'COMPLETE') setTabIndex(1);
                if(lastStatus == 'CANCELLED') setTabIndex(2);
            }else{
                setTabIndex(0);
            }
        }else{
            setBookingData([]);
            setTabIndex(0);
        }
    },[bookings]);

    useEffect(() => {
        if (props.route.params && typeof props.route.params.tabIndex === 'number') {
            setTabIndex(props.route.params.tabIndex);
        }
    }, [props.route.params]);

    const goDetails = (item, index) => {
        if (item && item.trip_cost > 0) {
            item.roundoffCost = Math.round(item.trip_cost).toFixed(settings.decimal);
            item.roundoff = (Math.round(item.roundoffCost) - item.trip_cost).toFixed(settings.decimal);
            const isDriver = auth.profile && auth.profile.usertype === 'driver';
            const isPutOutTab = isDriver && tabIndex === 3;
            if (isPutOutTab) {
                props.navigation.push('RideDetails', { data: item, fromPutOutTab: true });
            } else {
                props.navigation.push('RideDetails', { data: item });
            }
        }else {

            item.roundoffCost = Math.round(item.estimate).toFixed(settings.decimal);
            item.roundoff = (Math.round(item.roundoffCost) - item.estimate).toFixed(settings.decimal);
            const isDriver = auth.profile && auth.profile.usertype === 'driver';
            const isPutOutTab = isDriver && tabIndex === 3;
            if (isPutOutTab) {
                props.navigation.push('RideDetails', { data: item, fromPutOutTab: true });
            } else {
                props.navigation.push('RideDetails', { data: item });
            }
        }
    }

    const goAction = (item, index) => {
        if(item.status == 'PAYMENT_PENDING'){
            props.navigation.navigate('PaymentDetails', { booking: item });
        }else {
            props.navigation.push('BookedCab',{bookingId:item.id});
        }
    }


    const chatAction = (item) => {
        if(['PAYMENT_PENDING','NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(item.status) != -1){
            props.navigation.navigate("onlineChat", { bookingId: item.id })
        } else {
            Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_chat'));
        }
    }

    const goHome = () => {
        props.navigation.push('TabRoot');
    }

    return (
        <View style={styles.mainView}>
            {tabIndex>=0?
                <RideList
                    navigation={props.navigation}
                    tabIndex={tabIndex}
                    onPressButton={(item, index) => { goDetails(item, index) }}
                    goHome={goHome}
                    data={bookingData}
                    onPressAction={(item, index) => { goAction(item, index) }}
                    onChatAction={(item, index) => { chatAction(item, index) }}
                />
            :null}
        </View>
    );

}

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        backgroundColor: colors.WHITE,
    }
});
