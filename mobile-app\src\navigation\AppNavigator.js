import { Ionicons } from '@expo/vector-icons';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { CommonActions, NavigationContainer, useNavigationContainerRef } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import * as Notifications from 'expo-notifications';
import i18n from 'i18n-js';
import React, { useEffect, useRef } from 'react';
import {
    Dimensions,
    Platform,
} from 'react-native';
import { Icon } from "react-native-elements";
import { useSelector } from "react-redux";
import { fonts } from '../common/font';
import { MAIN_COLOR } from '../common/sharedFunctions';
import { colors } from '../common/theme';
import {
    AboutPage,
    AddMoneyScreen,
    AddPassengersPage,
    BookedCabScreen,
    BookingPage,
    CarEditScreen,
    CarsScreen,
    DriverIncomeScreen,
    DriverRating,
    DriverTrips,
    EditProfilePage,
    HomeScreen,
    LoginScreen,
    MapScreen,
    Notifications as NotificationsPage,
    OnlineChat,
    PaymentDetails,
    PaymentScreen,
    PayNowScreen,
    ProfileScreen,
    RatesScreen,
    RegistrationPage,
    Reserve,
    RideDetails,
    RideListPage,
    SearchScreen,
    SelectGatewayPage,
    SettingsScreen,
    WithdrawMoneyScreen
} from '../screens';
import Complain from '../screens/Complain';
import FinalConfirmScreen from '../screens/FinalConfirmScreen';
var { height, width } = Dimensions.get('window');

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

export default function AppContainer() {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    const auth = useSelector(state => state.auth);
    const responseListener = useRef();
    const navigationRef = useNavigationContainerRef();
    const activeBookings = useSelector(state => state.bookinglistdata.active);
    const tasks = useSelector(state => state.taskdata.tasks);
    const hasNavigatedToActiveRide = useRef(false); // Add this ref to prevent multiple navigations

    let assignedCount = 0;
    let activeBookingCount = 0;

    if (auth.profile && auth.profile.usertype === 'driver') {
        assignedCount = tasks ? tasks.filter(item => item.status === 'NEW').length : 0;
        activeBookingCount = activeBookings ? activeBookings.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')).length : 0;
    } else {
        activeBookingCount = activeBookings ? activeBookings.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')).length : 0;
    }
    const rideListBadge = assignedCount + activeBookingCount;

    // Add this useEffect to handle automatic navigation to active ride
    useEffect(() => {
        // Only proceed if user is authenticated and we haven't already navigated
        if (auth.profile && auth.profile.uid && !hasNavigatedToActiveRide.current && activeBookings) {
            // Get active rides (not cancelled or complete)
            const activeRides = activeBookings.filter(item => 
                !(item.status === 'CANCELLED' || item.status === 'COMPLETE' || item.status === 'PENDING')
            );  
            
            // If there are active rides, navigate to the first one
            if (activeRides.length > 0) {
                hasNavigatedToActiveRide.current = true;
                const firstActiveRide = activeRides[0];
                
                // Navigate to BookedCabScreen with the first active ride
                navigationRef.navigate('BookedCab', { bookingId: firstActiveRide.id });
            }
        }
    }, [auth.profile, activeBookings, navigationRef]);

    // Reset the navigation flag when user logs out
    useEffect(() => {
        if (!auth.profile || !auth.profile.uid) {
            hasNavigatedToActiveRide.current = false;
        }
    }, [auth.profile]);

    useEffect(() => {
      responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
          if (response && response.notification && response.notification.request && response.notification.request.content && response.notification.request.content.data){
            const nData = response.notification.request.content.data;
            if (nData.screen) {
              if (nData.params) {
                navigationRef.navigate(nData.screen, nData.params);
              } else {
                navigationRef.navigate(nData.screen);
              }
            } else {
              navigationRef.navigate("TabRoot");
            }
          }
        });
    }, []);
    
    const hasNotch = Platform.OS === 'ios' && !Platform.isPad && !Platform.isTVOS && ((height === 780 || width === 780) || (height === 812 || width === 812) || (height === 844 || width === 844) || (height === 852 || width === 852) || (height === 896 || width === 896) || (height === 926 || width === 926) || (height === 932 || width === 932))
    const screenOptions = {
        headerStyle: {
          backgroundColor: MAIN_COLOR,
          transform: [{ scaleX: isRTL ? -1 : 1 }]
        },
        headerTintColor: colors.TRANSPARENT,
        headerTitleAlign: 'center',
        headerTitleStyle: {
            fontFamily:fonts.Bold,
          color:'white',
          transform: [{ scaleX: isRTL ? -1 : 1 }]
        },
        headerBackImage: () => 
        <Icon
            name={isRTL?'arrow-right':'arrow-left'}
            type='font-awesome'
            color={colors.WHITE}
            size={25}
            style={{margin:10, transform: [{ scaleX: isRTL ? -1 : 1 }]}}
        /> 
    };
    
    const TabRoot = () => {
        return (
            <Tab.Navigator
                screenOptions={({ route }) => ({
                    animationEnabled: Platform.OS == 'android'? false : true,
                    tabBarIcon: ({ focused, color, size }) => {
                        let iconName;
                        if (route.name === 'Home') {
                            iconName = focused
                              ? 'home'
                              : 'home-outline';
                        } else if (route.name === 'Map') {
                            iconName = focused
                              ? 'car'
                              : 'car-outline';
                        } else if (route.name === 'DriverTrips') {
                            iconName = focused
                              ? 'home'
                              : 'home-outline';
                        } else if (route.name === 'RideList') {
                            iconName = focused ? 'list-circle' : 'list-circle-outline';
                        } else if (route.name === 'Wallet') {
                            iconName = focused ? 'wallet' : 'wallet-outline';
                        } else if (route.name === 'Settings') {
                            iconName = focused ? 'settings' : 'settings-outline';
                        }
                        return <Ionicons name={iconName} size={size} color={color} />;
                    },
                    tabBarActiveTintColor: MAIN_COLOR,
                    tabBarInactiveTintColor: colors.HEADER,
                    tabBarBadge: route.name == 'RideList' && rideListBadge > 0 ? rideListBadge : null,
                    tabBarBadgeStyle:{transform: [{ scaleX: isRTL ? -1 : 1 }]},
                    tabBarIndicatorStyle: {
                        borderBottomColor: '#C2D5A8',
                        borderBottomWidth: 2,
                      },
                    tabBarStyle: { height: hasNotch ? 80:55,transform:isRTL? [{scaleX: -1}] :[{scaleX: 1}]},
                    tabBarLabelStyle:{ fontSize: 14,fontFamily:fonts.Light,transform:isRTL? [{scaleX: -1}] :[{scaleX: 1}]},
                })}
            >
                {auth.profile && auth.profile.usertype && auth.profile.usertype == 'customer' ?
                    <Tab.Screen name="Home" 
                        component={HomeScreen} 
                        options={{title: t('home'),headerShown: false}}
                        listeners={({navigation,route})=>({
                            tabPress: e => {
                                e.preventDefault()
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{name: route.name}]
                                    })
                                )
                            },
                        })}
                    />
                : null}
                {auth.profile && auth.profile.usertype && auth.profile.usertype == 'customer' ?
                    <Tab.Screen name="Map" 
                        component={RatesScreen} 
                        options={{title: 'Rates',headerShown: false}}
                        listeners={({navigation,route})=>({
                            tabPress: e => {
                                e.preventDefault()
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{name: route.name}]
                                    })
                                )
                            },
                        })}
                    />
                : null}
                {auth.profile && auth.profile.usertype && auth.profile.usertype == 'driver' ?
                    <Tab.Screen 
                        name="DriverTrips" 
                        component={DriverTrips} 
                        options={{ title: t('task_list'),...screenOptions }}
                        listeners={({navigation,route})=>({
                            tabPress: e => {
                                e.preventDefault()
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{name: route.name}]
                                    })
                                )
                            },
                        })}
                    />
                : null}
                <Tab.Screen name="RideList"
                    component={RideListPage} 
                    options={{ title: t('ride_list_title'),...screenOptions }}
                    listeners={({navigation,route})=>({
                        tabPress: e => {
                            e.preventDefault()
                            navigation.dispatch(
                                CommonActions.reset({
                                    index: 0,
                                    routes: [{name: route.name}]
                                })
                            )
                        },
                    })}
                />
                {/* <Tab.Screen name="Wallet" 
                    component={WalletDetails} 
                    options={{ title: t('my_wallet_tile'),...screenOptions }}
                    listeners={({navigation,route})=>({
                        tabPress: e => {
                            e.preventDefault()
                            navigation.dispatch(
                                CommonActions.reset({
                                    index: 0,
                                    routes: [{name: route.name}]
                                })
                            )
                        },
                    })}
                /> */}
                {auth.profile && auth.profile.usertype && auth.profile.usertype == 'driver' ?
                    <Tab.Screen name="Settings" 
                        component={SettingsScreen} 
                        options={{ title: t('settings_title') ,...screenOptions}}
                        listeners={({navigation,route})=>({
                            tabPress: e => {
                                e.preventDefault()
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{name: route.name}]
                                    })
                                )
                            },
                        })}
                    />
                : null}
            </Tab.Navigator>
        );
    }

    return (
        <NavigationContainer ref={navigationRef}>
            <Stack.Navigator
                screenOptions={{
                    animationTypeForReplace: 'pop',
                    animationEnabled:   Platform.OS == 'android'? false: true,
                }}
            >
                {auth.profile && auth.profile.uid ?
                    <Stack.Group>
                        <Stack.Screen name="TabRoot" component={TabRoot}  options={{headerShown: false,}}/>
                        <Stack.Screen name="Profile" component={ProfileScreen} options={{ title: t('profile_setting_menu'),...screenOptions}}/>
                        <Stack.Screen name="PayNow" component={PayNowScreen} options={{ title: 'Pay Now',...screenOptions}}/>
                        <Stack.Screen name="Reserve" component={Reserve} options={{ title: 'Reserve',...screenOptions}}/>
                        <Stack.Screen name="Rates" component={RatesScreen} options={{ title: 'Rates',...screenOptions }}/>
                        <Stack.Screen name="editUser" component={EditProfilePage} options={{ title: t('update_profile_title'),...screenOptions }}/>
                        <Stack.Screen name="Search" component={SearchScreen} options={{ title: t('search'),...screenOptions }}/>
                        <Stack.Screen name="DriverRating" component={DriverRating} options={{ title: t('rate_ride'),headerLeft: ()=> null,...screenOptions }}/>
                        <Stack.Screen name="PaymentDetails" component={PaymentDetails} options={{ title: t('payment'),...screenOptions }}/>
                        <Stack.Screen name="PaymentScreen" component={PaymentScreen} options={{ title: t('payment'),...screenOptions }}/>
                        <Stack.Screen name="BookedCab" component={BookedCabScreen} options={{headerShown: false }}/>
                        <Stack.Screen name="RideDetails" component={RideDetails} options={{ title: t('ride_details_page_title'),...screenOptions }}/>
                        <Stack.Screen name="onlineChat" component={OnlineChat} options={{ title: t('chat_title'),...screenOptions }}/>
                        <Stack.Screen name="addMoney" component={AddMoneyScreen} options={{ title: t('add_money'),...screenOptions }}/>
                        <Stack.Screen name="paymentMethod" component={SelectGatewayPage} options={{ title: t('payment'),...screenOptions }}/>
                        <Stack.Screen name="withdrawMoney" component={WithdrawMoneyScreen} options={{ title: t('withdraw_money'),...screenOptions }}/>
                        <Stack.Screen name="About" component={AboutPage} options={{ title: t('about_us_menu'),...screenOptions }}/>
                        <Stack.Screen name="Complain" component={Complain} options={{ title: t('complain'),...screenOptions }}/>
                        <Stack.Screen name="MyEarning" component={DriverIncomeScreen} options={{ title: t('incomeText'),...screenOptions }}/>
                        <Stack.Screen name="Notifications" component={NotificationsPage} options={{ title: t('push_notification_title'),...screenOptions }}/>
                        <Stack.Screen name="Settings" component={SettingsScreen} options={{ title: t('settings_title'),...screenOptions }}/>
                        <Stack.Screen name="Cars" component={CarsScreen} options={{ title: t('cars'),...screenOptions  }}/>
                        <Stack.Screen name="CarEdit" component={CarEditScreen} options={{ title: t('editcar'),...screenOptions  }}/>
                        <Stack.Screen name="BookingPage" component={BookingPage} options={{ title: 'Booking' }} /> 
                        <Stack.Screen name="AddPassengersPage" component={AddPassengersPage} options={{ title: 'AddPassengersPage' }} /> 
                        <Stack.Screen name="PayNowScreen" component={PayNowScreen} options={{ title: 'Pay Now',...screenOptions}}/>
                        <Stack.Screen name="Map" component={MapScreen} options={{ title: 'Map',...screenOptions }}/>
                        <Stack.Screen name="FinalConfirm" component={FinalConfirmScreen} /> 
                    </Stack.Group>
                    :
                    <Stack.Group screenOptions={{ headerShown: false }}>
                        <Stack.Screen name="Login" component={LoginScreen}/>
                        <Stack.Screen name="Register" component={RegistrationPage}/>
                    </Stack.Group>
                }
            </Stack.Navigator>
        </NavigationContainer>
    );
}