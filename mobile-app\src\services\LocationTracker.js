import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import { store } from 'common';
import <PERSON>rror<PERSON>and<PERSON> from '../utils/ErrorHandler';
import { AppState } from 'react-native';

const LOCATION_TASK_NAME = 'background-location-task';
const DEBUG_LOCATION = __DEV__;

class LocationTracker {
  constructor() {
    this.isTrackingActive = false;
    this.currentWatcher = null;
    this.backgroundTaskActive = false;
    this.permissionStatus = null;
    this.lastKnownLocation = null;
    this.appState = AppState.currentState;
    this.appStateSubscription = null;
    // Current effective options for the active watcher/task
    this.currentOptions = null;

    // Conservative defaults; will be adapted dynamically by getDynamicTrackingOptions
    this.trackingOptions = {
      accuracy: Location.Accuracy.Balanced,
      timeInterval: 10000,
      distanceInterval: 25,
      activityType: Location.ActivityType.AutomotiveNavigation,
      // Help the OS batch updates to reduce wakeups
      deferredUpdatesInterval: 30000,
      deferredUpdatesDistance: 50,
    };
    
    this.initializeTaskManager();
    this.initializeAppStateListener();
  }

  initializeTaskManager() {
    // Define the background task
    TaskManager.defineTask(LOCATION_TASK_NAME, ({ data, error }) => {
      try {
        if (error) {
          console.error('[LocationTracker] Background task error:', error);
          this.handleLocationError(error);
          return;
        }

        if (data?.locations && data.locations.length > 0) {
          const location = data.locations[data.locations.length - 1];
          if (location?.coords) {
            this.updateLocationInStore(location);
            // Light-weight adaptive throttling based on speed for background
            this.maybeAdjustBackgroundTracking(location);
          }
        }
      } catch (err) {
        console.error('[LocationTracker] Task execution error:', err);
      }
    });
  }

  initializeAppStateListener() {
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      console.log(`[LocationTracker] App State: ${this.appState} → ${nextAppState}`);
      
      if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
        // App returned to foreground
        this.handleAppForeground();
      } else if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
        // App going to background
        this.handleAppBackground();
      }
      
      this.appState = nextAppState;
    });
  }

  async handleAppForeground() {
    console.log('[LocationTracker] App returned to foreground');
    
    // Small delay to ensure app is fully active
    setTimeout(async () => {
      try {
        const state = store.getState();
        const auth = state.auth;
        
        // If driver is active, ensure tracking is running
        if (auth.profile?.usertype === 'driver' && auth.profile?.driverActiveStatus) {
          console.log('[LocationTracker] Driver active, ensuring tracking is running');
          
          // Check if tracking is actually active
          if (!this.isTrackingActive && !this.backgroundTaskActive) {
            console.log('[LocationTracker] Restarting tracking after foreground return');
            await this.startBackgroundTracking();
          }
        }
      } catch (error) {
        console.error('[LocationTracker] Error handling app foreground:', error);
      }
    }, 2000);
  }

  async handleAppBackground() {
    console.log('[LocationTracker] App going to background');
    
    // For drivers, keep background tracking active
    // For customers, we might want to stop foreground tracking to save resources
    const state = store.getState();
    const auth = state.auth;
    
    if (auth.profile?.usertype === 'customer') {
      // Stop foreground tracking for customers when going to background
      if (this.isTrackingActive) {
        console.log('[LocationTracker] Stopping foreground tracking for customer');
        await this.stopForegroundTracking();
      }
    }
    // For drivers, background tracking should continue
  }

  async checkAndRequestPermissions() {
    try {
      // Check current permission status
      const { status: foregroundStatus } = await Location.getForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
        if (newStatus !== 'granted') {
          throw new Error('Foreground location permission denied');
        }
      }

      // For background location (only request if needed)
      const { status: backgroundStatus } = await Location.getBackgroundPermissionsAsync();
      
      this.permissionStatus = {
        foreground: foregroundStatus === 'granted' ? 'granted' : 'denied',
        background: backgroundStatus === 'granted' ? 'granted' : 'denied'
      };

      if (DEBUG_LOCATION) {
        console.log('[LocationTracker] Permission status:', this.permissionStatus);
      }

      return this.permissionStatus;
    } catch (error) {
      console.error('[LocationTracker] Permission check failed:', error);
      throw error;
    }
  }

  // Choose tracking options based on user role and trip status
  getDynamicTrackingOptions(context = 'foreground') {
    const state = store.getState();
    const auth = state?.auth;
    const bookingState = state?.bookinglistdata;
    const isDriver = auth?.profile?.usertype === 'driver';
    const active = bookingState?.active || bookingState?.tracked || [];
    const current = Array.isArray(active)
      ? active.find(b => ['ACCEPTED','ARRIVED','STARTED'].includes(b.status))
      : active;

    const highPrecisionNeeded = isDriver && current && ['ARRIVED','STARTED'].includes(current.status);

    // Foreground: prefer tighter intervals if driver is navigating a trip
    if (context === 'foreground') {
      return highPrecisionNeeded
        ? {
            accuracy: Location.Accuracy.BestForNavigation,
            timeInterval: 4000,
            distanceInterval: 8,
            activityType: Location.ActivityType.AutomotiveNavigation,
            deferredUpdatesInterval: 15000,
            deferredUpdatesDistance: 25,
          }
        : { ...this.trackingOptions };
    }

    // Background: keep conservative unless actively on trip
    return highPrecisionNeeded
      ? {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000,
          distanceInterval: 10,
          activityType: Location.ActivityType.AutomotiveNavigation,
          deferredUpdatesInterval: 20000,
          deferredUpdatesDistance: 30,
          foregroundService: {
            notificationTitle: 'Live Tracking Active',
            notificationBody: 'Tracking your location for ride service',
            notificationColor: '#0066CC'
          }
        }
      : { ...this.trackingOptions };
  }

  async requestBackgroundPermissions() {
    try {
      const { status } = await Location.requestBackgroundPermissionsAsync();
      this.permissionStatus.background = status === 'granted' ? 'granted' : 'denied';
      return status === 'granted';
    } catch (error) {
      console.error('[LocationTracker] Background permission request failed:', error);
      return false;
    }
  }

  updateLocationInStore(location) {
    try {
      if (!location || !location.coords) {
        return;
      }

      const locationData = {
        lat: location.coords.latitude,
        lng: location.coords.longitude,
        timestamp: location.timestamp,
        accuracy: location.coords.accuracy,
        speed: typeof location.coords.speed === 'number' ? location.coords.speed : null,
      };

      this.lastKnownLocation = locationData;

      // Safely dispatch to store
      if (store && store.dispatch) {
        store.dispatch({
          type: 'UPDATE_GPS_LOCATION',
          payload: locationData
        });
      }

      if (DEBUG_LOCATION) {
        console.log('[LocationTracker] Location updated:', locationData);
      }
    } catch (error) {
      console.error('[LocationTracker] Failed to update location in store:', error);
    }
  }

  handleLocationError(error) {
    const mappedError = ErrorHandler.handleLocationError(error, 'LocationTracker');
    
    if (store && store.dispatch) {
      store.dispatch({
        type: 'UPDATE_GPS_LOCATION',
        payload: { 
          error: true, 
          errorMessage: mappedError.message,
          errorType: mappedError.type,
          userFriendly: mappedError.userFriendly
        }
      });
    }
    
    return mappedError;
  }

  async startForegroundTracking() {
    return await ErrorHandler.safeLocationCall(async () => {
      if (this.isTrackingActive) {
        if (DEBUG_LOCATION) {
          console.log('[LocationTracker] Tracking already active');
        }
        return true;
      }

      await this.checkAndRequestPermissions();
      
      if (this.permissionStatus.foreground !== 'granted') {
        throw new Error('Foreground location permission required');
      }

      // Stop any existing watcher first
      await this.stopForegroundTracking();

      const options = this.getDynamicTrackingOptions('foreground');
      this.currentOptions = options;

      this.currentWatcher = await Location.watchPositionAsync(
        options,
        (location) => {
          try {
            this.updateLocationInStore(location);
            this.maybeAdjustForegroundWatcher(location);
          } catch (updateError) {
            console.error('[LocationTracker] Failed to update location:', updateError);
          }
        }
      );

      this.isTrackingActive = true;

      if (DEBUG_LOCATION) {
        console.log('[LocationTracker] Foreground tracking started');
      }

      return true;
    }, null).catch((error) => {
      this.handleLocationError(error);
      return false;
    });
  }

  async startBackgroundTracking() {
    try {
      if (this.backgroundTaskActive) {
        if (DEBUG_LOCATION) {
          console.log('[LocationTracker] Background tracking already active');
        }
        return true;
      }

      await this.checkAndRequestPermissions();
      
      if (this.permissionStatus.foreground !== 'granted') {
        throw new Error('Foreground location permission required for background tracking');
      }

      // Request background permission if not granted
      if (this.permissionStatus.background !== 'granted') {
        const granted = await this.requestBackgroundPermissions();
        if (!granted) {
          // Fall back to foreground tracking
          return await this.startForegroundTracking();
        }
      }

      // Check if task is already registered
      const registeredTasks = await TaskManager.getRegisteredTasksAsync();
      const isRegistered = registeredTasks.some(task => task.taskName === LOCATION_TASK_NAME);
      
      if (isRegistered) {
        await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
      }

      const bgOptions = this.getDynamicTrackingOptions('background');
      this.currentOptions = bgOptions;

      await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
        ...bgOptions,
        showsBackgroundLocationIndicator: true,
      });

      this.backgroundTaskActive = true;

      if (DEBUG_LOCATION) {
        console.log('[LocationTracker] Background tracking started');
      }

      return true;
    } catch (error) {
      console.error('[LocationTracker] Failed to start background tracking:', error);
      this.handleLocationError(error);
      // Fall back to foreground tracking
      return await this.startForegroundTracking();
    }
  }

  async stopForegroundTracking() {
    try {
      if (this.currentWatcher) {
        await this.currentWatcher.remove();
        this.currentWatcher = null;
      }
      this.isTrackingActive = false;

      if (DEBUG_LOCATION) {
        console.log('[LocationTracker] Foreground tracking stopped');
      }
    } catch (error) {
      console.error('[LocationTracker] Failed to stop foreground tracking:', error);
    }
  }

  async stopBackgroundTracking() {
    try {
      const registeredTasks = await TaskManager.getRegisteredTasksAsync();
      const isRegistered = registeredTasks.some(task => task.taskName === LOCATION_TASK_NAME);
      
      if (isRegistered) {
        await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
      }
      
      this.backgroundTaskActive = false;

      if (DEBUG_LOCATION) {
        console.log('[LocationTracker] Background tracking stopped');
      }
    } catch (error) {
      console.error('[LocationTracker] Failed to stop background tracking:', error);
    }
  }

  async stopAllTracking() {
    await Promise.all([
      this.stopForegroundTracking(),
      this.stopBackgroundTracking()
    ]);
  }

  async getCurrentLocation() {
    try {
      await this.checkAndRequestPermissions();
      
      if (this.permissionStatus.foreground !== 'granted') {
        throw new Error('Location permission required');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // 10 seconds
      });

      this.updateLocationInStore(location);
      return location;
    } catch (error) {
      console.error('[LocationTracker] Failed to get current location:', error);
      this.handleLocationError(error);
      return null;
    }
  }

  getTrackingStatus() {
    return {
      foregroundActive: this.isTrackingActive,
      backgroundActive: this.backgroundTaskActive,
      permissions: this.permissionStatus,
      lastLocation: this.lastKnownLocation
    };
  }

  // Cleanup method for app termination
  async cleanup() {
    await this.stopAllTracking();
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    
    if (DEBUG_LOCATION) {
      console.log('[LocationTracker] Cleanup completed');
    }
  }

  // --- Adaptive throttling helpers ---
  optionsChanged(a, b) {
    if (!a || !b) return true;
    return (
      a.accuracy !== b.accuracy ||
      a.timeInterval !== b.timeInterval ||
      a.distanceInterval !== b.distanceInterval ||
      a.deferredUpdatesInterval !== b.deferredUpdatesInterval ||
      a.deferredUpdatesDistance !== b.deferredUpdatesDistance
    );
  }

  async maybeAdjustForegroundWatcher(location) {
    try {
      const speed = location?.coords?.speed; // m/s, may be null
      if (typeof speed !== 'number') return;

      // High speed -> tighter updates, low speed -> relaxed
      const desired = speed > 5
        ? {
            accuracy: Location.Accuracy.BestForNavigation,
            timeInterval: 3000,
            distanceInterval: 6,
            activityType: Location.ActivityType.AutomotiveNavigation,
            deferredUpdatesInterval: 10000,
            deferredUpdatesDistance: 20,
          }
        : { ...this.trackingOptions };

      if (this.optionsChanged(this.currentOptions, desired)) {
        if (DEBUG_LOCATION) {
          console.log('[LocationTracker] Adjusting foreground watcher based on speed');
        }
        await this.stopForegroundTracking();
        this.currentOptions = desired;
        this.currentWatcher = await Location.watchPositionAsync(
          desired,
          (loc) => {
            this.updateLocationInStore(loc);
          }
        );
        this.isTrackingActive = true;
      }
    } catch (err) {
      console.error('[LocationTracker] Failed to adjust foreground watcher:', err);
    }
  }

  async maybeAdjustBackgroundTracking(location) {
    try {
      const speed = location?.coords?.speed;
      if (typeof speed !== 'number') return;

      // Keep background adaptive adjustments minimal to avoid frequent restarts
      const desired = speed > 5
        ? {
            accuracy: Location.Accuracy.High,
            timeInterval: 5000,
            distanceInterval: 10,
            activityType: Location.ActivityType.AutomotiveNavigation,
            deferredUpdatesInterval: 15000,
            deferredUpdatesDistance: 30,
            foregroundService: this.currentOptions?.foregroundService,
          }
        : { ...this.trackingOptions, foregroundService: this.currentOptions?.foregroundService };

      if (this.optionsChanged(this.currentOptions, desired)) {
        if (DEBUG_LOCATION) {
          console.log('[LocationTracker] Adjusting background tracking based on speed');
        }
        const registeredTasks = await TaskManager.getRegisteredTasksAsync();
        const isRegistered = registeredTasks.some(task => task.taskName === LOCATION_TASK_NAME);
        if (isRegistered) {
          await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
        }
        this.currentOptions = desired;
        await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
          ...desired,
          showsBackgroundLocationIndicator: true,
        });
      }
    } catch (err) {
      console.error('[LocationTracker] Failed to adjust background tracking:', err);
    }
  }
}

// Singleton instance
const locationTracker = new LocationTracker();

export default locationTracker;
