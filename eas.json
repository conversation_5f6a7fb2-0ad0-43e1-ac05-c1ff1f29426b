{"cli": {"version": ">= 11.0.3"}, "build": {"development": {"channel": "development", "developmentClient": true, "distribution": "internal"}, "simulator": {"channel": "simulator", "developmentClient": true, "ios": {"simulator": true}, "distribution": "internal"}, "preview": {"channel": "preview", "android": {"buildType": "apk"}, "ios": {"simulator": true}, "distribution": "internal"}, "production": {"channel": "production", "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "ios": {"image": "macos-sonoma-14.6-xcode-16.1"}}}, "submit": {"production": {}}}