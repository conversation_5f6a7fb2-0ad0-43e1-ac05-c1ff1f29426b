import { get, onValue, push } from "firebase/database";
import { firebase } from '../config/configureFirebase';
import { RequestPushMsg } from '../other/NotificationFunctions';
import { formatBookingObject } from '../other/sharedFunctions';
import store from '../store/store';
import {
    CLEAR_BOOKING,
    CONFIRM_BOOKING,
    CONFIRM_BOOKING_FAILED,
    CONFIRM_BOOKING_SUCCESS
} from "../store/types";

export const clearBooking = () => (dispatch) => {
    dispatch({
        type: CLEAR_BOOKING,
        payload: null,
    });
}

export const addBooking = (bookingData) => async (dispatch) => {

    const   {
        bookingRef,
        settingsRef,
        singleUserRef
    } = firebase;

    dispatch({
        type: CONFIRM_BOOKING,
        payload: bookingData,
    });

    const settingsdata = await get(settingsRef);
    const settings = settingsdata.val();

    console.log("Booking Data before Formatting:", bookingData);

    // Generate a reference to be used for both trips if round trip
    let sharedReference = null;
    let onwardData = await formatBookingObject(bookingData, settings);
    sharedReference = onwardData.reference;

    console.log("Formatted Booking Data (Onward):", onwardData);

    // Push onward trip
    let onwardPush = push(bookingRef, onwardData).then((res) => {
        var bookingKey = res.key;
        dispatch({
            type: CONFIRM_BOOKING_SUCCESS,
            payload: {
                booking_id:bookingKey,
                mainData:{
                    ...onwardData,
                    id:bookingKey
                }
            }    
        });
    }).catch(error => {
        dispatch({
            type: CONFIRM_BOOKING_FAILED,
            payload: error.code + ": " + error.message,
        });
    });

    // Handle notifications for onward trip
    if(bookingData.requestedDrivers){
        const drivers = bookingData.requestedDrivers;
        Object.keys(drivers).map((uid)=>{
            onValue(singleUserRef(uid),  snapshot => {
                if (snapshot.val()) {
                    const pushToken = snapshot.val().pushToken;
                    const ios = snapshot.val().userPlatform == "IOS"? true: false
                    if(pushToken){
                        RequestPushMsg(
                            pushToken,
                            {
                                title: store.getState().languagedata.defaultLanguage.notification_title,
                                msg: store.getState().languagedata.defaultLanguage.new_booking_notification,
                                screen: 'DriverTrips',
                                channelId: settings.CarHornRepeat? 'bookings-repeat': 'bookings',
                                ios: ios
                            });
                     }
                 }
            }, {onlyOnce: true});
            return drivers[uid];
        })
    }

    // If round trip, create and push return trip
    if (bookingData.roundTrip && bookingData.returnDate) {
        // Create return trip bookingData
        let returnBookingData = {
            ...bookingData,
            pickup: bookingData.drop, // swap
            drop: bookingData.pickup, // swap
            tripdate: bookingData.returnDate,
            isReturnTrip: true,
            reference: sharedReference, // ensure same reference
            flightNumber: bookingData.returnFlightNumber // <-- Use return flight number for return trip
        };
        let returnData = await formatBookingObject(returnBookingData, settings);
        // Overwrite reference to match onward trip
        returnData.reference = sharedReference;
        // Mark as return trip
        returnData.isReturnTrip = true;
        // Push return trip
        push(bookingRef, returnData).catch(error => {
            // Optionally handle error for return trip
            dispatch({
                type: CONFIRM_BOOKING_FAILED,
                payload: error.code + ": " + error.message,
            });
        });
        // Optionally, send notifications for return trip as well
        if(returnBookingData.requestedDrivers){
            const drivers = returnBookingData.requestedDrivers;
            Object.keys(drivers).map((uid)=>{
                onValue(singleUserRef(uid),  snapshot => {
                    if (snapshot.val()) {
                        const pushToken = snapshot.val().pushToken;
                        const ios = snapshot.val().userPlatform == "IOS"? true: false
                        if(pushToken){
                            RequestPushMsg(
                                pushToken,
                                {
                                    title: store.getState().languagedata.defaultLanguage.notification_title,
                                    msg: store.getState().languagedata.defaultLanguage.new_booking_notification,
                                    screen: 'DriverTrips',
                                    channelId: settings.CarHornRepeat? 'bookings-repeat': 'bookings',
                                    ios: ios
                                });
                         }
                     }
                }, {onlyOnce: true});
                return drivers[uid];
            })
        }
    }
};

