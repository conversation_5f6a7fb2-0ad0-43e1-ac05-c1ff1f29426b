{"build": {"development": {"channel": "development", "developmentClient": true, "distribution": "internal"}, "simulator": {"channel": "simulator", "developmentClient": true, "ios": {"simulator": true}, "distribution": "internal"}, "preview": {"channel": "preview", "android": {"buildType": "apk"}, "ios": {"simulator": true}, "distribution": "internal"}, "production": {"channel": "production", "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "ios": {"image": "macos-sonoma-14.6-xcode-16.1"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "../serviceaccount.json", "track": "internal"}}}, "cli": {"version": ">= 0.50.0", "requireCommit": false}}