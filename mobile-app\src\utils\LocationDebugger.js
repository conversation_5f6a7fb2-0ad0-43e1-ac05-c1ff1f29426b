import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import { Alert } from 'react-native';

class LocationDebugger {
  static async checkLocationServices() {
    try {
      const isEnabled = await Location.hasServicesEnabledAsync();
      return {
        enabled: isEnabled,
        message: isEnabled ? 'Location services are enabled' : 'Location services are disabled'
      };
    } catch (error) {
      return {
        enabled: false,
        error: error.message,
        message: 'Failed to check location services'
      };
    }
  }

  static async checkPermissions() {
    try {
      const foreground = await Location.getForegroundPermissionsAsync();
      const background = await Location.getBackgroundPermissionsAsync();
      
      return {
        foreground: {
          status: foreground.status,
          granted: foreground.status === 'granted',
          canAskAgain: foreground.canAskAgain
        },
        background: {
          status: background.status,
          granted: background.status === 'granted',
          canAskAgain: background.canAskAgain
        }
      };
    } catch (error) {
      return {
        error: error.message,
        message: 'Failed to check permissions'
      };
    }
  }

  static async getDeviceLocationCapabilities() {
    try {
      const hasGPS = await Location.hasServicesEnabledAsync();
      const accuracy = await Location.getLocationAccuracyAsync();
      
      return {
        hasGPS,
        accuracy: accuracy ? 'High' : 'Reduced',
        message: `GPS: ${hasGPS ? 'Available' : 'Not Available'}, Accuracy: ${accuracy ? 'High' : 'Reduced'}`
      };
    } catch (error) {
      return {
        error: error.message,
        message: 'Failed to check device capabilities'
      };
    }
  }

  static async checkActiveTracking() {
    try {
      const registeredTasks = await TaskManager.getRegisteredTasksAsync();
      const locationTasks = registeredTasks.filter(task => 
        task.taskName.includes('location') || task.taskName.includes('background')
      );
      
      return {
        taskCount: registeredTasks.length,
        locationTasks: locationTasks,
        message: `Active tasks: ${registeredTasks.length}, Location tasks: ${locationTasks.length}`
      };
    } catch (error) {
      return {
        error: error.message,
        message: 'Failed to check active tracking'
      };
    }
  }

  static async getCurrentLocation() {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000,
      });
      
      return {
        success: true,
        location: {
          lat: location.coords.latitude,
          lng: location.coords.longitude,
          accuracy: location.coords.accuracy,
          timestamp: location.timestamp
        },
        message: `Location: ${location.coords.latitude.toFixed(6)}, ${location.coords.longitude.toFixed(6)}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to get current location'
      };
    }
  }

  static async runDiagnostics() {
    console.log('🔍 Running Location Diagnostics...');
    
    const results = {
      timestamp: new Date().toISOString(),
      locationServices: await this.checkLocationServices(),
      permissions: await this.checkPermissions(),
      deviceCapabilities: await this.getDeviceLocationCapabilities(),
      activeTracking: await this.checkActiveTracking(),
      currentLocation: await this.getCurrentLocation()
    };

    console.log('📊 Location Diagnostics Results:', JSON.stringify(results, null, 2));
    return results;
  }

  static async showDiagnosticsAlert() {
    const results = await this.runDiagnostics();
    
    let message = '';
    
    // Location Services
    if (!results.locationServices.enabled) {
      message += '❌ Location services are disabled\n';
    } else {
      message += '✅ Location services enabled\n';
    }
    
    // Permissions
    if (!results.permissions.foreground?.granted) {
      message += '❌ Foreground location permission denied\n';
    } else {
      message += '✅ Foreground location permission granted\n';
    }
    
    if (!results.permissions.background?.granted) {
      message += '⚠️ Background location permission denied\n';
    } else {
      message += '✅ Background location permission granted\n';
    }
    
    // Current Location
    if (results.currentLocation.success) {
      message += `✅ Current location: ${results.currentLocation.location.lat.toFixed(4)}, ${results.currentLocation.location.lng.toFixed(4)}\n`;
    } else {
      message += '❌ Failed to get current location\n';
    }
    
    // Active tracking
    message += `📱 Active tracking tasks: ${results.activeTracking.taskCount}\n`;
    
    Alert.alert('Location Diagnostics', message, [
      { text: 'OK' },
      { 
        text: 'Copy to Clipboard', 
        onPress: () => {
          // You can add clipboard functionality here if needed
          console.log('Full diagnostics:', results);
        }
      }
    ]);
    
    return results;
  }

  static formatDiagnosticsReport(results) {
    return `
Location Tracking Diagnostics Report
Generated: ${results.timestamp}

🌍 Location Services: ${results.locationServices.enabled ? '✅ Enabled' : '❌ Disabled'}
${results.locationServices.error ? `Error: ${results.locationServices.error}` : ''}

🔐 Permissions:
  - Foreground: ${results.permissions.foreground?.granted ? '✅ Granted' : '❌ Denied'}
  - Background: ${results.permissions.background?.granted ? '✅ Granted' : '❌ Denied'}

📱 Device Capabilities: ${results.deviceCapabilities.message}

🎯 Current Location: ${results.currentLocation.success ? 
  `✅ ${results.currentLocation.message}` : 
  `❌ ${results.currentLocation.error}`}

🔄 Active Tracking: ${results.activeTracking.message}
${results.activeTracking.locationTasks?.length > 0 ? 
  `Active Location Tasks: ${results.activeTracking.locationTasks.map(t => t.taskName).join(', ')}` : 
  'No active location tasks'}

${results.activeTracking.error ? `Error: ${results.activeTracking.error}` : ''}
    `.trim();
  }
}

export default LocationDebugger;
