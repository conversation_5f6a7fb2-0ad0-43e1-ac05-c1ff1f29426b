import React from 'react';
import { View, Text } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { fonts } from '../common/font';
import { colors } from '../common/theme';

const SafeMapView = ({
  style,
  region,
  initialRegion,
  children,
  loadingText = 'Loading map...',
  errorText = 'Map unavailable',
  fallbackStyle,
  ...props
}) => {
  // Validate coordinates before rendering
  const isValidRegion = (reg) => {
    if (!reg) return false;
    
    const { latitude, longitude } = reg;
    
    // Check if coordinates are valid numbers
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      return false;
    }
    
    // Check if coordinates are within valid ranges
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return false;
    }
    
    // Check for NaN or Infinity
    if (isNaN(latitude) || isNaN(longitude) || !isFinite(latitude) || !isFinite(longitude)) {
      return false;
    }
    
    return true;
  };

  // Check if we have valid coordinates
  const hasValidCoordinates = isValidRegion(region) || isValidRegion(initialRegion);

  if (!hasValidCoordinates) {
    return (
      <View style={[style, fallbackStyle, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ fontFamily: fonts.Regular, color: colors.BLACK }}>
          {errorText}
        </Text>
      </View>
    );
  }

  return (
    <MapView
      style={style}
      region={region}
      initialRegion={initialRegion}
      provider={PROVIDER_GOOGLE}
      {...props}
    >
      {children}
    </MapView>
  );
};

// Safe Marker component
export const SafeMarker = ({ coordinate, ...props }) => {
  if (!coordinate || typeof coordinate.latitude !== 'number' || typeof coordinate.longitude !== 'number') {
    return null;
  }
  
  return <Marker coordinate={coordinate} {...props} />;
};

// Safe Polyline component
export const SafePolyline = ({ coordinates, ...props }) => {
  if (!coordinates || !Array.isArray(coordinates) || coordinates.length === 0) {
    return null;
  }
  
  // Validate all coordinates in the array
  const validCoordinates = coordinates.filter(coord => 
    coord && 
    typeof coord.latitude === 'number' && 
    typeof coord.longitude === 'number' &&
    !isNaN(coord.latitude) && 
    !isNaN(coord.longitude)
  );
  
  if (validCoordinates.length < 2) {
    return null;
  }
  
  return <Polyline coordinates={validCoordinates} {...props} />;
};

export default SafeMapView;
