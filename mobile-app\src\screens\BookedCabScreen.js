import { Ionicons } from '@expo/vector-icons';
import * as DecodePolyLine from '@mapbox/polyline';
import { CommonActions } from '@react-navigation/native';
import { api } from 'common';
import * as ImagePicker from 'expo-image-picker';
import { ActivityAction, startActivityAsync } from 'expo-intent-launcher';
import * as Location from 'expo-location';
import i18n from 'i18n-js';
import moment from 'moment/min/moment-with-locales';
import { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Dimensions,
    Image,
    Linking,
    Modal,
    TouchableOpacity as OldTouch,
    Platform,
    ScrollView,
    Share,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { Button, Icon } from 'react-native-elements';
import MapView, { AnimatedRegion, Marker, Polyline, PROVIDER_GOOGLE } from 'react-native-maps';
import RadioForm from 'react-native-simple-radio-button';
import StarRating from 'react-native-star-rating-widget';
import { useDispatch, useSelector } from 'react-redux';
import carImageIcon from '../../assets/images/track_Car.png';
import { fonts } from '../common/font';
import { appConsts, MAIN_COLOR } from '../common/sharedFunctions';
import { colors } from '../common/theme';
import { OtpModal } from '../components';
import ApiUsageDisplay from '../components/ApiUsageDisplay';
import simpleLocationTracker from '../services/SimpleLocationTracker';
import { calculateDistance, isValidLocation } from '../utils/LocationUtils';
var { width, height } = Dimensions.get('window');
const hasNotch = Platform.OS === 'ios' && !Platform.isPad && !Platform.isTVOS && ((height === 780 || width === 780) || (height === 812 || width === 812) || (height === 844 || width === 844) || (height === 896 || width === 896) || (height === 926 || width === 926))

// Add this helper function after the existing imports and before the component
const isAirportLocation = (address) => {
    if (!address) return false;
    const airportKeywords = [
        'airport', 'Airport', 'AIRPORT',
        "O'Hare", "O'Hare International Airport", "Chicago O'Hare International Airport", "ORD",
        "Midway", "Midway International Airport", "Chicago Midway International Airport", "MDW"
    ];
    return airportKeywords.some(keyword => address.includes(keyword));
};

export default function BookedCabScreen(props) {
    const {
        fetchBookingLocations,
        stopLocationFetch,
        updateBookingImage,
        cancelBooking,
        updateBooking,
        getDirectionsApi,
        editSos,
        updateProfile
    } = api;
    const dispatch = useDispatch();
    const { bookingId } = props.route.params;
    const latitudeDelta = 0.0922;
    const longitudeDelta = 0.0421;
    const [alertModalVisible, setAlertModalVisible] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [searchModalVisible, setSearchModalVisible] = useState(false);
    const activeBookings = useSelector(state => state.bookinglistdata.active);
    const [curBooking, setCurBooking] = useState(null);
    const cancelReasons = useSelector(state => state.cancelreasondata.complex);
    const auth = useSelector(state => state.auth);
    const [cancelReasonSelected, setCancelReasonSelected] = useState(0);
    const [otpModalVisible, setOtpModalVisible] = useState(false);
    const lastLocation = useSelector(state => state.locationdata.coords);
    const gpsLocation = useSelector(state => state.gpsdata.location);
    const [liveRouteCoords, setLiveRouteCoords] = useState(null);
    const mapRef = useRef();
    const pageActive = useRef();
    const [lastCoords, setlastCoords] = useState();
    const [arrivalTime, setArrivalTime] = useState(0);
    const [loading, setLoading] = useState(false);
    const [purchaseInfoModalStatus, setPurchaseInfoModalStatus] = useState(false);
    const [userInfoModalStatus, setUserInfoModalStatus] = useState(false);
    const settings = useSelector(state => state.settingsdata.settings);
    const gps = useSelector(state => state.gpsdata);
    const [checks, setChecks] = useState({
        driverActiveStatus: auth.profile.driverActiveStatus || false,
    });
    // Throttling refs for Directions API calls
    const lastRouteFetchAt = useRef(0);
    const lastRouteFetchOrigin = useRef(null);

    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;

    const [role, setRole] = useState();
    const [hasArrived, setHasArrived] = useState(false);
    const [gpsErrorCount, setGpsErrorCount] = useState(0); // Add GPS error count state

    // Determine which location to use for the marker
    const isDriver = role === 'driver';
    const markerLat = isDriver
        ? gpsLocation?.lat || curBooking?.pickup?.lat
        : lastLocation?.lat || curBooking?.pickup?.lat;
    const markerLng = isDriver
        ? gpsLocation?.lng || curBooking?.pickup?.lng
        : lastLocation?.lng || curBooking?.pickup?.lng;

    const markerRef = useRef();
    const animatedRegion = useRef(
        new AnimatedRegion({
            latitude: markerLat,
            longitude: markerLng,
            latitudeDelta,
            longitudeDelta,
        })
    ).current;

    // Animate marker when location changes
    useEffect(() => {
        if (markerLat && markerLng && animatedRegion) {
            animatedRegion.timing({
                latitude: markerLat,
                longitude: markerLng,
                duration: 1000,
                useNativeDriver: false,
            }).start();
        }
    }, [markerLat, markerLng]);

    // Start continuous GPS tracking for driver when trip is active
    useEffect(() => {
        let isActive = true;

        const shouldTrack = role === 'driver' &&
            auth.profile?.driverActiveStatus &&
            curBooking &&
            (curBooking.status === 'ACCEPTED' ||
                curBooking.status === 'STARTED' ||
                curBooking.status === 'ARRIVED');

        if (shouldTrack && !simpleLocationTracker.isActive()) {
            const startTracking = async () => {
                try {
                    const success = await simpleLocationTracker.startTracking(
                        (location) => {
                            if (isActive && isValidLocation(location)) {
                                // Update marker position
                                if (markerRef.current && animatedRegion) {
                                    animatedRegion.timing({
                                        latitude: location.lat,
                                        longitude: location.lng,
                                        duration: 1000,
                                        useNativeDriver: false,
                                    }).start();
                                }

                                // Update map center if auto-centering is enabled
                                if (mapRef.current && centerOnCar && !userInteracting) {
                                    mapRef.current.animateToRegion({
                                        latitude: location.lat,
                                        longitude: location.lng,
                                        latitudeDelta: latitudeDelta,
                                        longitudeDelta: longitudeDelta,
                                    }, 1000);
                                }

                                // Update route only if significantly moved
                                if (curBooking?.status === 'STARTED' && curBooking?.drop) {
                                    updateRouteIfNeeded(location, curBooking.drop);
                                }
                            }
                        },
                        (error) => {
                            console.error('Location tracking error:', error);
                            setGpsErrorCount(prevCount => prevCount + 1);
                        },
                        bookingId // Pass booking ID for API tracking
                    );

                    if (!success) {
                        console.warn('[BookedCabScreen] Failed to start location tracking');
                    }
                } catch (error) {
                    console.error('[BookedCabScreen] Error starting location tracking:', error);
                }
            };

            startTracking();
        }

        return () => {
            isActive = false;
        };
    }, [role, auth.profile?.driverActiveStatus, curBooking?.status]);

    // Optimized route update function
    const updateRouteIfNeeded = async (currentLocation, destination) => {
        if (!settings.showLiveRoute) return;

        try {
            const routeData = await simpleLocationTracker.getCachedRoute(
                currentLocation,
                destination,
                curBooking.waypoints ? curBooking.waypoints.map(wp => `${wp.lat},${wp.lng}`).join('|') : ''
            );

            if (routeData) {
                setArrivalTime(routeData.time_in_secs ? parseFloat(routeData.time_in_secs / 60).toFixed(0) : 0);

                if (routeData.polylinePoints) {
                    let points = DecodePolyLine.decode(routeData.polylinePoints);
                    let coords = points.map((point) => ({
                        latitude: point[0],
                        longitude: point[1]
                    }));
                    setLiveRouteCoords(coords);
                }
            }
        } catch (error) {
            console.error('Failed to update route:', error);
        }
    };

    // Replace the existing fitMap function with optimized version
    const fitMapOptimized = async (point1, point2) => {
        // Always fit map to coordinates first (no API call needed)
        if (mapRef.current) {
            mapRef.current.fitToCoordinates([
                { latitude: point1.lat, longitude: point1.lng },
                { latitude: point2.lat, longitude: point2.lng }
            ], {
                edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                animated: true,
            });
        }

        // Only fetch route if live route is enabled
        if (settings.showLiveRoute) {
            try {
                const routeData = await simpleLocationTracker.getCachedRoute(
                    point1,
                    point2,
                    curBooking.waypoints ? curBooking.waypoints.map(wp => `${wp.lat},${wp.lng}`).join('|') : ''
                );

                if (routeData) {
                    setArrivalTime(routeData.time_in_secs ? parseFloat(routeData.time_in_secs / 60).toFixed(0) : 0);

                    if (routeData.polylinePoints) {
                        let points = DecodePolyLine.decode(routeData.polylinePoints);
                        let coords = points.map((point) => ({
                            latitude: point[0],
                            longitude: point[1]
                        }));
                        setLiveRouteCoords(coords);
                    }
                }
            } catch (error) {
                console.error('Failed to fetch route:', error);
            }
        }
    };

    // Update existing useEffect to use optimized fitMap
    useEffect(() => {
        if (lastLocation && curBooking && curBooking.status == 'ACCEPTED' && pageActive.current) {
            let point1 = { lat: lastLocation.lat, lng: lastLocation.lng };
            let point2 = { lat: curBooking.pickup.lat, lng: curBooking.pickup.lng };
            fitMapOptimized(point1, point2);
            setlastCoords(lastLocation);
        }

        if (curBooking && curBooking.status == 'ARRIVED' && pageActive.current) {
            setlastCoords(null);
            setTimeout(() => {
                mapRef.current.fitToCoordinates([
                    { latitude: curBooking.pickup.lat, longitude: curBooking.pickup.lng },
                    { latitude: curBooking.drop.lat, longitude: curBooking.drop.lng }
                ], {
                    edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                    animated: true,
                });
            }, 1000);
        }

        if (lastLocation && curBooking && curBooking.status == 'STARTED' && pageActive.current) {
            let point1 = { lat: lastLocation.lat, lng: lastLocation.lng };
            let point2 = { lat: curBooking.drop.lat, lng: curBooking.drop.lng };
            fitMapOptimized(point1, point2);
            setlastCoords(lastLocation);

            // Force map update for live tracking
            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    latitude: lastLocation.lat,
                    longitude: lastLocation.lng,
                    latitudeDelta: latitudeDelta,
                    longitudeDelta: longitudeDelta
                }, 1000);
            }
        }

        if (lastLocation && curBooking && curBooking.status == 'REACHED' && role == 'customer' && pageActive.current) {
            setTimeout(() => {
                mapRef.current.fitToCoordinates([
                    { latitude: curBooking.pickup.lat, longitude: curBooking.pickup.lng },
                    { latitude: lastLocation.lat, longitude: lastLocation.lng }
                ], {
                    edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                    animated: true,
                });
            }, 1000);
        }
    }, [lastLocation, curBooking, pageActive.current]);

    useEffect(() => {
        if (auth.profile && auth.profile.usertype) {
            setRole(auth.profile.usertype);
        } else {
            setRole(null);
        }
    }, [auth.profile]);

    // Monitor API usage and provide real-time feedback
    useEffect(() => {
        // Log detailed API statistics every 5 minutes (BEST CASE - less frequent monitoring)
        const statsInterval = setInterval(() => {
            if (simpleLocationTracker.isActive()) {
                const stats = simpleLocationTracker.getApiCallStats();
                console.log('📊 API Usage Report (BEST CASE):');
                console.log(`Total API Calls: ${stats.totalCalls}`);
                console.log(`Recent Calls (5min): ${stats.monitorStats.recentCalls}`);
                console.log(`Calls/Minute: ${stats.monitorStats.callsPerMinute}`);
                console.log(`Total Cost: $${stats.costs.totalCost.toFixed(4)}`);
                console.log(`Projected Hourly Cost: $${stats.costs.projectedHourlyCost.toFixed(2)}`);
                console.log(`Cache Size: ${stats.cacheSize} routes`);

                // Store current API data in booking every 5 minutes (BEST CASE)
                if (curBooking && bookingId) {
                    console.log('🔄 Storing API data in booking...');
                    simpleLocationTracker.storeApiCountInBooking(bookingId).then(result => {
                        if (result) {
                            console.log('✅ API data stored successfully:', result);
                        } else {
                            console.log('❌ Failed to store API data');
                        }
                    }).catch(error => {
                        console.error('❌ Error storing API data:', error);
                    });
                }
            }
        }, 300000); // Every 5 minutes (BEST CASE)

        return () => clearInterval(statsInterval);
    }, [curBooking, bookingId]);


    useEffect(() => {
        if (lastLocation && curBooking && curBooking.status == 'ACCEPTED' && pageActive.current) {
            let point1 = { lat: lastLocation.lat, lng: lastLocation.lng };
            let point2 = { lat: curBooking.pickup.lat, lng: curBooking.pickup.lng };
            fitMap(point1, point2);
            setlastCoords(lastLocation);
        }

        if (curBooking && curBooking.status == 'ARRIVED' && pageActive.current) {
            setlastCoords(null);
            setTimeout(() => {
                mapRef.current.fitToCoordinates([{ latitude: curBooking.pickup.lat, longitude: curBooking.pickup.lng }, { latitude: curBooking.drop.lat, longitude: curBooking.drop.lng }], {
                    edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                    animated: true,
                })
            }, 1000);
        }
        if (lastLocation && curBooking && curBooking.status == 'STARTED' && pageActive.current) {
            let point1 = { lat: lastLocation.lat, lng: lastLocation.lng };
            let point2 = { lat: curBooking.drop.lat, lng: curBooking.drop.lng };
            fitMap(point1, point2);
            setlastCoords(lastLocation);

            // Force map update for live tracking
            if (mapRef.current) {
                mapRef.current.animateToRegion({
                    latitude: lastLocation.lat,
                    longitude: lastLocation.lng,
                    latitudeDelta: latitudeDelta,
                    longitudeDelta: longitudeDelta
                }, 1000);
            }
        }
        if (lastLocation && curBooking && curBooking.status == 'REACHED' && role == 'customer' && pageActive.current) {
            setTimeout(() => {
                mapRef.current.fitToCoordinates([{ latitude: curBooking.pickup.lat, longitude: curBooking.pickup.lng }, { latitude: lastLocation.lat, longitude: lastLocation.lng }], {
                    edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                    animated: true,
                })
            }, 1000);
        }
    }, [lastLocation, curBooking, pageActive.current])

    // Remove the problematic useEffect and replace with a safe version
    const prevStatusRef = useRef();
    useEffect(() => {
        if (
            curBooking &&
            role === 'driver' &&
            auth.profile.driverActiveStatus &&
            curBooking.status === 'STARTED' &&
            lastLocation &&
            prevStatusRef.current !== 'STARTED'
        ) {
            api.saveTracking(bookingId, {
                at: new Date().getTime(),
                status: 'STARTED',
                lat: lastLocation.lat,
                lng: lastLocation.lng
            });
        }
        prevStatusRef.current = curBooking ? curBooking.status : undefined;
    }, [curBooking, lastLocation, role, auth.profile.driverActiveStatus, bookingId]);

    const fitMap = (point1, point2) => {
        // Throttle Directions calls to control costs and avoid overfetching
        // Only attempt Directions when live route is enabled
        let startLoc = point1.lat + ',' + point1.lng;
        let destLoc = point2.lat + ',' + point2.lng;
        if (settings.showLiveRoute) {
            const now = Date.now();
            const MIN_INTERVAL_MS = 120000; // 2 minutes
            const MIN_MOVE_KM = 0.3; // 300 meters

            // Time-based throttle
            if (now - lastRouteFetchAt.current < MIN_INTERVAL_MS) {
                // Too soon; skip recomputing the route
            } else {
                // Distance-based throttle from last origin
                let movedEnough = true;
                if (lastRouteFetchOrigin.current && typeof api?.GetDistance === 'function') {
                    const km = api.GetDistance(
                        point1.lat,
                        point1.lng,
                        lastRouteFetchOrigin.current.lat,
                        lastRouteFetchOrigin.current.lng
                    );
                    movedEnough = km >= MIN_MOVE_KM;
                }

                if (!lastRouteFetchOrigin.current || movedEnough) {
                    lastRouteFetchAt.current = now;
                    lastRouteFetchOrigin.current = { lat: point1.lat, lng: point1.lng };
                } else {
                    // Not moved enough; skip network call but still allow map fit below
                    // Fall through to fit bounds only
                }
            }

            let waypoints = "";
            if (curBooking.waypoints && curBooking.waypoints.length > 0) {
                const arr = curBooking.waypoints;
                for (let i = 0; i < arr.length; i++) {
                    waypoints = waypoints + arr[i].lat + "," + arr[i].lng;
                    if (i < arr.length - 1) {
                        waypoints = waypoints + "|";
                    }
                }
            }
            // Only fetch Directions if interval and movement conditions are met
            if (Date.now() - lastRouteFetchAt.current <= MIN_INTERVAL_MS + 50) {
                getDirectionsApi(startLoc, destLoc, waypoints).then((details) => {
                    setArrivalTime(details.time_in_secs ? parseFloat(details.time_in_secs / 60).toFixed(0) : 0);
                    let points = DecodePolyLine.decode(details.polylinePoints);
                    let coords = points.map((point, index) => {
                        return {
                            latitude: point[0],
                            longitude: point[1]
                        }
                    })
                    setLiveRouteCoords(coords);
                    if (mapRef.current) {
                        mapRef.current.fitToCoordinates([{ latitude: point1.lat, longitude: point1.lng }, { latitude: point2.lat, longitude: point2.lng }], {
                            edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                            animated: true,
                        })
                    }
                }).catch(() => {
                });
            } else {
                // If throttled, just adjust the viewport without fetching a new route
                if (mapRef.current) {
                    mapRef.current.fitToCoordinates([{ latitude: point1.lat, longitude: point1.lng }, { latitude: point2.lat, longitude: point2.lng }], {
                        edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                        animated: true,
                    })
                }
            }
        } else {
            mapRef.current.fitToCoordinates([{ latitude: point1.lat, longitude: point1.lng }, { latitude: point2.lat, longitude: point2.lng }], {
                edgePadding: { top: 40, right: 40, bottom: 40, left: 40 },
                animated: true,
            })
        }
    }


    useEffect(() => {
        if (activeBookings && activeBookings.length >= 1) {
            let booking = activeBookings.filter(booking => booking.id == bookingId)[0];
            if (booking) {
                setCurBooking(booking);
                let diffMins = ((new Date(booking.tripdate)) - (new Date())) / (1000 * 60);
                if (booking.status == 'NEW' && (booking.bookLater == false || (booking.bookLater && diffMins <= 15))) {
                    if (role == 'customer' && !booking.hasOwnProperty('confirmModal')) setTimeout(() => setConfirmModalVisible(true), Platform.OS === "ios" ? 200 : 0);
                    if (role == 'customer' && booking.driverOffers) setTimeout(() => setSearchModalVisible(true), Platform.OS === "ios" ? 200 : 0);
                    if (role == 'customer' && booking.selectedBid && !booking.customer_paid) {
                        setTimeout(() => {
                            setConfirmModalVisible(false);
                            setSearchModalVisible(false);
                            props.navigation.navigate('PaymentDetails', { booking: { ...booking, ...booking.selectedBid } });
                        }, 2000)
                    }
                }
                if (booking.status == 'ACCEPTED') {
                    if (role == 'customer') setConfirmModalVisible(false);
                    if (role == 'customer') setSearchModalVisible(false);
                    if (role == 'customer') dispatch(fetchBookingLocations(bookingId));
                }
                if (booking.status == 'ARRIVED') {
                    if (role == 'customer') dispatch(fetchBookingLocations(bookingId));
                }
                if (booking.status == 'STARTED') {
                    // Both customer and driver need location tracking for started trips
                    dispatch(fetchBookingLocations(bookingId));
                }
                if (booking.status == 'REACHED') {
                    if (role == 'driver') {
                        setTimeout(() => {
                            props.navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'PaymentDetails', params: { booking: booking } }] }));
                        }, 1000);
                    }
                }
                if (booking.status == 'PENDING') {
                    if (role == 'customer') {
                        setTimeout(() => {
                            props.navigation.navigate('PaymentDetails', { booking: booking });
                        }, 1000);
                    }
                }
                if (booking.status == 'PAID' & pageActive.current) {
                    if (role == 'customer') {
                        setTimeout(() => {
                            props.navigation.navigate('DriverRating', { bookingId: booking });
                        }, 1000);
                    }
                    if (role == 'driver') {
                        props.navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'TabRoot' }] }));
                    }
                }
                if ((booking.status == 'ACCEPTED' || booking.status == 'ARRIVED') && booking.pickup_image) {
                    setLoading(false);
                }
                if (booking.status == 'STARTED' && booking.deliver_image) {
                    setLoading(false);
                }
            }
            else {
                setModalVisible(false);
                setSearchModalVisible(false);
                props.navigation.navigate('TabRoot', { screen: 'RideList', params: { fromBooking: true } });
            }
        }
        else {
            setModalVisible(false);
            setSearchModalVisible(false);
            if (role == 'driver') {
                props.navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'TabRoot' }] }));
            } else {
                props.navigation.navigate('TabRoot', { screen: 'RideList', params: { fromBooking: true } });
            }
        }
    }, [activeBookings, role, pageActive.current]);

    const renderButtons = () => {
        // Check if GPS error exists for driver
        const isGpsError = gps.error && role === 'driver';

        return (
            (curBooking && role == 'customer' && (curBooking.status == 'NEW' || curBooking.status == 'ACCEPTED')) ||
                (curBooking && role == 'driver' && (curBooking.status == 'ACCEPTED' || curBooking.status == 'ARRIVED' || curBooking.status == 'STARTED')) ?
                <View style={{ height: 50, flexDirection: isRTL ? 'row-reverse' : 'row', width: '98%', alignSelf: 'center', marginVertical: 5, marginBottom: 15 }}>
                    {(role == 'customer' && !curBooking.pickup_image && (curBooking.status == 'NEW' || curBooking.status == 'ACCEPTED')) ?
                        <View style={{ flex: 1 }}>
                            <Button
                                title={t('cancel_ride')}
                                loading={false}
                                loadingProps={{ size: "large" }}
                                titleStyle={{ fontFamily: fonts.Bold, fontSize: 13 }}
                                onPress={() => setModalVisible(true)}
                                buttonStyle={{ height: '100%', backgroundColor: colors.RED, borderRadius: 10, width: curBooking && role == 'customer' ? '100%' : '95%', alignSelf: curBooking && role == 'customer' ? 'center' : isRTL ? 'flex-end' : 'flex-start', }}
                                containerStyle={{ height: '100%' }}
                            />
                        </View>
                        : null}
                    {appConsts.captureBookingImage && settings && settings.AllowDeliveryPickupImageCapture && role == 'driver' && !curBooking.pickup_image && (curBooking.status == 'ACCEPTED' || curBooking.status == 'ARRIVED') ?
                        <View style={{ flex: 1 }}>
                            <Button
                                title={t('take_pickup_image')}
                                loading={loading}
                                titleStyle={{ color: colors.WHITE, fontFamily: fonts.Bold, fontSize: 13 }}
                                loadingProps={{ size: "large", color: colors.WHITE }}
                                onPress={() => _pickImage(ImagePicker.launchCameraAsync)}
                                buttonStyle={{ height: '100%', backgroundColor: MAIN_COLOR, width: '95%', borderRadius: 10, alignSelf: isRTL ? 'flex-start' : 'flex-end' }}
                                containerStyle={{ height: '100%' }}
                            />
                        </View>
                        : null}
                    {/* {role == 'driver' && (!appConsts.captureBookingImage || (curBooking.pickup_image && appConsts.captureBookingImage) || (settings && !settings.AllowDeliveryPickupImageCapture && appConsts.captureBookingImage)) && (curBooking.status == 'ACCEPTED' || curBooking.status == 'ARRIVED') ?
                        <View style={{ flex: 1 }}>
                            <Button
                                title={t('start_trip')}
                                loading={false}
                                loadingProps={{ size: "large", color: colors.WHITE }}
                                titleStyle={{ color: colors.WHITE, fontFamily: fonts.Bold, fontSize: 13 }}
                                onPress={() => {
                                    if (curBooking.otp && appConsts.hasStartOtp) {
                                        setOtpModalVisible(true);
                                    } else {
                                        startBooking();
                                    }
                                }}
                                buttonStyle={{ height: '100%', backgroundColor: MAIN_COLOR, width: '100%', borderRadius: 10, alignSelf: 'center' }}
                                containerStyle={{ height: '100%' }}
                            />
                        </View>
                        : null} */}

                    {role == 'driver' && curBooking && curBooking.status == 'ACCEPTED' && !hasArrived ? (
                        <View style={{ flex: 1 }}>
                            <Button
                                title={isGpsError ? `${t('arrived')} (GPS Error)` : t('arrived')}
                                loading={false}
                                disabled={isGpsError}
                                titleStyle={{ color: colors.WHITE, fontFamily: fonts.Bold, fontSize: 13 }}
                                onPress={() => {
                                    if (hasOtherActiveRide()) {
                                        showCompleteActiveRideAlert();
                                        return;
                                    }
                                    // Check for GPS error first
                                    if (isGpsError) {
                                        Alert.alert(
                                            'GPS Error',
                                            'Please fix the GPS location issue before marking as arrived.',
                                            [{ text: 'OK' }]
                                        );
                                        return;
                                    }

                                    // Check if driver is active first
                                    if (!auth.profile.driverActiveStatus) {
                                        Alert.alert(
                                            'Driver Not Active',
                                            'You need to be active to mark as arrived. Please go to Home and activate your status.',
                                            [
                                                {
                                                    text: 'OK'
                                                },
                                                {
                                                    text: 'Cancel',
                                                    style: 'cancel'
                                                }
                                            ]
                                        );
                                        return;
                                    }

                                    // Check if button is disabled due to distance using dynamic threshold
                                    if (distanceToPickup > pickupThreshold) {
                                        // Show alert with distance information and appropriate threshold
                                        const thresholdText = isAirportLocation(curBooking?.pickup?.add) ? '2000 meters' : '250 meters';
                                        Alert.alert(
                                            'Cannot Mark as Arrived',
                                            `You need to be closer to the pickup location. Current distance: ${distanceToPickup.toFixed(0)} meters. Please get within ${thresholdText} of the pickup location.`,
                                            [{ text: 'OK' }]
                                        );
                                    } else {
                                        // Call your function to update booking status to ARRIVED
                                        setHasArrived(true);
                                        startArrived(); // implement this function to update status
                                    }
                                }}
                                buttonStyle={{
                                    height: '100%',
                                    backgroundColor: isGpsError ? colors.GRAY : MAIN_COLOR,
                                    width: '100%',
                                    borderRadius: 10,
                                    alignSelf: 'center'
                                }}
                                containerStyle={{ height: '100%' }}
                            />
                        </View>
                    ) : null}
                    {role == 'driver' && (!appConsts.captureBookingImage || (curBooking.pickup_image && appConsts.captureBookingImage) || (settings && !settings.AllowDeliveryPickupImageCapture && appConsts.captureBookingImage)) && (curBooking.status == 'ARRIVED') ? (
                        <View style={{ flex: 1 }}>
                            <Button
                                title={isGpsError ? `${t('start_trip')} (GPS Error)` : t('start_trip')}
                                loading={false}
                                disabled={isGpsError}
                                titleStyle={{ color: colors.WHITE, fontFamily: fonts.Bold, fontSize: 13 }}
                                onPress={() => {
                                    if (hasOtherActiveRide()) {
                                        showCompleteActiveRideAlert();
                                        return;
                                    }
                                    // Check for GPS error first
                                    if (isGpsError) {
                                        Alert.alert(
                                            'GPS Error',
                                            'Please fix the GPS location issue before starting the trip.',
                                            [{ text: 'OK' }]
                                        );
                                        return;
                                    }

                                    // Check if driver is active first
                                    if (!auth.profile.driverActiveStatus) {
                                        Alert.alert(
                                            'Driver Not Active',
                                            'You need to be active to start the trip. Please go to Home and activate your status.',
                                            [
                                                {
                                                    text: 'OK'
                                                },
                                                {
                                                    text: 'Cancel',
                                                    style: 'cancel'
                                                }
                                            ]
                                        );
                                        return;
                                    }

                                    if (curBooking.otp && appConsts.hasStartOtp) {
                                        setOtpModalVisible(true);
                                    } else {
                                        startBooking();
                                    }
                                }}
                                buttonStyle={{
                                    height: '100%',
                                    backgroundColor: isGpsError ? colors.GRAY : MAIN_COLOR,
                                    width: '100%',
                                    borderRadius: 10,
                                    alignSelf: 'center'
                                }}
                                containerStyle={{ height: '100%' }}
                            />
                        </View>
                    ) : null}
                    {appConsts.captureBookingImage && settings && settings.AllowFinalDeliveryImageCapture && role == 'driver' && !curBooking.deliver_image && curBooking.status == 'STARTED' ?
                        <View style={{ flex: 1 }}>
                            <Button
                                title={t('take_deliver_image')}
                                loading={loading}
                                loadingProps={{ size: "large", color: colors.WHITE }}
                                titleStyle={{ color: colors.WHITE, fontFamily: fonts.Bold, fontSize: 14 }}
                                onPress={() => _pickImage(ImagePicker.launchCameraAsync)}
                                buttonStyle={{ height: '100%', backgroundColor: MAIN_COLOR, borderRadius: 10, alignSelf: 'center', width: '100%' }}
                                containerStyle={{ height: '100%' }}
                            />
                        </View>
                        : null}
                    {role == 'driver' && (!appConsts.captureBookingImage || (curBooking.deliver_image && appConsts.captureBookingImage) || (settings && !settings.AllowFinalDeliveryImageCapture && appConsts.captureBookingImage)) && curBooking.status == 'STARTED' ?
                        <View style={{ flex: 1 }}>
                            <Button
                                title={isGpsError ? `${t('complete_ride')} (GPS Error)` : t('complete_ride')}
                                loading={loading}
                                disabled={isGpsError}
                                titleStyle={{ color: colors.WHITE, fontFamily: fonts.Bold, fontSize: 16 }}
                                onPress={() => {
                                    // Check for GPS error first
                                    if (isGpsError) {
                                        Alert.alert(
                                            'GPS Error',
                                            'Please fix the GPS location issue before completing the ride.',
                                            [{ text: 'OK' }]
                                        );
                                        return;
                                    }

                                    // Check if driver is active first
                                    if (!auth.profile.driverActiveStatus) {
                                        Alert.alert(
                                            'Driver Not Active',
                                            'You need to be active to complete the ride. Please go to Home and activate your status.',
                                            [
                                                {
                                                    text: 'OK'
                                                },
                                                {
                                                    text: 'Cancel',
                                                    style: 'cancel'
                                                }
                                            ]
                                        );
                                        return;
                                    }

                                    // Check if driver is too far from drop location using dynamic threshold
                                    if (distanceToDrop > dropThreshold) {
                                        // Show alert with distance information and appropriate threshold
                                        const thresholdText = isAirportLocation(curBooking?.drop?.add) ? '2000 meters' : '250 meters';
                                        Alert.alert(
                                            'Cannot Complete Ride',
                                            `You need to be closer to the drop location. Current distance: ${distanceToDrop.toFixed(0)} meters. Please get within ${thresholdText} of the drop location.`,
                                            [{ text: 'OK' }]
                                        );
                                    } else {
                                        // Proceed with normal completion logic
                                        if (curBooking.otp && !appConsts.hasStartOtp) {
                                            setOtpModalVisible(true);
                                        } else {
                                            endBooking();
                                        }
                                    }
                                }}
                                buttonStyle={{
                                    height: '100%',
                                    backgroundColor: isGpsError ? colors.GRAY : MAIN_COLOR,
                                    borderRadius: 10
                                }}
                                containerStyle={{ height: '100%', width: '100%' }}
                            />
                        </View>
                        : null}
                </View>
                : null
        );
    }



    const startBooking = () => {
        // Check if driver is active
        if (!auth.profile.driverActiveStatus) {
            Alert.alert(
                'Driver Not Active',
                'You need to be active to start the trip. Please go to Home and activate your status.',
                [
                    {
                        text: 'OK'
                    },
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    }
                ]
            );
            return;
        }

        setOtpModalVisible(false);
        let booking = { ...curBooking };
        booking.status = 'STARTED';
        dispatch(updateBooking(booking));

        // Update driver queue status to false (not in queue, actively driving)
        if (auth.profile && auth.profile.uid) {
            dispatch(api.updateProfile({ queue: true }));
        }

        // Ensure location tracking is active for live tracking
        if (role == 'driver' && lastLocation) {
            // Save initial tracking point for started trip
            api.saveTracking(bookingId, {
                at: new Date().getTime(),
                status: 'STARTED',
                lat: lastLocation.lat,
                lng: lastLocation.lng
            });

            // Start location fetching for customer
            if (role == 'driver') {
                dispatch(fetchBookingLocations(bookingId));
            }
        }
    }

    const endBooking = async () => {
        // Check if driver is active
        if (!auth.profile.driverActiveStatus) {
            Alert.alert(
                'Driver Not Active',
                'You need to be active to complete the ride. Please go to Home and activate your status.',
                [
                    {
                        text: 'OK'
                    },
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    }
                ]
            );
            return;
        }

        // Store final API usage data before ending booking
        try {
            const apiData = await simpleLocationTracker.endBookingTracking(bookingId);
            if (apiData) {
                console.log(`📊 Final API usage for booking ${bookingId}:`, apiData);
            }
        } catch (error) {
            console.error('Failed to store final API data:', error);
        }

        setLoading(true);
        let booking = { ...curBooking };
        booking.status = 'REACHED';
        dispatch(updateBooking(booking));
        setOtpModalVisible(false);

        // Update driver queue status to false (not in queue, actively driving)
        if (auth.profile && auth.profile.uid) {
            dispatch(api.updateProfile({ queue: false }));
        }
    }

    const startArrived = () => {
        // Check if driver is active
        if (!auth.profile.driverActiveStatus) {
            Alert.alert(
                'Driver Not Active',
                'You need to be active to mark as arrived. Please go to Home and activate your status.',
                [
                    {
                        text: 'OK'
                    },
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    }
                ]
            );
            return;
        }

        let booking = { ...curBooking };
        booking.status = 'ARRIVED';
        dispatch(updateBooking(booking));

        // Update driver queue status to true (in queue)
        if (auth.profile && auth.profile.uid) {
            dispatch(api.updateProfile({ queue: true }));
        }
    }

    const acceptBid = (item) => {
        let bookingObj = { ...curBooking };
        if ((bookingObj.payment_mode === 'wallet' && parseFloat(auth.profile.walletBalance) >= item.trip_cost) || bookingObj.payment_mode === 'cash' || bookingObj.payment_mode === 'card') {
            bookingObj.selectedBid = item;
            for (let key in bookingObj.driverOffers) {
                if (key !== item.driver) {
                    delete bookingObj.driverOffers[key];
                }
            }
            for (let key in bookingObj.requestedDrivers) {
                if (key !== item.driver) {
                    delete bookingObj.requestedDrivers[key];
                }
            }
            dispatch(updateBooking(bookingObj));
        } else {
            Alert.alert(t('alert'), t('wallet_balance_low'));
        }
    }

    const startNavigation = () => {
        let url = 'https://www.google.com/maps/dir/?api=1&travelmode=driving';
        if (curBooking.status == 'ACCEPTED') {
            url = url + '&destination=' + curBooking.pickup.lat + "," + curBooking.pickup.lng;
            Linking.openURL(url);
        }
        else if (curBooking.status == 'STARTED') {
            if (curBooking.waypoints && curBooking.waypoints.length && curBooking.waypoints.length > 0) {
                let abc = url + '&destination=' + curBooking.drop.lat + "," + curBooking.drop.lng + '&waypoints=';
                if (curBooking.waypoints.length > 1) {
                    for (let i = 0; i < curBooking.waypoints.length; i++) {
                        let obj = curBooking.waypoints[i];
                        if (i < curBooking.waypoints.length - 1) {
                            abc = abc + obj.lat + ',' + obj.lng + '%7C'
                        } else {
                            abc = abc + obj.lat + ',' + obj.lng

                        }
                    }
                    Linking.openURL(abc);
                } else {
                    url = url + '&destination=' + curBooking.drop.lat + "," + curBooking.drop.lng + '&waypoints=' + curBooking.waypoints[0].lat + "," + curBooking.waypoints[0].lng;
                    Linking.openURL(url);
                }
            } else {
                url = url + '&destination=' + curBooking.drop.lat + "," + curBooking.drop.lng;
                Linking.openURL(url);
            }
        } else {
            Alert.alert(t('alert'), t('navigation_available'));
        }
    }

    const alertModal = () => {
        return (
            <Modal
                animationType="none"
                transparent={true}
                visible={alertModalVisible}
                onRequestClose={() => {
                    setAlertModalVisible(false);
                }}>
                <View style={styles.alertModalContainer}>
                    <View style={styles.alertModalInnerContainer}>

                        <View style={styles.alertContainer}>

                            <Text style={styles.rideCancelText}>{t('rider_cancel_text')}</Text>

                            <View style={styles.horizontalLLine} />

                            <View style={styles.msgContainer}>
                                <Text style={styles.cancelMsgText}>{t('cancel_messege1')}  {bookingId} {t('cancel_messege2')} </Text>
                            </View>
                            <View style={styles.okButtonContainer}>
                                <Button
                                    title={t('no_driver_found_alert_OK_button')}
                                    titleStyle={{ fontFamily: fonts.Bold }}
                                    onPress={() => {
                                        setAlertModalVisible(false);
                                        props.navigation.popToTop();
                                    }}
                                    buttonStyle={styles.okButtonStyle}
                                    containerStyle={styles.okButtonContainerStyle}
                                />
                            </View>

                        </View>

                    </View>
                </View>

            </Modal>
        )
    }

    const goBack = () => {
        props.navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'TabRoot' }] }));
    }

    const cancelModal = () => {
        return (
            <Modal
                animationType="none"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => {
                    setModalVisible(false);
                }}>
                <View style={styles.cancelModalContainer}>
                    <View style={styles.cancelModalInnerContainer}>

                        <View style={styles.cancelContainer}>
                            <View style={styles.cancelReasonContainer}>
                                <Text style={styles.cancelReasonText}>{t('cancel_reason_modal_title')}</Text>
                            </View>

                            <View style={styles.radioContainer}>
                                <RadioForm
                                    radio_props={cancelReasons}
                                    initial={0}
                                    animation={false}
                                    buttonColor={MAIN_COLOR}
                                    selectedButtonColor={MAIN_COLOR}
                                    buttonSize={10}
                                    buttonOuterSize={20}
                                    style={styles.radioContainerStyle}
                                    labelStyle={styles.radioText}
                                    radioStyle={[styles.radioStyle, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
                                    onPress={(value) => { setCancelReasonSelected(value) }}
                                />
                            </View>
                            <View style={[styles.cancelModalButtosContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Button
                                    title={t('close')}
                                    titleStyle={{ fontFamily: fonts.Bold }}
                                    onPress={() => { setModalVisible(false) }}
                                    buttonStyle={styles.cancelModalButttonStyle}
                                    containerStyle={styles.cancelModalButtonContainerStyle}
                                />

                                <View style={styles.buttonSeparataor} />

                                <Button
                                    title={t('no_driver_found_alert_OK_button')}
                                    titleStyle={{ fontFamily: fonts.Bold }}
                                    onPress={() => {
                                        if (cancelReasonSelected >= 0) {
                                            dispatch(cancelBooking({ booking: curBooking, reason: cancelReasons[cancelReasonSelected].label, cancelledBy: role }));
                                            props.navigation.replace('TabRoot', { screen: 'RideList', params: { fromBooking: true } });
                                        } else {
                                            Alert.alert(t('alert'), t('select_reason'));
                                        }
                                    }}
                                    buttonStyle={[styles.cancelModalButttonStyle, { backgroundColor: colors.GREEN }]}
                                    containerStyle={styles.cancelModalButtonContainerStyle}
                                />
                            </View>

                        </View>


                    </View>
                </View>

            </Modal>
        )
    }

    const confirmModalClose = () => {
        setSearchModalVisible(true);
        setConfirmModalVisible(false);
        let booking = { ...curBooking };
        booking.confirmModal = true;
        dispatch(updateBooking(booking));
    }

    const confirmModal = () => {
        return (
            <Modal
                animationType="none"
                transparent={true}
                visible={confirmModalVisible}
                onRequestClose={() => {
                    setConfirmModalVisible(false);
                }}>
                <View style={{ flex: 1, backgroundColor: colors.BACKGROUND, justifyContent: 'center', alignItems: 'center' }}>
                    <View style={{ width: width - 70, borderRadius: 10, flex: 1, maxHeight: 280, marginTop: 15, backgroundColor: colors.WHITE, alignItems: 'center' }}>
                        <Ionicons name="checkmark-circle" size={130} color={colors.GREEN} style={{ marginTop: 10, }} />
                        <Text style={{ fontSize: 25, fontFamily: fonts.Bold, marginTop: -10 }}>{t('booking_successful')}</Text>
                        <Text style={{ fontSize: 16, marginTop: 10, fontFamily: fonts.Regular }}>{t('booking_confirm')}</Text>
                        <View style={{ position: 'absolute', bottom: 20, alignSelf: 'center' }}>
                            <Button
                                title={t('done')}
                                loading={false}
                                loadingProps={{ size: "large", }}
                                titleStyle={{ fontFamily: fonts.Bold }}
                                onPress={() => confirmModalClose()}
                                buttonStyle={{ width: 100, backgroundColor: colors.GREEN }}
                                containerStyle={{ marginTop: 15 }}
                            />
                        </View>

                    </View>


                </View>
            </Modal>
        )
    }

    const searchModal = () => {
        return (
            <Modal
                animationType="slide"
                transparent={true}
                visible={searchModalVisible}
                onRequestClose={() => {
                    setSearchModalVisible(false)
                }}
            >
                <View style={{ flex: 1, backgroundColor: colors.BACKGROUND, justifyContent: 'center', alignItems: 'center' }}>
                    {settings && curBooking && curBooking.driverOffers && !curBooking.selectedBid ?
                        <View style={{ width: width - 40, backgroundColor: colors.WHITE, borderRadius: 10, flex: 1, maxHeight: height - 200, marginTop: 15 }}>
                            <View style={{ color: colors.BLACK, position: 'absolute', top: 20, alignSelf: 'center' }}>
                                <Text style={{ color: colors.BLACK, fontSize: 20, fontFamily: fonts.Regular }}>{t('drivers')}</Text>
                            </View>
                            <View style={{ marginTop: 60, width: width - 60, height: height - 340, marginRight: 10, marginLeft: 10, alignSelf: 'center', maxWidth: 350, }}>
                                <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }}>
                                    {Object.keys(curBooking.driverOffers).map(key =>
                                        <View key={key} style={styles.vew}>
                                            <View style={{ height: '70%', width: '100%', flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                                <View style={{ width: '25%', justifyContent: 'center', alignItems: 'center' }}>
                                                    <Image source={curBooking && curBooking.driverOffers[key].driver_image ? { uri: curBooking.driverOffers[key].driver_image } : require('../../assets/images/profilePic.png')} style={{ borderRadius: 33, width: 65, height: 65 }} />
                                                </View>
                                                <View style={{ width: '75%', alignItems: 'center' }}>
                                                    <Text style={{ color: colors.BLACK, fontSize: 16, fontFamily: fonts.Regular, marginTop: 4, textAlign: 'center', }}>{curBooking.driverOffers[key].driver_name}</Text>
                                                    <StarRating
                                                        maxStars={5}
                                                        starSize={20}
                                                        enableHalfStar={true}
                                                        color={colors.STAR}
                                                        emptyColor={colors.STAR}
                                                        rating={curBooking && curBooking.driverOffers[key] && curBooking.driverOffers[key].driverRating ? parseFloat(curBooking.driverOffers[key].driverRating) : 0}
                                                        onChange={() => {
                                                            //console.log('hello')
                                                        }}
                                                        style={[isRTL ? { transform: [{ scaleX: -1 }] } : null]}
                                                    />
                                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', width: '100%', justifyContent: 'center', alignItems: 'center', marginTop: 4 }}>
                                                        {settings.swipe_symbol === false ?
                                                            <Text style={{ color: colors.BLACK, fontSize: 22, fontFamily: fonts.Bold }}>{settings.symbol} {parseFloat(curBooking.driverOffers[key].trip_cost).toFixed(2)}</Text>
                                                            :
                                                            <Text style={{ color: colors.BLACK, fontSize: 22, fontFamily: fonts.Bold }}>{parseFloat(curBooking.driverOffers[key].trip_cost).toFixed(2)} {settings.symbol}</Text>
                                                        }
                                                        <Button
                                                            title={t('accept')}
                                                            titleStyle={[styles.buttonTitleText, { fontFamily: fonts.Bold }]}
                                                            onPress={() => acceptBid(curBooking.driverOffers[key])}
                                                            buttonStyle={styles.accpt}
                                                        />
                                                    </View>
                                                    <Text style={{ color: colors.BLACK, fontSize: 16, fontFamily: fonts.Bold, alignSelf: 'center' }}>{moment(curBooking.driverOffers[key].deliveryDate).format('MM/DD/YYYY h:mm A')}</Text>
                                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignSelf: 'center' }}>
                                                        <Text style={{ color: colors.BLACK, fontSize: 12, marginTop: 3, fontFamily: fonts.Regular }}>{t('driver_distance')} - </Text>
                                                        <Text style={{ color: colors.BLACK, fontSize: 16, fontFamily: fonts.Bold, }}>{curBooking && curBooking.driverEstimates && curBooking.driverEstimates[key].timein_text ? curBooking.driverEstimates[key].timein_text : t('within_min')}</Text>
                                                    </View>
                                                </View>

                                            </View>

                                        </View>
                                    )}
                                </ScrollView>
                            </View>
                            <View style={{ position: 'absolute', bottom: 20, alignSelf: 'center' }}>
                                <Button
                                    title={t('close')}
                                    loading={false}
                                    loadingProps={{ size: "large", }}
                                    titleStyle={{ fontFamily: fonts.Bold }}
                                    onPress={() => { setSearchModalVisible(false) }}
                                    buttonStyle={{ width: 120, borderRadius: 10, backgroundColor: colors.RED }}
                                    containerStyle={{ marginTop: 20 }}
                                />
                            </View>
                        </View>
                        :
                        <View style={{ width: width - 70, borderRadius: 10, flex: 1, maxHeight: 310, marginTop: 15, backgroundColor: colors.WHITE }}>
                            <Image source={require('../../assets/images/g4.gif')} resizeMode={'contain'} style={{ width: '100%', height: 220, alignSelf: 'center' }} />
                            <View style={{ color: colors.BLACK, alignSelf: 'center' }}>
                                <Text style={{ color: colors.HEADER, fontSize: 16, fontFamily: fonts.Regular }}>{t('driver_assign_messege')}</Text>
                            </View>
                            <View style={{ position: 'absolute', bottom: 10, alignSelf: 'center' }}>
                                <Button
                                    title={t('close')}
                                    loading={false}
                                    loadingProps={{ size: "large", }}
                                    titleStyle={{ fontFamily: fonts.Bold }}
                                    onPress={() => { setSearchModalVisible(false) }}
                                    buttonStyle={{ width: 120, backgroundColor: colors.RED }}
                                    containerStyle={{ marginTop: 20, }}
                                />
                            </View>
                        </View>
                    }
                </View>
            </Modal>
        );
    }

    const chat = () => {
        props.navigation.navigate("onlineChat", { bookingId: bookingId })
    }
    const openWhatsApp = () => {
        const message = 'Hi';

        if (role === 'customer') {

            const whatsappLink = `whatsapp://send?phone=${curBooking.driver_contact}&text=${encodeURIComponent(message)}`;
            Linking.openURL(whatsappLink)
        }
        else if (role === 'driver') {

            const whatsappLink = `whatsapp://send?phone=${curBooking.customer_contact}&text=${encodeURIComponent(message)}`;
            Linking.openURL(whatsappLink)
        }


    };


    const onPressCall = (phoneNumber) => {
        let call_link = Platform.OS == 'android' ? 'tel:' + phoneNumber : 'telprompt:' + phoneNumber;
        Linking.openURL(call_link);
    }

    const _pickImage = async (res) => {
        var pickFrom = res;

        const { status } = await ImagePicker.requestCameraPermissionsAsync();

        if (status == 'granted') {
            let result = await pickFrom({
                allowsEditing: true,
                aspect: [3, 3]
            });

            if (!result.canceled) {
                const blob = await new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest();
                    xhr.onload = function () {
                        resolve(xhr.response);
                    };
                    xhr.onerror = function () {
                        Alert.alert(t('alert'), t('image_upload_error'));
                        setLoader(false);
                    };
                    xhr.responseType = 'blob';
                    xhr.open('GET', result.assets[0].uri, true);
                    xhr.send(null);
                });
                if (blob) {
                    setLoading(true);
                    dispatch(updateBookingImage(curBooking,
                        curBooking.status == 'ACCEPTED' || curBooking.status == 'ARRIVED' ? 'pickup_image' : 'deliver_image',
                        blob));
                }
            }
        }
    };

    const PurchaseInfoModal = () => {
        return (
            <Modal
                animationType="fade"
                transparent={true}
                visible={purchaseInfoModalStatus}
                onRequestClose={() => {
                    setPurchaseInfoModalStatus(false);
                }}
            >
                <View style={styles.centeredView}>
                    <View style={styles.modalView}>
                        <View style={{ width: '100%' }}>
                            <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                <Text style={styles.textHeading}>{t('parcel_type')}</Text>
                                <Text style={styles.textContent}>
                                    {curBooking && curBooking.parcelTypeSelected ? curBooking.parcelTypeSelected.description : ''}
                                </Text>
                            </View>
                            <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                <Text style={styles.textHeading}>{t('options')}</Text>
                                <Text style={styles.textContent}>
                                    {curBooking && curBooking.optionSelected ? curBooking.optionSelected.description : ''}
                                </Text>
                            </View>
                            {curBooking && curBooking.otherPerson ?
                                <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                    <Text style={styles.textHeading}>{t('otherPerson')}</Text>
                                    <Text style={styles.textContent}>
                                        {curBooking ? curBooking.otherPerson : ''}
                                    </Text>
                                </View>
                                : null}
                            {curBooking && curBooking.otherPersonPhone ?
                                <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                    <Text style={styles.textHeading}>{t('otherPersonPhone')}</Text>
                                    <Text style={styles.textContent}>
                                        {curBooking ? curBooking.otherPersonPhone : ''}
                                    </Text>
                                </View>
                                : null}
                            {curBooking && curBooking.pickUpInstructions ?
                                <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                    <Text style={styles.textHeading}>{t('pickUpInstructions')}</Text>
                                    <Text style={styles.textContent}>
                                        {curBooking ? curBooking.pickUpInstructions : ''}
                                    </Text>
                                </View>
                                : null}
                            {curBooking && curBooking.deliveryInstructions ?
                                <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                    <Text style={styles.textHeading}>{t('deliveryInstructions')}</Text>
                                    <Text style={styles.textContent}>
                                        {curBooking ? curBooking.deliveryInstructions : ''}
                                    </Text>
                                </View>
                                : null}
                        </View>
                        <View style={{ flexDirection: 'row', alignSelf: 'center', height: 40 }}>
                            <OldTouch
                                loading={false}
                                onPress={() => setPurchaseInfoModalStatus(false)}
                                style={styles.modalButtonStyle}
                            >
                                <Text style={styles.modalButtonTextStyle}>{t('ok')}</Text>
                            </OldTouch>
                        </View>
                    </View>
                </View>
            </Modal>

        )
    }




    const UserInfoModal = () => {
        return (
            <Modal
                animationType="fade"
                transparent={true}
                visible={userInfoModalStatus}
                onRequestClose={() => {
                    setUserInfoModalStatus(false);
                }}
            >
                <View style={styles.centeredView}>
                    <View style={styles.modalView}>
                        <View style={{ width: '100%' }}>
                            <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                <Text style={styles.textHeading1}>{t('otherPersonPhone')}</Text>
                                <Text style={styles.textContent1} onPress={() => onPressCall(curBooking.otherPersonPhone)}>
                                    <Icon
                                        name="call"
                                        type="ionicon"
                                        size={15}
                                        color={colors.INDICATOR_BLUE}
                                    />
                                    {curBooking ? curBooking.otherPersonPhone : ''}
                                </Text>
                            </View>
                            <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                <Text style={styles.textHeading1}>{t('senderPersonPhone')}</Text>

                                <Text style={styles.textContent1} onPress={() => onPressCall(curBooking.customer_contact)}>
                                    <Icon
                                        name="call"
                                        type="ionicon"
                                        size={15}
                                        color={colors.INDICATOR_BLUE}
                                    />
                                    {curBooking ? curBooking.customer_contact : ''}
                                </Text>
                            </View>
                        </View>
                        <View style={{ flexDirection: 'row', alignSelf: 'center', height: 40 }}>
                            <OldTouch
                                loading={false}
                                onPress={() => setUserInfoModalStatus(false)}
                                style={styles.modalButtonStyle}
                            >
                                <Text style={styles.modalButtonTextStyle}>{t('ok')}</Text>
                            </OldTouch>
                        </View>
                    </View>
                </View>
            </Modal>

        )
    }

    const onShare = async (curBooking) => {
        try {
            const result = await Share.share({
                message: curBooking.otp + t('otp_sms')
            });
        } catch (error) {
            alert(error.message);
        }
    };

    useEffect(() => {
        const unsubscribe = props.navigation.addListener('focus', () => {
            pageActive.current = true;
        });
        return unsubscribe;
    }, [props.navigation, pageActive.current]);

    useEffect(() => {
        const unsubscribe = props.navigation.addListener('blur', () => {
            pageActive.current = false;
            if (role == 'customer') {
                dispatch(stopLocationFetch(bookingId));
            }
        });
        return unsubscribe;
    }, [props.navigation, pageActive.current]);

    // Cleanup location tracking when component unmounts
    useEffect(() => {
        return () => {
            if (role == 'customer') {
                dispatch(stopLocationFetch(bookingId));
            }

            // Store final API data when component unmounts
            if (bookingId && simpleLocationTracker.isActive()) {
                simpleLocationTracker.endBookingTracking(bookingId);
            }
        };
    }, [role, bookingId]);

    useEffect(() => {
        pageActive.current = true;
        return () => {
            pageActive.current = false;
        };
    }, []);

    const submitComplain = (curBooking) => {
        Alert.alert(
            t('panic_text'),
            t('panic_question'),
            [
                {
                    text: t('cancel'),
                    onPress: () => { },
                    style: 'cancel'
                },
                {
                    text: t('ok'), onPress: async () => {
                        let call_link = Platform.OS == 'android' ? 'tel:' + settings.panic : 'telprompt:' + settings.panic;
                        Linking.openURL(call_link);

                        let obj = {};
                        obj.bookingId = curBooking.id,
                            obj.complainDate = new Date().getTime();

                        if (auth.profile && auth.profile && auth.profile.usertype && auth.profile.usertype == 'driver') {
                            obj.user_name = curBooking.driver_name;
                            obj.contact = curBooking.driver_contact;
                        }
                        if (auth.profile && auth.profile && auth.profile.usertype && auth.profile.usertype == 'customer') {
                            obj.user_name = curBooking.customer_name;
                            obj.contact = curBooking.customer_contact;
                        }
                        obj.user_type = auth.profile && auth.profile && auth.profile.usertype ? auth.profile.usertype : null;
                        dispatch(editSos(obj, "Add"));
                    }
                }
            ],
            { cancelable: false }
        )
    }

    // Calculate distance to pickup (in meters)
    let distanceToPickup = 99999;
    if (curBooking && ((isDriver && gpsLocation) || (!isDriver && lastLocation)) && curBooking.pickup) {
        const loc = isDriver ? gpsLocation : lastLocation;
        distanceToPickup = api.GetDistance(
            loc.lat,
            loc.lng,
            curBooking.pickup.lat,
            curBooking.pickup.lng
        ) * 1000; // assuming GetDistance returns km
    }

    // Calculate distance to drop (in meters)
    let distanceToDrop = 99999;
    if (curBooking && ((isDriver && gpsLocation) || (!isDriver && lastLocation)) && curBooking.drop) {
        const loc = isDriver ? gpsLocation : lastLocation;
        distanceToDrop = api.GetDistance(
            loc.lat,
            loc.lng,
            curBooking.drop.lat,
            curBooking.drop.lng
        ) * 1000; // assuming GetDistance returns km
    }

    // Determine distance thresholds based on location type
    const pickupThreshold = isAirportLocation(curBooking?.pickup?.add) ? 2000 : 250; // 2000m for airports, 250m for others
    const dropThreshold = isAirportLocation(curBooking?.drop?.add) ? 2000 : 250; // 2000m for airports, 250m for others

    // Debug function to check location tracking status
    const debugLocationTracking = () => {
        console.log('=== Location Tracking Debug ===');
        console.log('Current Booking:', curBooking?.id, curBooking?.status);
        console.log('Role:', role);
        console.log('Is Driver:', isDriver);
        console.log('Driver Active Status:', auth.profile.driverActiveStatus);
        console.log('GPS Location (Device):', gpsLocation);
        console.log('Last Location (Backend):', lastLocation);
        console.log('Marker Location:', { lat: markerLat, lng: markerLng });
        console.log('GPS Error:', gps.error);
        console.log('Page Active:', pageActive.current);
        console.log('Last Coords:', lastCoords);
        console.log('Live Route Coords:', liveRouteCoords?.length || 0);
        console.log('==============================');
    };

    // Debug GPS location updates
    useEffect(() => {
        if (gpsLocation && role === 'driver') {
            console.log('🚗 GPS Location Updated:', gpsLocation);
        }
    }, [gpsLocation, role]);

    const changePermission = async () => {
        try {
            // Increment GPS error count
            setGpsErrorCount(prevCount => prevCount + 1);

            let permResp = await Location.requestForegroundPermissionsAsync();
            if (permResp.status == 'granted') {
                let { status } = await Location.requestBackgroundPermissionsAsync();
                if (status === 'granted') {
                    dispatch(updateProfile({ driverActiveStatus: true }));
                    setChecks({ ...checks, driverActiveStatus: true });
                    // Reset GPS error count on successful permission grant
                    setGpsErrorCount(0);
                } else {
                    // Handle background permission denied
                    if (Platform.OS == 'ios') {
                        Alert.alert(
                            'Background Location Permission Required',
                            'Please enable background location access in Settings to continue.',
                            [
                                { text: 'Cancel', style: 'cancel' },
                                { text: 'Open Settings', onPress: () => Linking.openSettings() }
                            ]
                        );
                    } else {
                        Alert.alert(
                            'Background Location Permission Required',
                            'Please enable background location access to continue.',
                            [
                                { text: 'Cancel', style: 'cancel' },
                                { text: 'Open Settings', onPress: () => startActivityAsync(ActivityAction.LOCATION_SOURCE_SETTINGS) }
                            ]
                        );
                    }
                }
            } else {
                // Handle foreground permission denied
                if (Platform.OS == 'ios') {
                    Alert.alert(
                        'Location Permission Required',
                        'Please enable location access in Settings to continue.',
                        [
                            { text: 'Cancel', style: 'cancel' },
                            { text: 'Open Settings', onPress: () => Linking.openSettings() }
                        ]
                    );
                } else {
                    Alert.alert(
                        'Location Permission Required',
                        'Please enable location access to continue.',
                        [
                            { text: 'Cancel', style: 'cancel' },
                            { text: 'Open Settings', onPress: () => startActivityAsync(ActivityAction.LOCATION_SOURCE_SETTINGS) }
                        ]
                    );
                }
            }
        } catch (error) {
            console.error('Error requesting location permissions:', error);
            Alert.alert(
                'Permission Error',
                'An error occurred while requesting location permissions. Please try again.',
                [{ text: 'OK' }]
            );
        }
    }

    // Add this helper function inside your component
    const hasOtherActiveRide = () => {
        if (!activeBookings || !curBooking) return false;
        return activeBookings.some(
            b =>
                b.id !== curBooking.id &&
                ['ARRIVED', 'STARTED', 'PENDING'].includes(b.status)
        );
    };

    // Add this helper function inside your component
    const showCompleteActiveRideAlert = () => {
        Alert.alert(
            'Active Ride Incomplete',
            'You must complete your current active ride before starting or arriving at another ride.',
            [{ text: 'OK' }]
        );
    };

    // Add a useEffect to update liveRouteCoords when driver's GPS location changes during STARTED
    useEffect(() => {
        if (
            curBooking &&
            curBooking.status === 'STARTED' &&
            isDriver &&
            gpsLocation &&
            curBooking.drop
        ) {
            // Use fitMap to update liveRouteCoords and map
            fitMap(gpsLocation, curBooking.drop);
        }
    }, [gpsLocation?.lat, gpsLocation?.lng, curBooking?.status]);

    // Add these new state variables after line 96 (after the existing useState declarations)
    const [userInteracting, setUserInteracting] = useState(false);
    const [centerOnCar, setCenterOnCar] = useState(true);

    // Add these functions after line 1374 (after the debugLocationTracking function)
    // Handle region changes and auto-center on car
    const onRegionChangeComplete = (newRegion, gesture) => {
        // Only auto-center if centering is enabled and user isn't manually interacting
        if (centerOnCar && !userInteracting && markerLat && markerLng) {
            // Small delay to prevent infinite loops
            setTimeout(() => {
                if (mapRef.current) {
                    mapRef.current.animateToRegion({
                        latitude: markerLat,
                        longitude: markerLng,
                        latitudeDelta: newRegion.latitudeDelta,
                        longitudeDelta: newRegion.longitudeDelta,
                    }, 300);
                }
            }, 100);
        }

        // Reset user interaction flag after delay
        if (userInteracting) {
            setTimeout(() => setUserInteracting(false), 2000);
        }
    };

    // Detect when user starts interacting with map
    const onMapTouchStart = () => {
        setUserInteracting(true);
        setCenterOnCar(false); // Disable auto-centering during user interaction
    };

    // Function to manually re-center on car
    const recenterOnCar = () => {
        if (mapRef.current && markerLat && markerLng) {
            setCenterOnCar(true);
            setUserInteracting(false);
            mapRef.current.animateToRegion({
                latitude: markerLat,
                longitude: markerLng,
                latitudeDelta: latitudeDelta,
                longitudeDelta: longitudeDelta,
            }, 1000);
        }
    };
    // Map booking status to background color
    const getStatusBgColor = (status) => {
        const s = (status || '').toUpperCase();
        switch (s) {
            case 'PAYMENT_PENDING': return '#FFF4E5';
            case 'NEW': return '#FFFDE7';
            case 'ACCEPTED': return '#E6F7FF';
            case 'ARRIVED': return '#E8F5E9';
            case 'STARTED': return '#E3F2FD';
            case 'REACHED': return '#F3E5F5';
            case 'PENDING': return '#FFFDE7';
            case 'COMPLETE': return '#F1F8E9';
            case 'CANCELLED': return '#FFEBEE';
            default: return colors.WHITE;
        }
    };
    return (
        <View style={styles.mainContainer}>
            {/* GPS Error Alert with error count */}
            {gps.error && role === 'driver' ?
                <View style={[styles.alrt, { flexDirection: isRTL ? 'row-reverse' : 'row', }]}>
                    <View style={[styles.alrt1, { flexDirection: isRTL ? 'row-reverse' : 'row', }]}>
                        <Icon name="alert-circle" type="ionicon" color={colors.RED} size={18} />
                        <Text style={{ fontSize: 14, fontFamily: fonts.Bold, color: colors.BLACK, marginLeft: 3 }}>
                            {t('always_on')} {gpsErrorCount > 0 ? `(${gpsErrorCount})` : ''}
                        </Text>
                    </View>
                    <Button
                        onPress={changePermission}
                        title={t('fix').toUpperCase()}
                        titleStyle={styles.checkButtonTitle}
                        buttonStyle={styles.checkButtonStyle}
                    />
                    {/* {__DEV__ && (
                        <Button 
                            onPress={debugLocationTracking} 
                            title="DEBUG" 
                            titleStyle={styles.checkButtonTitle} 
                            buttonStyle={[styles.checkButtonStyle, { backgroundColor: colors.BLUE, marginLeft: 5 }]} 
                        />
                    )} */}
                </View>
                : null}
            <View style={styles.mapcontainer}>
                {curBooking && curBooking.pickup && curBooking.pickup.lat && curBooking.pickup.lng ? (
                    <MapView
                        ref={mapRef}
                        style={styles.map}
                        provider={PROVIDER_GOOGLE}
                        initialRegion={{
                            latitude: curBooking.pickup.lat,
                            longitude: curBooking.pickup.lng,
                            latitudeDelta: latitudeDelta,
                            longitudeDelta: longitudeDelta
                        }}
                        minZoomLevel={3}
                        onRegionChangeComplete={onRegionChangeComplete}
                        onTouchStart={onMapTouchStart}
                        onPanDrag={() => {
                            setUserInteracting(true);
                            setCenterOnCar(false);
                        }}
                    >

                        {(curBooking.status == 'ACCEPTED' || curBooking.status == 'ARRIVED' || curBooking.status == 'STARTED') && lastLocation ?
                            <Marker.Animated
                                ref={markerRef}
                                coordinate={animatedRegion}
                            >
                                <Image
                                    source={carImageIcon}
                                    style={{ height: 40, width: 40 }}
                                />
                            </Marker.Animated>
                            : null}

                        <Marker
                            coordinate={{ latitude: (curBooking.pickup.lat), longitude: (curBooking.pickup.lng) }}
                            title={curBooking.pickup.add}>
                            <Image source={require("../../assets/images/green_pin.png")} style={{ height: 35, width: 35 }} />
                        </Marker>
                        {curBooking != null && curBooking.waypoints && curBooking.waypoints.length > 0 ? curBooking.waypoints.map((point, index) => {
                            return (
                                <Marker
                                    coordinate={{ latitude: point.lat, longitude: point.lng }}
                                    pinColor={colors.GREEN}
                                    title={point.add}
                                    key={point.add}
                                >
                                    <Image source={require("../../assets/images/rsz_2red_pin.png")} style={{ height: 35, width: 35 }} />
                                </Marker>
                            )
                        })
                            : null}
                        <Marker
                            coordinate={{ latitude: (curBooking.drop.lat), longitude: (curBooking.drop.lng) }}
                            title={curBooking.drop.add}>
                            <Image source={require("../../assets/images/rsz_2red_pin.png")} style={{ height: 35, width: 35 }} />
                        </Marker>

                        {liveRouteCoords && (curBooking.status == 'ACCEPTED' || curBooking.status == 'STARTED') ?
                            <Polyline
                                coordinates={liveRouteCoords}
                                strokeWidth={5}
                                strokeColor={colors.INDICATOR_BLUE}
                            />
                            : null}

                        {(curBooking.status == 'NEW' || curBooking.status == 'ARRIVED' || curBooking.status == 'REACHED') && curBooking.coords ?
                            <Polyline
                                coordinates={curBooking.coords}
                                strokeWidth={4}
                                strokeColor={colors.INDICATOR_BLUE}
                            />
                            : null}
                    </MapView>
                ) : (
                    <View style={[styles.map, { justifyContent: 'center', alignItems: 'center' }]}>
                        <Text style={{ fontFamily: fonts.Regular, color: colors.BLACK }}>
                            Loading booking details...
                        </Text>
                    </View>
                )}
                <View style={[styles.menuIcon, isRTL ? { right: 15 } : { left: 15 }]}>
                    <TouchableOpacity onPress={() => { goBack() }} style={styles.menuIconButton} >
                        <Icon
                            name={isRTL ? 'arrow-right' : 'arrow-left'}
                            type='font-awesome'
                            color='#517fa4'
                            size={26}
                        />
                    </TouchableOpacity>
                </View>
                <View style={[isRTL ? styles.topTitle1 : styles.topTitle, { height: settings && settings.otp_secure ? 60 : 45, backgroundColor: getStatusBgColor(curBooking && curBooking.status) }]}>
                    <Text style={styles.cabText}>{t('booking_status')}: <Text style={styles.cabBoldText}>{curBooking && curBooking.status ? t(curBooking.status) : null} </Text></Text>
                    {curBooking && curBooking.status == 'ACCEPTED' ?
                        <Text style={styles.cabText}>{curBooking && curBooking.status == 'ACCEPTED' || settings && settings.showLiveRoute ? '( ' + arrivalTime + ' ' + t('mins') + ' )' : ''}</Text>
                        : null}
                    {role == 'customer' && settings && settings.otp_secure ?
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', padding: 1, alignSelf: 'center' }}>
                            <Text style={styles.otpText}>{curBooking ? t('otp') + curBooking.otp : null}</Text>
                            <View>
                                <TouchableOpacity onPress={() => onShare(curBooking)}>
                                    <Icon
                                        name="share-social"
                                        type="ionicon"
                                        size={22}
                                        color={colors.INDICATOR_BLUE} />
                                </TouchableOpacity>
                            </View>
                        </View>
                        : null}
                </View>

                {(curBooking && curBooking.status && auth.profile && auth.profile.uid && ((['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED'].indexOf(curBooking.status) != -1))) && (settings && settings.panic && settings.panic.length > 0) && (role === 'driver' || appConsts.canCall) ?
                    <TouchableOpacity style={[styles.floatButton, isRTL ? { right: 10, bottom: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 285 : 360 } : { left: 10, bottom: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 300 : (role == 'customer' && (curBooking && curBooking.status == 'ACCEPTED')) ? 350 : 330 }]}
                        onPress={() => submitComplain(curBooking)} >
                        <Text style={[styles.driverNameText, { color: colors.WHITE }]}>{t('sos').toUpperCase()}</Text>
                    </TouchableOpacity>
                    : null}

                {curBooking && !(curBooking.status == 'NEW') ?
                    <TouchableOpacity
                        style={[styles.floatButton, isRTL ? { left: 10, bottom: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 345 : 410 } : { right: 10, bottom: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 360 : (role == 'customer' && (curBooking && curBooking.status == 'ACCEPTED')) ? 408 : 388 }]}
                        // onPress={() => openWhatsApp()}
                        onPress={settings && settings.chatViaWhatsApp === true ? () => openWhatsApp() : () => chat()}
                    >
                        <Icon
                            name="chatbubbles"
                            type="ionicon"
                            size={30}
                            color={colors.WHITE}
                        />
                    </TouchableOpacity>
                    : null}
                {curBooking && !(curBooking.status == 'NEW') ?
                    <TouchableOpacity
                        style={[styles.floatButton, isRTL ? { left: 10, bottom: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 285 : 350 } : { right: 10, bottom: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 300 : (role == 'customer' && (curBooking && curBooking.status == 'ACCEPTED')) ? 350 : 330 }]}
                        onPress={() => role == 'customer' ? onPressCall(curBooking.driver_contact) : (curBooking.otherPersonPhone && curBooking.otherPersonPhone.length > 0 ? onPressCall(curBooking.otherPersonPhone) : onPressCall(curBooking.customer_contact))}
                    >
                        <Icon
                            name="call"
                            type="ionicon"
                            size={30}
                            color={colors.WHITE}
                        />
                    </TouchableOpacity>
                    : null}

            </View>
            <View style={[styles.vew1,
            { minHeight: (role == 'customer' && (curBooking && curBooking.status == 'NEW')) ? 220 : (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'REACHED' || curBooking.status == 'PAID' || (curBooking && curBooking.status == 'STARTED'))) ? 240 : 285 }]}>
                {curBooking && curBooking.status != "NEW" ?
                    <View style={{ minHeight: (role == 'customer' && ((curBooking && curBooking.status == 'ARRIVED') || curBooking.status == 'PAID' || curBooking.status == 'REACHED' || (curBooking && curBooking.status == 'STARTED'))) ? '10%' : '8%', justifyContent: 'center', }}>
                        {role == 'customer' ?
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', padding: 3, alignSelf: 'flex-start', width: '100%', borderTopRightRadius: 10, borderTopLeftRadius: 10 }}>
                                <Image source={curBooking.driver_image ? { uri: curBooking.driver_image } : require('../../assets/images/profilePic.png')} style={{ height: 55, width: 55, borderRadius: 30, marginLeft: isRTL ? 0 : 1 }} />
                                <View style={{ width: '85%', flexDirection: isRTL ? 'row-reverse' : 'row', justifyContent: 'space-between' }}>
                                    <View style={{ width: '60%', justifyContent: 'center' }}>
                                        <Text style={[styles.driverNameText, { textAlign: isRTL ? 'right' : 'left' }]} numberOfLines={1}>{curBooking.driver_name}</Text>
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                                            <StarRating
                                                maxStars={1}
                                                starSize={20}
                                                enableHalfStar={true}
                                                color={MAIN_COLOR}
                                                emptyColor={MAIN_COLOR}
                                                rating={parseFloat(curBooking.driverRating)}
                                                style={[styles.ratingContainerStyle, isRTL ? { marginRight: 0, transform: [{ scaleX: -1 }] } : { scaleX: 1 }]}
                                                onChange={() => {
                                                    //console.log('hello')
                                                }}
                                            />
                                            <Text style={[styles.driverNameText, { textAlign: isRTL ? 'right' : 'left' }]} numberOfLines={1}>{curBooking.driverRating}</Text>
                                        </View>
                                    </View>
                                    <View style={{ marginTop: 2, alignItems: 'center', marginLeft: isRTL ? 10 : 0, marginRight: isRTL ? 0 : 10, width: '40%' }}>
                                        <Image source={{ uri: curBooking.carImage }} resizeMode={'contain'} style={{ height: 40, width: 60, }} />
                                        <View style={{ marginTop: 2, alignItems: 'center', width: '100%' }}>
                                            <Text numberOfLines={2} style={styles.cabNameText}>{curBooking.carType}</Text>
                                        </View>
                                        <View style={{ marginTop: 2, alignItems: 'center', width: '100%' }}>
                                            <Text numberOfLines={2} style={styles.cabNameText}>{curBooking.vehicle_number}</Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                            :
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', padding: 3, alignSelf: 'flex-start', width: '100%', borderTopRightRadius: 10, borderTopLeftRadius: 10 }}>
                                <Image source={curBooking.customer_image ? { uri: curBooking.customer_image } : require('../../assets/images/profilePic.png')} style={{ height: 55, width: 55, borderRadius: 25, marginLeft: isRTL ? 0 : 5 }} />
                                <View style={{ flex: 1, flexDirection: isRTL ? 'row-reverse' : 'row', justifyContent: 'space-between' }}>
                                    <View style={{ flex: 1, justifyContent: 'center' }}>
                                        <Text style={[styles.driverNameText, { textAlign: isRTL ? 'right' : 'left' }]} >{curBooking.customer_name}</Text>
                                    </View>
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginLeft: isRTL ? 5 : 0, marginRight: isRTL ? 0 : 5 }}>

                                        {role == 'driver' && appConsts.showBookingOptions ?
                                            <TouchableOpacity
                                                style={[styles.floatButton1, { marginHorizontal: 3 }]}
                                                onPress={() => setPurchaseInfoModalStatus(true)}
                                            >
                                                <Icon
                                                    name="cube"
                                                    type="ionicon"
                                                    size={30}
                                                    color={colors.WHITE}
                                                />
                                            </TouchableOpacity>
                                            : null}
                                        {role == 'driver' ?
                                            <TouchableOpacity
                                                style={styles.floatButton1}
                                                onPress={() =>
                                                    startNavigation()
                                                }
                                            >
                                                <Icon
                                                    name="navigate-circle"
                                                    type="ionicon"
                                                    size={30}
                                                    color={colors.WHITE}
                                                />
                                            </TouchableOpacity>
                                            : null}
                                    </View>
                                </View>

                            </View>
                        }
                    </View>
                    : null}
                <View style={{
                    backgroundColor: colors.WHITE, alignSelf: 'center', borderWidth: 1, color: colors.FOOTERTOP, borderRadius: 10, height: 100
                }}>
                    <ScrollView style={{ flex: 1 }}>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginTop: 5, marginBottom: 8 }}>
                            <View style={styles.locationStyle}>
                                <Ionicons name="location-sharp" size={22} color="white" />
                            </View>
                            <Text numberOfLines={1} style={[styles.textStyle, { textAlign: isRTL ? 'right' : 'left', width: '85%' }]}>{curBooking ? curBooking.pickup.add : ""}</Text>
                        </View>
                        {curBooking != null && curBooking.waypoints && curBooking.waypoints.length > 0 ? curBooking.waypoints.map((point, index) => {
                            return (
                                <View key={"key" + index} style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 8 }}>
                                    <View style={styles.locationStyle}>
                                        <Ionicons name="location-sharp" size={24} color="white" />
                                    </View>
                                    <Text numberOfLines={1} style={[styles.textStyle, { textAlign: isRTL ? 'right' : 'left', width: '85%' }]}>{curBooking ? point.add : ""}</Text>
                                </View>
                            )
                        })
                            : null}
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 2 }}>
                            <View style={styles.locationStyle}>
                                <Ionicons name="location-sharp" size={24} color="white" />
                            </View>
                            <Text numberOfLines={1} style={[styles.textStyle, { textAlign: isRTL ? 'right' : 'left', width: '85%' }]}>{curBooking ? curBooking.drop.add : ""}</Text>
                        </View>
                    </ScrollView>
                </View>
                {(curBooking && curBooking.status == 'ARRIVED' || curBooking && curBooking.status == 'ACCEPTED' || curBooking && curBooking.status == 'REACHED' || curBooking && curBooking.status == 'PAID' || curBooking && curBooking.status == 'STARTED') ?
                    <View style={{ justifyContent: 'space-around', alignItems: 'center', flexDirection: isRTL ? 'row-reverse' : 'row', height: 55 }}>
                        <View style={{ alignItems: 'center' }}>
                            <Text style={{ fontFamily: fonts.Regular }}>{t('distance')}</Text>
                            <Text style={{ fontFamily: fonts.Bold }}>{curBooking ? parseFloat(curBooking.estimateDistance).toFixed(settings.decimal) : 0} {settings.convert_to_mile ? t('mile') : t('km')}</Text>
                        </View>
                        <View style={{ alignItems: 'center' }}>
                            <Text style={{ fontFamily: fonts.Regular }}>{t('time')}</Text>
                            <Text style={{ fontFamily: fonts.Bold }}>{curBooking.estimateTime ? parseFloat(curBooking.estimateTime / 60).toFixed(0) : 0} {t('mins')}</Text>
                        </View>
                        <View style={{ alignItems: 'center' }}>
                            <Text style={{ fontFamily: fonts.Regular }}>{t('cost')}</Text>
                            {/* <Text style={{fontWeight:'bold'}}>{curBooking? curBooking.distance:null}</Text> */}
                            {settings && settings.swipe_symbol === false ? (
                                <View style={{ alignItems: 'center' }}>
                                    <Text style={{ fontFamily: fonts.Bold }}>
                                        {settings.symbol} {curBooking && curBooking.trip_cost > 0
                                            ? parseFloat(curBooking.trip_cost).toFixed(settings.decimal)
                                            : curBooking && curBooking.estimate
                                                ? parseFloat(curBooking.estimate).toFixed(settings.decimal)
                                                : 0}
                                    </Text>
                                    {curBooking && curBooking.tips && parseFloat(curBooking.tips) > 0 ? (
                                        <Text style={{ fontFamily: fonts.Regular, color: '#34C759', fontSize: 12 }}>
                                            + {t('Tips')}: {settings.symbol}{parseFloat(curBooking.tips).toFixed(settings.decimal)}
                                        </Text>
                                    ) : null}
                                </View>
                            ) : (
                                <View style={{ alignItems: 'center' }}>
                                    <Text style={{ fontFamily: fonts.Bold }}>
                                        {(() => {
                                            // Defensive: parseFloat and .toFixed only on numbers, and tips may be string or undefined
                                            let tips = 0;
                                            // Only add tips if status is not 'PENDING'
                                            if (
                                                curBooking &&
                                                curBooking.tips &&
                                                curBooking.status &&
                                                curBooking.status.toLowerCase() !== 'pending'
                                            ) {
                                                tips = parseFloat(curBooking.tips);
                                            }
                                            if (curBooking && curBooking.trip_cost > 0) {
                                                const tripCost = parseFloat(curBooking.trip_cost) || 0;
                                                return (tripCost + tips).toFixed(settings.decimal);
                                            } else if (curBooking && curBooking.estimate) {
                                                const estimate = parseFloat(curBooking.estimate) || 0;
                                                return (estimate + tips).toFixed(settings.decimal);
                                            } else {
                                                return tips.toFixed(settings.decimal);
                                            }
                                        })()} {settings.symbol}
                                    </Text>
                                    {curBooking && curBooking.tips && parseFloat(curBooking.tips) > 0 ? (
                                        <Text style={{ fontFamily: fonts.Regular, color: '#34C759', fontSize: 12 }}>
                                            + {t('Tips')}: {parseFloat(curBooking.tips).toFixed(settings.decimal)} {settings.symbol}
                                        </Text>
                                    ) : null}
                                </View>
                            )}
                        </View>
                    </View>
                    : null}
                {curBooking && curBooking.status == "NEW" && (curBooking.bookLater == false || (curBooking.bookLater && (((new Date(curBooking.tripdate)) - (new Date())) / (1000 * 60)) <= 15)) ?
                    <View style={{ width: width, height: 'auto', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                        <Image style={{ width: 40, height: 40 }} source={require('../../assets/images/loader.gif')} />
                        <TouchableOpacity onPress={() => { setSearchModalVisible(!searchModalVisible) }}>
                            <Text style={{ fontSize: 22, fontFamily: fonts.Regular }}>{curBooking.driverOffers ? t('selectBid') : t('searching')}</Text>
                        </TouchableOpacity>
                    </View>
                    : null}
                {curBooking && curBooking.status == "NEW" && curBooking.bookLater && (((new Date(curBooking.tripdate)) - (new Date())) / (1000 * 60)) > 15 ?
                    <View style={{ flex: 1, width: width, height: 'auto', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                        <Text style={{ fontSize: 16, fontFamily: fonts.Regular }}>{t('trip_start_time') + ":  "}</Text>
                        <Text style={{ fontFamily: fonts.Bold, fontSize: 16 }}>{moment(curBooking.tripdate).format('MM/DD/YYYY h:mm A')}</Text>
                    </View>
                    : null}
                {
                    renderButtons()
                }

                {/* API Usage Display - Show for drivers in development mode */}
                {__DEV__ && role === 'driver' && (
                    <View>
                        <ApiUsageDisplay
                            bookingData={curBooking}
                            showDetails={true}
                        />
                        <TouchableOpacity
                            style={styles.testButton}
                            onPress={async () => {
                                console.log('🧪 Manual API data storage test...');
                                const result = await simpleLocationTracker.storeApiCountInBooking(bookingId);
                                if (result) {
                                    console.log('✅ Manual test successful:', result);
                                    Alert.alert('Success', `API data stored: ${result.totalApiCalls} calls, $${result.apiCost.toFixed(4)} cost`);
                                } else {
                                    console.log('❌ Manual test failed');
                                    Alert.alert('Error', 'Failed to store API data');
                                }
                            }}
                        >
                            <Text style={styles.testButtonText}>🧪 Test API Storage</Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
            {
                PurchaseInfoModal()
            }
            {
                UserInfoModal()
            }
            {
                cancelModal()
            }
            {
                alertModal()
            }
            {
                searchModal()
            }
            {
                confirmModal()
            }
            <OtpModal
                modalvisable={otpModalVisible}
                requestmodalclose={() => { setOtpModalVisible(false) }}
                otp={curBooking ? curBooking.otp : ''}
                onMatch={(value) => value ? appConsts.hasStartOtp ? startBooking() : endBooking() : null}
            />
            {(curBooking && (curBooking.status == 'ACCEPTED' || curBooking.status == 'ARRIVED' || curBooking.status == 'STARTED') && markerLat && markerLng) ?
                <TouchableOpacity
                    style={[styles.recenterButton, isRTL ? { left: 15 } : { right: 15 }]}
                    onPress={recenterOnCar}
                >
                    <Icon
                        name="locate"
                        type="ionicon"
                        size={24}
                        color={colors.WHITE}
                    />
                </TouchableOpacity>
                : null}
        </View>
    );

}

const styles = StyleSheet.create({
    mainContainer: { flex: 1, backgroundColor: colors.WHITE, },
    headerStyle: {
        backgroundColor: colors.HEADER,
        borderBottomWidth: 0,
    },
    headerInnerStyle: {
        marginLeft: 10,
        marginRight: 10
    },
    accpt: {
        width: 90,
        backgroundColor: colors.GREEN,
        height: 40,
        borderRadius: 10,
        marginLeft: 10
    },
    vew1: {
        width: '96%',
        backgroundColor: colors.WHITE,
        marginTop: -8,
        shadowColor: 'black',
        shadowOffset: { width: 0, height: -6 },
        shadowOpacity: Platform.OS == 'ios' ? 0.1 : 0.8,
        shadowRadius: 3,
        elevation: Platform.OS == 'ios' ? 2 : 9,
        position: 'absolute',
        bottom: 25,
        alignSelf: 'center',
        borderRadius: 10
    },
    vew: {
        height: 150,
        marginBottom: 20,
        borderColor: 'black',
        borderRadius: 10,
        backgroundColor: colors.WHITE,
        shadowColor: colors.BLACK,
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
        borderWidth: 1
    },
    headerTitleStyle: {
        color: colors.WHITE,
        fontFamily: fonts.Bold,
        fontSize: 20
    },
    topContainer: { flex: 1.5, borderTopWidth: 0, alignItems: 'center', backgroundColor: colors.HEADER, paddingEnd: 20 },
    topLeftContainer: {
        flex: 1.5,
        alignItems: 'center'
    },
    topRightContainer: {
        flex: 9.5,
        justifyContent: 'space-between',
    },
    circle: {
        height: 15,
        width: 15,
        borderRadius: 15 / 2,
        backgroundColor: colors.LIGHT_YELLOW
    },
    staightLine: {
        height: height / 25,
        width: 1,
        backgroundColor: colors.LIGHT_YELLOW
    },
    square: {
        height: 17,
        width: 17,
        backgroundColor: colors.MAP_SQUARE
    },
    whereButton: { flex: 1, justifyContent: 'center', borderBottomColor: colors.WHITE, borderBottomWidth: 1 },
    whereContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', },
    whereText: { flex: 9, fontFamily: fonts.Regular, fontSize: 14, fontWeight: '400', color: colors.WHITE },
    iconContainer: { flex: 1, },
    dropButton: { flex: 1, justifyContent: 'center' },
    mapcontainer: {
        flex: 7,
        width: width,
    },
    bottomContainer: { alignItems: 'center' },
    map: {
        flex: 1,
        minHeight: 400,
        ...StyleSheet.absoluteFillObject,
    },
    locationStyle: {
        height: 35,
        width: 35,
        backgroundColor: MAIN_COLOR,
        justifyContent: 'center',
        borderRadius: 25,
        alignItems: 'center',
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.5,
        shadowRadius: 5,
        elevation: 2,
        marginHorizontal: 5
    },
    otpContainer: { flex: 0.8, backgroundColor: colors.BOX_BG, width: width, flexDirection: 'row', justifyContent: 'space-between' },
    cabText: { paddingLeft: 10, alignSelf: 'center', color: colors.BLACK, fontFamily: fonts.Regular },
    cabBoldText: { fontFamily: fonts.Bold },
    otpText: { color: colors.BLACK, fontFamily: fonts.Bold, },
    cabDetailsContainer: { flex: 2.5, backgroundColor: colors.WHITE, flexDirection: 'row', position: 'relative', zIndex: 1 },
    cabDetails: { flex: 19 },
    cabName: { flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' },
    cabNameText: { color: colors.BLACK, fontFamily: fonts.Bold, fontSize: 14 },
    cabPhoto: { flex: 1, alignItems: 'center', justifyContent: 'center' },
    cabImage: { width: 100, height: height / 20, marginBottom: 5, marginTop: 5 },
    cabNumber: { flex: 1, alignItems: 'center', justifyContent: 'center' },
    cabNumberText: { color: colors.BUTTON, fontFamily: fonts.Bold, fontSize: 13 },
    verticalDesign: { flex: 2, height: 50, width: 1, alignItems: 'center' },
    triangle: {
        width: 0,
        height: 0,
        backgroundColor: colors.TRANSPARENT,
        borderStyle: 'solid',
        borderLeftWidth: 9,
        borderRightWidth: 9,
        borderBottomWidth: 10,
        borderLeftColor: colors.TRANSPARENT,
        borderRightColor: colors.TRANSPARENT,
        borderBottomColor: colors.BOX_BG,
        transform: [
            { rotate: '180deg' }
        ],

        marginTop: -1,
        overflow: 'visible'
    },
    verticalLine: { height: height / 18, width: 0.5, backgroundColor: colors.BLACK, alignItems: 'center', marginTop: 10 },
    driverDetails: { flex: 19, alignItems: 'center', justifyContent: 'center', },
    driverPhotoContainer: { alignItems: 'center', marginTop: 10 },
    driverPhoto: { borderRadius: height / 20 / 2, width: height / 20, height: height / 20, },
    driverNameContainer: { flex: 2.2, alignItems: 'center', justifyContent: 'center' },
    driverNameText: { color: colors.BLACK, fontFamily: fonts.Bold, fontSize: 16, marginHorizontal: 5 },
    ratingContainer: { flex: 2.4, alignItems: 'center', justifyContent: 'center' },
    alertModalContainer: { flex: 1, justifyContent: 'center', backgroundColor: colors.BACKGROUND },
    alertModalInnerContainer: { height: 200, width: (width * 0.85), backgroundColor: colors.WHITE, alignItems: 'center', alignSelf: 'center', borderRadius: 7 },
    alertContainer: { flex: 2, justifyContent: 'space-between', width: (width - 100) },
    rideCancelText: { flex: 1, top: 15, color: colors.BLACK, fontFamily: fonts.Bold, fontSize: 20, alignSelf: 'center' },
    horizontalLLine: { width: (width - 110), height: 0.5, backgroundColor: colors.BLACK, alignSelf: 'center', },
    msgContainer: { flex: 2.5, alignItems: 'center', justifyContent: 'center' },
    cancelMsgText: { color: colors.BLACK, fontFamily: fonts.Regular, fontSize: 15, alignSelf: 'center', textAlign: 'center' },
    okButtonContainer: { flex: 1, width: (width * 0.85), flexDirection: 'row', backgroundColor: colors.BUTTON, alignSelf: 'center' },
    okButtonStyle: { flexDirection: 'row', backgroundColor: colors.BUTTON, alignItems: 'center', justifyContent: 'center' },
    okButtonContainerStyle: { flex: 1, width: (width * 0.85), backgroundColor: colors.BUTTON, },

    cancelModalContainer: { flex: 1, justifyContent: 'center', backgroundColor: colors.BACKGROUND },
    cancelModalInnerContainer: { height: 400, width: width * 0.85, padding: 0, backgroundColor: colors.WHITE, alignItems: 'center', alignSelf: 'center', borderRadius: 7 },
    cancelContainer: { flex: 1, justifyContent: 'space-between', width: (width * 0.85) },
    cancelReasonContainer: { flex: 1 },
    cancelReasonText: { top: 10, color: colors.BLACK, fontFamily: fonts.Bold, fontSize: 20, alignSelf: 'center' },
    radioContainer: { flex: 8, alignItems: 'center' },
    radioText: { fontSize: 16, fontFamily: fonts.Medium, color: colors.BLACK, },
    radioContainerStyle: { paddingTop: 30, marginLeft: 10 },
    radioStyle: { paddingBottom: 25 },
    cancelModalButtosContainer: { flex: 1, alignItems: 'center', justifyContent: 'space-evenly', marginBottom: 10 },
    buttonSeparataor: { height: height / 35, width: 0.8, backgroundColor: colors.WHITE, alignItems: 'center', marginTop: 3 },
    cancelModalButttonStyle: { backgroundColor: colors.RED, borderRadius: 0 },
    cancelModalButtonContainerStyle: { minWidth: 140, alignSelf: 'center', borderRadius: 10 },
    textStyle: {
        fontFamily: fonts.Regular,
        fontSize: 16,
        color: colors.BLACK,

    },
    floatButton: {
        borderWidth: 1,
        borderColor: MAIN_COLOR,
        alignItems: "center",
        justifyContent: "center",
        width: 50,
        position: "absolute",
        right: 10,
        height: 50,
        backgroundColor: MAIN_COLOR,
        borderRadius: 30
    },
    floatButton1: {
        borderWidth: 1,
        borderColor: MAIN_COLOR,
        alignItems: "center",
        justifyContent: "center",
        width: 50,
        height: 50,
        backgroundColor: MAIN_COLOR,
        borderRadius: 30
    },
    centeredView: {
        flex: 1,
        justifyContent: "center",
        backgroundColor: colors.BACKGROUND
    },
    modalView: {
        margin: 20,
        backgroundColor: "white",
        borderRadius: 20,
        padding: 35,
        alignItems: "flex-start",
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    textContainerStyle: {
        flexDirection: 'column',
        marginBottom: 12,
    },
    textHeading: {
        fontSize: 12,
        fontFamily: fonts.Bold
    },
    textHeading1: {
        fontSize: 20,
        color: colors.BLACK,
        fontFamily: fonts.Regular
    },
    textContent: {
        fontSize: 14,
        margin: 4,
        fontFamily: fonts.Regular
    },
    textContent1: {
        fontSize: 20,
        color: colors.BUTTON_LOADING,
        padding: 5,
        fontFamily: fonts.Regular
    },
    modalButtonStyle: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: MAIN_COLOR,
        width: 100,
        height: 40,
        elevation: 0,
        borderRadius: 10
    },
    modalButtonTextStyle: {
        color: colors.WHITE,
        fontFamily: fonts.Bold,
        fontSize: 18
    },
    topTitle: {
        //width: 188,
        backgroundColor: colors.WHITE,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 3,
        elevation: 3,
        borderTopLeftRadius: 30,
        borderBottomLeftRadius: 30,
        justifyContent: 'center',
        position: 'absolute',
        right: 0,
        top: hasNotch ? 45 : 55
    },
    topTitle1: {
        //width: 188,
        backgroundColor: colors.WHITE,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 3,
        elevation: 3,
        borderTopRightRadius: 30,
        borderBottomRightRadius: 30,
        justifyContent: 'center',
        position: 'absolute',
        left: 0,
        top: hasNotch ? 45 : 55
    },

    addressBar: {
        borderBottomWidth: 0.7,
        bottom: 0,
        width: '100%',
        flexDirection: 'row',
        backgroundColor: colors.WHITE,
        paddingLeft: 8,
        paddingRight: 8,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 3,
        elevation: 3,
    },
    addressBarMul: {
        borderBottomWidth: 0.7,
        bottom: 0,
        width: '100%',
        flexDirection: 'row',
        backgroundColor: colors.WHITE,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 3,
        elevation: 3,
    },
    ballandsquare: {
        width: 12,
        alignItems: 'center',
        justifyContent: 'center',
        left: 5
    },
    hbox1: {
        height: 12,
        width: 12,
        borderRadius: 6,
        backgroundColor: colors.GREEN_DOT
    },
    hbox2: {
        height: 36,
        width: 1,
        backgroundColor: colors.MAP_TEXT
    },
    hbox3: {
        height: 12,
        width: 12,
        backgroundColor: colors.DULL_RED
    },
    hboxMul: {
        height: 12,
        width: 12,
        backgroundColor: colors.BUTTON_YELLOW
    },
    contentStyle: {
        justifyContent: 'center',
        width: '95%',
        height: 90,
        left: 7
    },
    contentStyleMul: {
        width: '100%',
        marginHorizontal: 5
    },
    addressStyle1: {
        borderBottomWidth: 1,
        height: 45,
        justifyContent: 'center',
        paddingTop: 2
    },
    addressStyle2: {
        height: 45,
        justifyContent: 'center',
    },
    menuIcon: {
        height: 40,
        width: 40,
        borderRadius: 25,
        backgroundColor: colors.WHITE,
        shadowColor: 'black',
        shadowOffset: { width: 2, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 5,
        elevation: 3,
        alignItems: 'center',
        position: 'absolute',
        top: hasNotch ? 40 : 55,
    },
    menuIconButton: {
        flex: 1,
        height: 50,
        width: 50,
        justifyContent: 'center',
    },
    alrt: {
        backgroundColor: colors.WHITE,
        padding: 10,
        margin: 10,
        borderRadius: 8,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        justifyContent: 'space-between',
        alignItems: 'center',
        zIndex: 1000,
    },
    alrt1: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'flex-start',
    },
    checkButtonTitle: {
        color: colors.WHITE,
        fontFamily: fonts.Bold,
        fontSize: 12,
    },
    checkButtonStyle: {
        backgroundColor: colors.GREEN,
        width: 110,
        minHeight: 40,
        borderColor: colors.TRANSPARENT,
        borderWidth: 0,
        borderRadius: 5
    },
    recenterButton: {
        position: 'absolute',
        top: hasNotch ? 120 : 130,
        backgroundColor: MAIN_COLOR,
        borderRadius: 25,
        width: 50,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 3,
        elevation: 5,
        zIndex: 1000,
    },
    testButton: {
        backgroundColor: colors.INDICATOR_BLUE || '#007AFF',
        padding: 10,
        margin: 5,
        borderRadius: 8,
        alignItems: 'center',
    },
    testButtonText: {
        color: colors.WHITE,
        fontFamily: fonts.Bold,
        fontSize: 14,
    },
});