const { withAndroidManifest, withGradleProperties } = require('@expo/config-plugins');

const with16KBSupport = (config) => {
  // Add Android manifest modifications
  config = withAndroidManifest(config, (config) => {
    try {
      const androidManifest = config.modResults;

      // Add 16KB page size support to application tag
      if (androidManifest?.manifest?.application?.[0]) {
        const application = androidManifest.manifest.application[0];

        // Ensure meta-data array exists
        if (!application['meta-data']) {
          application['meta-data'] = [];
        }

        // Check if meta-data already exists to avoid duplicates
        const existingMetaData = application['meta-data'].find(
          meta => meta.$?.['android:name'] === 'android.app.supports_16kb_page_size'
        );

        if (!existingMetaData) {
          // Add support for 16KB page sizes
          application['meta-data'].push({
            $: {
              'android:name': 'android.app.supports_16kb_page_size',
              'android:value': 'true'
            }
          });
        }
      }
    } catch (error) {
      console.warn('Warning: Could not add 16KB support to Android manifest:', error.message);
    }

    return config;
  });

  // Add Gradle properties for 16KB support
  config = withGradleProperties(config, (config) => {
    try {
      const gradleProperties = config.modResults;

      // Helper function to check if property already exists
      const hasProperty = (key) => {
        return gradleProperties.some(prop => prop.key === key);
      };

      // Add properties only if they don't exist
      if (!hasProperty('android.enableR8.fullMode')) {
        gradleProperties.push({
          type: 'property',
          key: 'android.enableR8.fullMode',
          value: 'true'
        });
      }

      if (!hasProperty('android.enableJetifier')) {
        gradleProperties.push({
          type: 'property',
          key: 'android.enableJetifier',
          value: 'true'
        });
      }

      if (!hasProperty('android.useAndroidX')) {
        gradleProperties.push({
          type: 'property',
          key: 'android.useAndroidX',
          value: 'true'
        });
      }

    } catch (error) {
      console.warn('Warning: Could not add 16KB support to gradle.properties:', error.message);
    }

    return config;
  });

  return config;
};

module.exports = with16KBSupport;
