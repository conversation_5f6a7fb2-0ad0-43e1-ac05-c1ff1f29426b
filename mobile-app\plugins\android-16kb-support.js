const { withAndroidManifest, withGradleProperties } = require('@expo/config-plugins');

const with16KBSupport = (config) => {
  // Add Android manifest modifications
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // Add 16KB page size support to application tag
    if (androidManifest.manifest && androidManifest.manifest.application) {
      const application = androidManifest.manifest.application[0];
      
      // Add meta-data for 16KB support
      if (!application['meta-data']) {
        application['meta-data'] = [];
      }
      
      // Add 16KB page size support meta-data
      application['meta-data'].push({
        $: {
          'android:name': 'android.max_aspect',
          'android:value': '2.4'
        }
      });
      
      // Add support for 16KB page sizes
      application['meta-data'].push({
        $: {
          'android:name': 'android.app.supports_16kb_page_size',
          'android:value': 'true'
        }
      });
    }
    
    return config;
  });

  // Add Gradle properties for 16KB support
  config = withGradleProperties(config, (config) => {
    const gradleProperties = config.modResults;
    
    // Add 16KB page size support properties
    gradleProperties.push({
      type: 'property',
      key: 'android.enableR8.fullMode',
      value: 'true'
    });
    
    gradleProperties.push({
      type: 'property',
      key: 'android.enableJetifier',
      value: 'true'
    });
    
    gradleProperties.push({
      type: 'property',
      key: 'android.useAndroidX',
      value: 'true'
    });
    
    // Enable 16KB page size support
    gradleProperties.push({
      type: 'property',
      key: 'android.supports16KBPageSize',
      value: 'true'
    });
    
    return config;
  });

  return config;
};

module.exports = with16KBSupport;
