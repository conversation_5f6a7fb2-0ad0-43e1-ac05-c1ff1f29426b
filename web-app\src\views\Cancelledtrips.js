import {
  Button,
  FormControl,
  FormLabel,
  Grid,
  MenuItem,
  Modal,
  Select,
  Typography,
} from "@mui/material";
import { ThemeProvider } from '@mui/material/styles';
import { makeStyles } from "@mui/styles";
import { api } from "common";
import MaterialTable from "material-table";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import theme from "styles/tableStyle";
import { BidModal, FONT_FAMILY, SECONDORY_COLOR, acceptBid, downloadCsv } from "../common/sharedFunctions";
import AlertDialog from "../components/AlertDialog";
import CircularLoading from "../components/CircularLoading";
import ConfirmationDialogRaw from "../components/ConfirmationDialogRaw";
import { colors } from "../components/Theme/WebTheme";
import UsersCombo from "../components/UsersCombo";

const useStyles = makeStyles((theme) => ({
  modal: {
    display: "flex",
    padding: theme.spacing(1),
    alignItems: "center",
    justifyContent: "center",
  },
  paper: {
    width: 680,
    backgroundColor: theme.palette.background.paper,
    border: `2px solid ${colors.BLACK}`,
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
  action: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
    justifyContent: "center",
    borderRadius: "20px",
    paddingLeft: "10px",
    paddingRight: "10px",
    width: "10rem",
    minHeight:"40px"
  },
}));

const icons = {
  paypal: require("../assets/payment-icons/paypal-logo.png").default,
  braintree: require("../assets/payment-icons/braintree-logo.png").default,
  stripe: require("../assets/payment-icons/stripe-logo.png").default,
  paytm: require("../assets/payment-icons/paytm-logo.png").default,
  payulatam: require("../assets/payment-icons/payulatam-logo.png").default,
  flutterwave: require("../assets/payment-icons/flutterwave-logo.png").default,
  paystack: require("../assets/payment-icons/paystack-logo.png").default,
  securepay: require("../assets/payment-icons/securepay-logo.png").default,
  payfast: require("../assets/payment-icons/payfast-logo.png").default,
  liqpay: require("../assets/payment-icons/liqpay-logo.png").default,
  culqi: require("../assets/payment-icons/culqi-logo.png").default,
  mercadopago: require("../assets/payment-icons/mercadopago-logo.png").default,
  squareup: require("../assets/payment-icons/squareup-logo.png").default,
  wipay: require("../assets/payment-icons/wipay-logo.png").default,
  test: require("../assets/payment-icons/test-logo.png").default,
  razorpay: require("../assets/payment-icons/razorpay-logo.png").default,
  paymongo:require("../assets/payment-icons/paymongo-logo.png").default,
  iyzico:require("../assets/payment-icons/iyzico-logo.png").default,
};

const Cancelledtrips = (props) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const { cancelBooking, updateBooking, RequestPushMsg, forceEndBooking } = api;
  const dispatch = useDispatch();
  const auth = useSelector((state) => state.auth);
  const navigate = useNavigate();
  const userdata = useSelector((state) => state.usersdata);
  const driverNameById = useMemo(() => {
    const map = {};
    if (userdata?.users) {
      userdata.users.forEach(u => {
        if (u?.usertype === 'driver') {
          const first = (u.firstName || '').toString().trim();
          map[u.id] = first || (u.name ? u.name.toString().trim().split(/\s+/)[0] : '');
        }
      });
    }
    return map;
  }, [userdata?.users]);
  const settings = useSelector((state) => state.settingsdata.settings);
  const role = useSelector((state) => state.auth.profile.usertype);
  const [paymentModalStatus, setPaymentModalStatus] = useState(false);
  const providers = useSelector((state) => state.paymentmethods.providers);
  const [selectedProvider, setSelectedProvider] = useState();
  const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
  const [data, setData] = useState([]);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState();
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const [users, setUsers] = useState(null);
  const [userCombo, setUserCombo] = useState(null);
  const rootRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [rowIndex, setRowIndex] = useState();
  const [bidModalStatus, setBidModalStatus] = useState(true);
  const [selectedBidder, setSelectedBidder] = useState();
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: "" });
  const classes = useStyles();
  const [walletModalStatus, setWalletModalStatus] = useState(false);

  useEffect(() => {
    if (bookinglistdata.bookings) {
      setData(bookinglistdata.bookings.filter(booking => booking.status === "CANCELLED"));
    } else {
      setData([]);
    }
  }, [bookinglistdata.bookings]);

  // You may want to define columns for cancelled trips, or reuse a shared one
  const columns = [
    // { title: t('booking_id'), field: 'id' },
    { title: t('booking_ref'), field: 'reference' },
    { title: t('Reason'), field: 'reason' },
    { title: t('AdminAssigned'), field: 'driver_name',
    render: rowData => {
      const ids = rowData?.requestedDrivers ? Object.keys(rowData.requestedDrivers) : [];
      if (!ids.length) {
        const name = rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : '';
        return <span>{name}</span>;
      }
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
          {ids.map(id => {
            const isAccepted = rowData?.driver && id === rowData.driver;
            const first = driverNameById?.[id]
              || (isAccepted && rowData?.driver_name ? rowData.driver_name.trim().split(/\s+/)[0] : (id || '').slice(0,6));
            return (
              <span
                key={id}
                style={{ color: isAccepted ? colors.GREEN : colors.BLACK, fontWeight: isAccepted ? 'bold' : 'normal' }}
              >
                {first},
              </span>
            );
          })}
        </div>
      );
    }
    },
    { title: t('DriverAccepted'), field: 'driver_name'},
    {
      title: t('booking_status_web'),
      field: 'status',
      render: rowData => (
        <div
          style={{
            backgroundColor:
              rowData.status === 'CANCELLED' ? colors.RED :
              rowData.status === 'COMPLETE' ? colors.GREEN :
              colors.YELLOW,
            color: 'white',
            padding: 7,
            borderRadius: '15px',
            fontWeight: 'bold',
            width: '150px',
            margin: 'auto',
            textAlign: 'center',
          }}
        >
          {t(rowData.status)}
        </div>
      )
    },
    { title: t('booking_date'), field: 'bookingDate', render: rowData => rowData.bookingDate ? new Date(rowData.bookingDate).toLocaleDateString(undefined, { day: '2-digit', month: 'long', year: 'numeric' }) : '' },
    { title: t('pickup_address'), field: 'pickupAddress' },
    { title: t('drop_address'), field: 'dropAddress' },
    // Add more columns as needed
  ];

  const [selectedRow, setSelectedRow] = useState(null);

  return bookinglistdata.loading ? (
    <CircularLoading />
  ) : (
    <div>
      <ThemeProvider theme={theme}>
        <MaterialTable
          title={t("Cancelled Trips")}
          style={{
            direction: isRTL === "rtl" ? "rtl" : "ltr",
            borderRadius: "8px",
            boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
          }}
          columns={columns}
          data={data}
          onRowClick={(evt, selectedRow) =>
            setSelectedRow(selectedRow.tableData.id)
          }
          options={{
            pageSize: 100,
            pageSizeOptions: [100, 500, { value: data.length, label: t("All") }],
            toolbarbuttonalignment: "right",
            exportButton: {
              csv: settings.AllowCriticalEditsAdmin,
              pdf: false,
            },
            maxColumnSort: "all_columns",
            rowStyle: (rowData) => ({
              backgroundColor:
                selectedRow === rowData.tableData.id ? colors.ROW_SELECTED :colors.WHITE
            }),
            maxBodyHeight: "calc(100vh - 199.27px)",
            headerStyle: {
              position: "sticky",
              top: "0px",
              backgroundColor: SECONDORY_COLOR,
              color: colors.Black,
              fontWeight: "bold ",
              textAlign: "center",
              zIndex: 1,
              border: `1px solid ${colors.TABLE_BORDER}`,
            },
            cellStyle: {
              border: `1px solid ${colors.TABLE_BORDER}`,
              textAlign: "center",
              margin: "auto",
            },
            actionsColumnIndex: -1,
          }}
          localization={{
            toolbar: {
              searchPlaceholder: t("search"),
              exportTitle: t("export"),
              exportCSVName: t("export"),
            },
            header: {
              actions: t("actions"),
            },
            pagination: {
              labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
              firstTooltip: t("first_page_tooltip"),
              previousTooltip: t("previous_page_tooltip"),
              nextTooltip: t("next_page_tooltip"),
              lastTooltip: t("last_page_tooltip"),
            },
          }}
          actions={[
            {
              icon: "info",
              tooltip: t("booking_details"),
              onClick: (event, rowData) => {
                navigate(`/cancelledtrips/cancelledtripsdetails/${rowData.id}`);
              },
            }
          ]}
        />
      </ThemeProvider>
    </div>
  );
};

export default Cancelledtrips; 