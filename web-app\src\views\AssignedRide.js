import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { makeStyles, ThemeProvider } from "@mui/styles";
import { api } from "common";
import MaterialTable from "material-table";
import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { bookingHistoryColumns, calendarBookingColumns, driverColumns, customerTableColumns, FONT_FAMILY, SECONDORY_COLOR } from "../common/sharedFunctions";
import AlertDialog from "../components/AlertDialog";
import CircularLoading from "../components/CircularLoading";
import { colors } from "../components/Theme/WebTheme";
import theme from "../styles/tableStyle";
// Add MUI radio imports
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormLabel from '@mui/material/FormLabel';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
// Add Tab imports
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { Grid, Modal } from "@mui/material";
import IconButton from '@mui/material/IconButton';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import moment from 'moment/min/moment-with-locales';
import { useNavigate, useLocation } from "react-router-dom";
import UsersCombo from "../components/UsersCombo";

const useStyles = makeStyles(() => ({
  root: {
    padding: 24,
    background: colors.WHITE,
    minHeight: '100vh',
    boxSizing: 'border-box',
    '@media (max-width: 900px)': {
      padding: 12,
    },
    '@media (max-width: 600px)': {
      padding: 4,
    },
  },
  tableContainer: {
    width: '100%',
    overflowX: 'auto',
    boxSizing: 'border-box',
    '@media (max-width: 600px)': {
      minWidth: 320,
    },
  },
  tabPanel: {
    padding: '20px 0',
  },
  calendarContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px', 
    borderRadius: '12px', 
    '@media (max-width: 991px)': {
      flexDirection: 'column',
    },
  },
  calendarContainer1: {
    display: 'flex',
    flexDirection: 'row',
    gap: '10px',
    alignItems: 'flex-start',
    boxSizing: 'border-box',
    '@media (max-width: 991px)': {
      flexDirection: 'column',
      gap: '10px', 
    },
  },
  calendarSection: {
    flex: '0 0 340px',
    minWidth: '300px',
    maxWidth: '340px',
    '@media (max-width: 991px)': {
      width: '100%',
      maxWidth: '100%',
      minWidth: 0,
      order: 1,
      margin: '0 auto',
    },
  },
  tableSection: {
    flex: 1,
    minWidth: 0, // allows table to shrink
    maxWidth: '100%',
    '@media (max-width: 991px)': {
      width: '100%',
      order: 2,
    },
  },
  calendarHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: '311px',
    marginBottom: '20px',
    backgroundColor: 'white',
    padding: '16px',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  calendarGrid: {
    width: '100%',
    maxWidth: '311px',
    backgroundColor: 'white',
    borderRadius: '8px',
    overflow: 'hidden',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    display: 'grid',
    gridTemplateColumns: 'repeat(7, 1fr)',
    gridTemplateRows: 'auto repeat(6, 1fr)',
  },
  calendarDay: {
    border: '1px solid #e0e0e0',
    padding: '12px 8px',
    textAlign: 'center',
    cursor: 'pointer',
    minHeight: '50px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    transition: 'all 0.2s ease',
    '&:hover': {
      backgroundColor: '#f0f8ff',
      transform: 'scale(1.05)',
    },
  },
  calendarDayHeader: {
    border: '1px solid #e0e0e0',
    padding: '12px 8px',
    textAlign: 'center',
    fontWeight: 'bold',
    backgroundColor: SECONDORY_COLOR,
    color: 'white',
    fontSize: '0.9rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarDaySelected: {
    backgroundColor: SECONDORY_COLOR,
    color: 'white',
    fontWeight: 'bold',
    '&:hover': {
      backgroundColor: SECONDORY_COLOR,
      transform: 'scale(1.05)',
    },
  },
  calendarDayToday: {
    backgroundColor: '#e3f2fd',
    fontWeight: 'bold',
    border: '2px solid #2196f3',
  },
  calendarDayOtherMonth: {
    color: '#ccc',
    backgroundColor: '#fafafa',
  },
  calendarEvents: {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  eventsHeader: {
    backgroundColor: 'white',
    padding: '20px',
    borderRadius: '8px 8px 0 0',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    borderBottom: '2px solid #e0e0e0',
  },
  eventsTable: {
    backgroundColor: 'white',
    borderRadius: '0 0 8px 8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    overflow: 'hidden',
    flex: '1',
  },
  eventCard: {
    margin: '8px 0',
    padding: '16px',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    backgroundColor: 'white',
    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
    transition: 'all 0.2s ease',
    '&:hover': {
      boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
      transform: 'translateY(-2px)',
    },
  },
  statusBadge: {
    display: 'inline-block',
    padding: '4px 8px',
    borderRadius: '12px',
    fontSize: '0.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  noEventsContainer: {
    textAlign: 'center',
    padding: '40px 20px',
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  },
  noEventsIcon: {
    fontSize: '48px',
    color: '#ccc',
    marginBottom: '16px',
  },
  modal: {
    display: "flex",
    padding: theme.spacing(1),
    alignItems: "center",
    justifyContent: "center",
  },
  paper: {
    width: 680,
    backgroundColor: theme.palette.background.paper,
    border: `2px solid ${colors.BLACK}`,
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
  title: {
    marginBottom: 20,
    fontFamily: FONT_FAMILY,
  },
  items: {
    marginBottom: 20,
  },
}));

// Tab Panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box >
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

// Custom Calendar Component
const CustomCalendar = ({ selectedDate, onDateSelect, events }) => {
  const classes = useStyles();
  const { t } = useTranslation();
  
  const [currentMonth, setCurrentMonth] = useState(moment(selectedDate).startOf('month'));
  
  const daysInMonth = currentMonth.daysInMonth();
  const firstDayOfMonth = currentMonth.startOf('month').day();
  const lastDayOfMonth = currentMonth.endOf('month').day();
  
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  const getDaysArray = () => {
    const days = [];
    
    // Previous month days
    const prevMonth = currentMonth.clone().subtract(1, 'month');
    const prevMonthDays = prevMonth.daysInMonth();
    for (let i = firstDayOfMonth - 1; i >= 0; i--) {
      days.push({
        day: prevMonthDays - i,
        month: 'prev',
        date: prevMonth.clone().date(prevMonthDays - i),
        hasEvents: false
      });
    }
    
    // Current month days
    for (let day = 1; day <= daysInMonth; day++) {
      const date = currentMonth.clone().date(day);
      const dateStr = date.format('YYYY-MM-DD');
      const hasEvents = events.some(event => {
        const eventDate = moment(event.tripdate).format('YYYY-MM-DD');
        return eventDate === dateStr;
      });
      
      days.push({
        day,
        month: 'current',
        date,
        hasEvents
      });
    }
    
    // Next month days
    const nextMonth = currentMonth.clone().add(1, 'month');
    for (let day = 1; day <= 6 - lastDayOfMonth; day++) {
      days.push({
        day,
        month: 'next',
        date: nextMonth.clone().date(day),
        hasEvents: false
      });
    }
    
    return days;
  };
  
  const handlePrevMonth = () => {
    setCurrentMonth(currentMonth.clone().subtract(1, 'month'));
  };
  
  const handleNextMonth = () => {
    setCurrentMonth(currentMonth.clone().add(1, 'month'));
  };
  
  const handleDateClick = (date) => {
    onDateSelect(date);
  };
  
  const days = getDaysArray();
  
  return (
    <div className={classes.calendarContainer}>
      <div className={classes.calendarHeader}>
        <IconButton onClick={handlePrevMonth}>
          <ChevronLeftIcon />
        </IconButton>
        <Typography variant="h6">
          {currentMonth.format('MMMM YYYY')}
        </Typography>
        <IconButton onClick={handleNextMonth}>
          <ChevronRightIcon />
        </IconButton>
      </div>
      
      <div className={classes.calendarGrid}>
        {/* Week day headers */}
        {weekDays.map(day => (
          <div key={day} className={classes.calendarDayHeader}>
            {t(day)}
          </div>
        ))}
        
        {/* Calendar days */}
        {days.map((dayObj, index) => {
          const isSelected = selectedDate.isSame(dayObj.date, 'day');
          const isToday = moment().isSame(dayObj.date, 'day');
          const isOtherMonth = dayObj.month !== 'current';
          
          return (
            <div 
              key={index}
              className={`${classes.calendarDay} ${
                isSelected ? classes.calendarDaySelected : ''
              } ${
                isToday ? classes.calendarDayToday : ''
              } ${
                isOtherMonth ? classes.calendarDayOtherMonth : ''
              }`}
              onClick={() => handleDateClick(dayObj.date)}
            >
              <div style={{ position: 'relative' }}>
                {dayObj.day}
                {dayObj.hasEvents && (
                  <div style={{
                    position: 'absolute',
                    top: '-2px',
                    right: '-2px',
                    width: '6px',
                    height: '6px',
                    backgroundColor: colors.RED,
                    borderRadius: '50%'
                  }} />
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const AssignedRide = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const classes = useStyles();
  const muiTheme = useTheme();
  const fullScreen = useMediaQuery(muiTheme.breakpoints.down('sm'));
  const role = useSelector((state) => state.auth.profile.usertype);
  const settings = useSelector((state) => state.settingsdata.settings);
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const userdata = useSelector((state) => state.usersdata);
  const driverNameById = useMemo(() => {
    const map = {};
    if (userdata?.users) {
      userdata.users.forEach(u => {
        if (u?.usertype === 'driver') {
          const first = (u.firstName || '').toString().trim();
          map[u.id] = first || (u.name ? u.name.toString().trim().split(/\s+/)[0] : '');
        }
      });
    }
    return map;
  }, [userdata?.users]);
  const auth = useSelector((state) => state.auth);
  const [data, setData] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const dispatch = useDispatch();
  const { updateBooking, fetchBookings, updateProfile, editUser, fetchUsersOnce, RequestPushMsg, cancelBooking } = api;
  const navigate = useNavigate();
  const location = useLocation();

  // Get tab from URL parameter
  const searchParams = new URLSearchParams(location.search);
  const urlTab = searchParams.get('tab');

  // Tab state - initialize from URL parameter if available
  const [tabValue, setTabValue] = useState(() => {
    if (urlTab && !isNaN(parseInt(urlTab)) && parseInt(urlTab) >= 0 && parseInt(urlTab) <= 3) {
      return parseInt(urlTab);
    }
    return 0;
  });
  const [selectedDate, setSelectedDate] = useState(moment());

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [putoutReason, setPutoutReason] = useState("");
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [loading, setLoading] = useState(false);
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: "" });

  // Add new state variables for driver assignment
  const [open, setOpen] = useState(false);
  const [rowIndex, setRowIndex] = useState();
  const [userCombo, setUserCombo] = useState(null);
  const [users, setUsers] = useState(null);
  const [openConfirm, setOpenConfirm] = useState(false);

  // Add new state for status filter
  const [statusFilter, setStatusFilter] = useState('All');

  useEffect(() => {
    if (bookinglistdata.bookings) {
      const filtered = bookinglistdata.bookings.filter((booking) => {
        if (!booking || !booking.status) return false;
        if (["ACCEPTED", "STARTED", "ARRIVED", "REACHED"].includes(booking.status)) return true;
        if (booking.status === "NEW") {
          const rd = booking.requestedDrivers;
          return rd && typeof rd === "object" && Object.values(rd).some(v => v === true);
        }
        return false; 
      });
      setData(filtered);
      console.log('AssignedRide data:', filtered);
    } else {
      setData([]);
      console.log('AssignedRide data: []');
    }
  }, [bookinglistdata.bookings]);

  // Add useEffect for users data (similar to Neworders)
  useEffect(() => {
    if (userdata.users) {
      let arr = [];
      for (let i = 0; i < userdata.users.length; i++) {
        let user = userdata.users[i];
        if (
          user.usertype === "driver" &&
          ((user.fleetadmin === auth.profile.uid && role === "fleetadmin") ||
            role === "admin")
        ) {
          arr.push({
            firstName: user.firstName,
            lastName: user.lastName,
            mobile: user.mobile,
            email: user.email,
            uid: user.id,
            desc:
              user.firstName +
              " " +
              user.lastName +
              " (" +
              (settings.AllowCriticalEditsAdmin
                ? user.mobile
                : t("hidden_demo")) +
              ") " +
              (settings.AllowCriticalEditsAdmin
                ? user.email
                : t("hidden_demo")),
            pushToken: user.pushToken ? user.pushToken : "",
            carType: user.carType,
          });
        }
      }
      setUsers(arr);
    }
  }, [
    userdata.users,
    settings.AllowCriticalEditsAdmin,
    auth.profile.uid,
    role,
    t,
  ]);

  // Handle tab change - update URL parameter
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    
    // Update URL parameter without page reload
    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set('tab', newValue.toString());
    navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
  };

  // Get driver list data with ride count
  const getDriverListData = () => {
    if (userdata.users) {
      const drivers = userdata.users.filter(user => 
        user.usertype === "driver" && 
        ((role === "fleetadmin" && user.fleetadmin === userdata.auth?.profile?.uid) || role === "admin")
      );
      
      // Add ride count to each driver (only ACCEPTED bookings)
      return drivers.map(driver => {
        const rideCount = bookinglistdata.bookings ? 
          bookinglistdata.bookings.filter(booking => 
            (booking.driver === driver.id || booking.driver === driver.uid) &&
            ["ACCEPTED", "ARRIVED", "STARTED"].includes(booking.status)
          ).length : 0;
        
        return {
          ...driver,
          rideCount
        };
      });
    }
    return [];
  };

  // Get customer list data with ride count
  const getCustomerListData = () => {
    if (userdata.users) {
      const customers = userdata.users.filter(user => 
        user.usertype === "customer" && 
        ((role === "fleetadmin" && user.fleetadmin === userdata.auth?.profile?.uid) || role === "admin")
      );
      
      // Add ride count to each customer (all bookings)
      return customers.map(customer => {
        const rideCount = bookinglistdata.bookings ? 
          bookinglistdata.bookings.filter(booking => 
            (booking.customer === customer.id || booking.customer === customer.uid)
          ).length : 0;
        
        return {
          ...customer,
          rideCount
        };
      });
    }
    return [];
  };

  // Handle rides click
  const handleRidesClick = (driver, mode = 'active') => {
    const driverKey = driver.id || driver.uid;
    const modeParam = mode === 'history' ? 'history' : 'active';
    navigate(`/driver-rides/${driverKey}?mode=${modeParam}`);
  };

  // Handle customer rides click
  const handleCustomerRidesClick = (customer, mode = 'active') => {
    const customerKey = customer.id || customer.uid;
    const modeParam = mode === 'history' ? 'history' : 'active';
    navigate(`/customer-rides/${customerKey}?mode=${modeParam}`);
  };

  // Get calendar events for selected date with status filter
  const getCalendarEvents = () => { 
    if (!bookinglistdata.bookings) return [];
    
    const selectedDateStr = selectedDate.format('YYYY-MM-DD');
    let filteredBookings = bookinglistdata.bookings.filter(booking => {
      if (!booking.tripdate) return false;
      const bookingDate = moment(booking.tripdate).format('YYYY-MM-DD');
      return bookingDate === selectedDateStr;
    });

    // Apply status filter
    if (statusFilter !== 'All') {
      filteredBookings = filteredBookings.filter(booking => booking.status === statusFilter);
    }

    return filteredBookings;
  };

  const columns = bookingHistoryColumns(role, settings, t, isRTL, driverNameById).map(col => {
    if (col.field === 'status' || col.field === 'booking_status_web') {
      return {
        ...col,
        cellStyle: rowData => ({
          backgroundColor: rowData.status === 'CANCELLED' ? colors.RED : undefined,
          color: rowData.status === 'CANCELLED' ? colors.WHITE : undefined,
          fontWeight: rowData.status === 'CANCELLED' ? 'bold' : undefined,
          borderRadius: rowData.status === 'CANCELLED' ? '10px' : undefined,
          textAlign: 'center',
          padding: 3,
        })
      };
    }
    return col;
  });

  // Use shared driverColumns function with rides click handler
  const driverTableColumns = driverColumns(t, settings, colors, handleRidesClick);

  // Use shared customerTableColumns function with rides click handler
  const customerTableColumnsData = customerTableColumns(t, settings, colors, handleCustomerRidesClick);

  // Add updateBookingStatus function
  const updateBookingStatus = () => {
    if (selectedBooking) {
      setLoading(true);
      // Find driver profile for correct deviceId
      let driverProfile;
      if (selectedBooking.driver && userdata.users) {
        driverProfile = userdata.users.find(
          u => String(u.uid) === String(selectedBooking.driver) || String(u.id) === String(selectedBooking.driver)
        );
      }

      // Compute a stable driver key
      const currentDriverKey = selectedBooking.driver || driverProfile?.uid || driverProfile?.id || null;

      // Helper to drop undefined fields (Firebase update rejects undefined)
      const clean = (obj) => Object.fromEntries(Object.entries(obj).filter(([, v]) => v !== undefined));

      // Save previous driver details in putoutdrivers (omit undefineds)
      const prevDriver = clean({
        driver: currentDriverKey || undefined,
        driverDeviceId: driverProfile?.deviceId || selectedBooking.driverDeviceId,
        driverRating: selectedBooking.driverRating,
        driver_contact: selectedBooking.driver_contact,
        driver_image: selectedBooking.driver_image,
        driver_name: selectedBooking.driver_name,
        driver_share: selectedBooking.driver_share,
        driver_token: selectedBooking.driver_token,
        putout_res: putoutReason
      });

      const previousPutouts = Array.isArray(selectedBooking.putoutdrivers) ? selectedBooking.putoutdrivers : [];

           // Mark requestedDrivers[currentDriver] = false; if not found, set all to false
           const updatedRequestedDrivers = (() => {
            const rd = (selectedBooking && typeof selectedBooking.requestedDrivers === 'object')
              ? { ...selectedBooking.requestedDrivers }
              : {};
            if (!rd) return {};
            if (currentDriverKey && Object.prototype.hasOwnProperty.call(rd, currentDriverKey)) {
              rd[currentDriverKey] = false;
            } else {
              Object.keys(rd).forEach(k => {
                rd[k] = false;
              });
            }
            return rd;
          })();

      // Create updatedBooking without driver fields
      const {
        driver,
        driverDeviceId,
        driverRating,
        driver_contact,
        driver_image,
        driver_name,
        driver_share,
        driver_token,
        ...rest
      } = selectedBooking;

      const updatedBooking = {
        ...rest,
        status: "NEW",
        putoutdrivers: [...previousPutouts, prevDriver],
        requestedDrivers: updatedRequestedDrivers
      };

      console.log('PUTOUT raised, updated booking before dispatch:', updatedBooking);
      dispatch(updateBooking(updatedBooking));

      // Set queue to false for the specific driver
      if (selectedBooking.driver && userdata.users) {
        if (driverProfile) {
          dispatch(editUser(driverProfile.uid || driverProfile.id, { ...driverProfile, queue: false }));
        } else {
          dispatch(editUser(selectedBooking.driver, { queue: false }));
        }
        dispatch(api.fetchUsersOnce());
      }

      dispatch(fetchBookings());
      setLoading(false);
      setCommonAlert({ open: true, msg: t("Booking status updated successfully.") });
      console.log('PUTOUT raised, updated booking:', updatedBooking);
    }
    setModalOpen(false);
    setPutoutReason("");
    setSelectedBooking(null);
  };
   
  // Modal handlers
  const handlePutoutClick = (rowData) => {
    setSelectedBooking(rowData);
    setPutoutReason("");
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
    setPutoutReason("");
    setSelectedBooking(null);
  };

  const handleModalConfirm = () => {
    updateBookingStatus();
  };

  // Update action handlers for calendar table
  const handleAssignClick = (rowData) => {
    setSelectedBooking(rowData);
    setOpen(true);
    setRowIndex(rowData.tableData?.id || 0);
  };

  const handleReassignClick = (rowData) => {
    setSelectedBooking(rowData);
    setOpen(true);
    setRowIndex(rowData.tableData?.id || 0);
  };

  const handleCancelClick = (rowData) => {
    setSelectedBooking(rowData);
    setOpenConfirm(true);
  };

  // Add assignDriver function (similar to Neworders)
  const assignDriver = () => {
    if (!userCombo) {
      setCommonAlert({ open: true, msg: t("please_select_driver") });
      return;
    }

    let booking = { ...selectedBooking };
    
    // Check if this is a reassignment (has existing driver)
    const isReassignment = booking.driver_name && booking.driver;
    
    if (isReassignment) {
      // For reassignment: Update driver information
      booking.driver = userCombo.uid;
      booking.driver_name = `${userCombo.firstName} ${userCombo.lastName}`;
      booking.driver_contact = userCombo.mobile;
      booking.driver_token = userCombo.pushToken;
      booking.carType = userCombo.carType;
      booking.status = "ACCEPTED";
    }

    // Add to requestedDrivers if it exists
    if (booking["requestedDrivers"]) {
      booking["requestedDrivers"][userCombo.uid] = true;
    } else {
      booking["requestedDrivers"] = {};
      booking["requestedDrivers"][userCombo.uid] = true;
    }

    dispatch(updateBooking(booking));

    // Send notification to driver
    if (userCombo.pushToken) {
      RequestPushMsg(userCombo.pushToken, {
        title: t("notification_title"),
        msg: t("new_booking_notification"),
        screen: "DriverTrips",
        channelId: settings.CarHornRepeat ? "bookings-repeat" : "bookings",
      });
    }

    setUserCombo(null);
    handleClose();
    setCommonAlert({ 
      open: true, 
      msg: isReassignment ? t("Driver reassigned successfully and notified.") : t("Driver assigned successfully and notified.") 
    });
  };

  // Add modal handlers
  const handleClose = () => {
    setOpen(false);
    setUserCombo(null);
  };

  // Add cancel booking function (similar to Neworders)
  const handleCancelBooking = () => {
    if (!selectedBooking) return;

    if (settings.AllowCriticalEditsAdmin && (role === "admin" || role === "fleetadmin")) {
      dispatch(
        cancelBooking({
          reason: t("cancelled_by_admin"),
          booking: selectedBooking,
          cancelledBy: role,
        })
      );
      setOpenConfirm(false);
      setSelectedBooking(null);
      setCommonAlert({ open: true, msg: t("Booking cancelled successfully.") });
    } else {
      setCommonAlert({ open: true, msg: t("demo_mode") });
    }
  };

  // Handle status filter change
  const handleStatusFilterChange = (event, newValue) => {
    setStatusFilter(newValue);
  };

  return (
    <div className={classes.root}>
      <ThemeProvider theme={theme}>
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="assigned ride tabs"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab label={t("ASSIGNED RIDE")} {...a11yProps(0)} />
              {role === 'admin' && (<Tab label={t("DRIVER LIST")} {...a11yProps(1)} />)}
              {role === 'admin' && (<Tab label={t("CUSTOMER LIST")} {...a11yProps(2)} />)}
              {role === 'admin' && (<Tab label={t("RIDE CALENDAR")} {...a11yProps(3)} />)}
            </Tabs>
          </Box>

          {/* Assigned Ride Tab */}
          <TabPanel value={tabValue} index={0} className={classes.tabPanel}>
        <Box className={classes.tableContainer}>
          <MaterialTable
            title={t("ASSIGNED RIDE")}
            style={{
              direction: isRTL === "rtl" ? "rtl" : "ltr",
              borderRadius: "8px",
              boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
              minWidth: 320,
            }}
            columns={columns}
            data={data}
            onRowClick={(evt, selectedRow) => setSelectedRow(selectedRow.tableData.id)}
            actions={[
              rowData => {
                // Always show PUTOUT for admin
                if (role === 'admin') {
                  return {
                    icon: () => (
                      <button
                        style={{
                          backgroundColor: colors.RED,
                          color: '#fff',
                          border: 'none',
                          borderRadius: '5px',
                          padding: '6px 16px',
                          fontWeight: 'bold',
                          cursor: 'pointer',
                          fontSize: '0.95rem',
                        }}
                      >
                        PUTOUT
                      </button>
                    ),
                    tooltip: 'PUTOUT',
                    onClick: (event, row) => {
                      handlePutoutClick(row);
                    },
                  };
                }
                // For driver, show only one button based on diffHours
                if (role === 'driver') {
                  if (!rowData.tripdate) return null;
                  const now = new Date();
                  const tripDate = new Date(rowData.tripdate);
                  const diffMs = Math.abs(tripDate - now);
                  const diffHours = diffMs / (1000 * 60 * 60);
                  if (diffHours <= 2) {
                    return {
                      icon: () => (
                        <button
                          style={{
                            backgroundColor: "blue",
                            color: '#fff',
                            border: 'none',
                            borderRadius: '5px',
                            padding: '6px 5px',
                            fontWeight: 'bold',
                            cursor: 'pointer',
                            fontSize: '0.95rem',
                          }}
                        >
                          Contact Dispatcher
                        </button>
                      ),
                      tooltip: 'Contact Dispatcher',
                      onClick: (event, row) => {
                        window.open('tel:12345678');
                      },
                    };
                  } else {
                    return {
                      icon: () => (
                        <button
                          style={{
                            backgroundColor: colors.RED,
                            color: '#fff',
                            border: 'none',
                            borderRadius: '5px',
                            padding: '6px 16px',
                            fontWeight: 'bold',
                            cursor: 'pointer',
                            fontSize: '0.95rem',
                          }}
                        >
                          PUTOUT
                        </button>
                      ),
                      tooltip: 'PUTOUT',
                      onClick: (event, row) => {
                        handlePutoutClick(row);
                      },
                    };
                  }
                }
                // Default: no actions
                return null;
              }
            ]}
            options={{
              pageSize: 100,
              pageSizeOptions: [100, 500, { value: data.length, label: t("All") }],
              toolbarbuttonalignment: "right",
              exportButton: true,
              maxColumnSort: "all_columns",
              rowStyle: rowData => ({
                backgroundColor: selectedRow === rowData.tableData.id ? colors.ROW_SELECTED : colors.WHITE
              }),
              maxBodyHeight: "calc(100vh - 199.27px)",
              headerStyle: {
                position: "sticky",
                top: "0px",
                backgroundColor: SECONDORY_COLOR,
                color: colors.Black,
                fontWeight: "bold ",
                textAlign: "center",
                zIndex: 1,
                border: `1px solid ${colors.TABLE_BORDER}`,
              },
              cellStyle: {
                border: `1px solid ${colors.TABLE_BORDER}`,
                textAlign: "center",
                margin: "auto",
                minWidth: 100,
                fontSize: '0.95rem',
                padding: 4,
              },
              actionsColumnIndex: -1,
            }}
            localization={{
              toolbar: {
                searchPlaceholder: t("search"),
                exportTitle: t("export"),
                exportCSVName: t("export"),
              },
              header: {
                actions: t("actions"),
              },
              pagination: {
                labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                firstTooltip: t("first_page_tooltip"),
                previousTooltip: t("previous_page_tooltip"),
                nextTooltip: t("next_page_tooltip"),
                lastTooltip: t("last_page_tooltip"),
              },
              body: {
                emptyDataSourceMessage: t("No records to display"),
              },
            }}
          />
        </Box>
          </TabPanel> 
          
          
          {/* Driver List Tab */}
          {role === 'admin' && (
          <TabPanel value={tabValue} index={1} className={classes.tabPanel}>
            <Box className={classes.tableContainer}>
              <MaterialTable
                title={t("DRIVER LIST")}
                style={{
                  direction: isRTL === "rtl" ? "rtl" : "ltr",
                  borderRadius: "8px",
                  boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
                  minWidth: 320,
                }}
                columns={driverTableColumns}
                data={getDriverListData()}
                options={{
                  pageSize: 10,
                  pageSizeOptions: [10, 25, 50],
                  toolbarbuttonalignment: "right",
                  exportButton: true,
                  maxColumnSort: "all_columns",
                  maxBodyHeight: "calc(100vh - 199.27px)",
                  headerStyle: {
                    position: "sticky",
                    top: "0px",
                    backgroundColor: SECONDORY_COLOR,
                    color: colors.Black,
                    fontWeight: "bold ",
                    textAlign: "center",
                    zIndex: 1,
                    border: `1px solid ${colors.TABLE_BORDER}`,
                  },
                  cellStyle: {
                    border: `1px solid ${colors.TABLE_BORDER}`,
                    textAlign: "center",
                    margin: "auto",
                    minWidth: 100,
                    fontSize: '0.95rem',
                    padding: 4,
                  },
                }}
                localization={{
                  toolbar: {
                    searchPlaceholder: t("search"),
                    exportTitle: t("export"),
                    exportCSVName: t("export"),
                  },
                  header: {
                    actions: t("actions"),
                  },
                  pagination: {
                    labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                    firstTooltip: t("first_page_tooltip"),
                    previousTooltip: t("previous_page_tooltip"),
                    nextTooltip: t("next_page_tooltip"),
                    lastTooltip: t("last_page_tooltip"),
                  },
                  body: {
                    emptyDataSourceMessage: t("No drivers to display"),
                  },
                }}
              />
            </Box>
          </TabPanel>
          )}

          {/* Customer List Tab */}
          {role === 'admin' && (
          <TabPanel value={tabValue} index={2} className={classes.tabPanel}>
            <Box className={classes.tableContainer}>
              <MaterialTable
                title={t("CUSTOMER LIST")}
                style={{
                  direction: isRTL === "rtl" ? "rtl" : "ltr",
                  borderRadius: "8px",
                  boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
                  minWidth: 320,
                }}
                columns={customerTableColumnsData}
                data={getCustomerListData()}
                options={{
                  pageSize: 10,
                  pageSizeOptions: [10, 25, 50],
                  toolbarbuttonalignment: "right",
                  exportButton: true,
                  maxColumnSort: "all_columns",
                  maxBodyHeight: "calc(100vh - 199.27px)",
                  headerStyle: {
                    position: "sticky",
                    top: "0px",
                    backgroundColor: SECONDORY_COLOR,
                    color: colors.Black,
                    fontWeight: "bold ",
                    textAlign: "center",
                    zIndex: 1,
                    border: `1px solid ${colors.TABLE_BORDER}`,
                  },
                  cellStyle: {
                    border: `1px solid ${colors.TABLE_BORDER}`,
                    textAlign: "center",
                    margin: "auto",
                    minWidth: 100,
                    fontSize: '0.95rem',
                    padding: 4,
                  },
                }}
                localization={{
                  toolbar: {
                    searchPlaceholder: t("search"),
                    exportTitle: t("export"),
                    exportCSVName: t("export"),
                  },
                  header: {
                    actions: t("actions"),
                  },
                  pagination: {
                    labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                    firstTooltip: t("first_page_tooltip"),
                    previousTooltip: t("previous_page_tooltip"),
                    nextTooltip: t("next_page_tooltip"),
                    lastTooltip: t("last_page_tooltip"),
                  },
                  body: {
                    emptyDataSourceMessage: t("No customers to display"),
                  },
                }}
              />
            </Box>
          </TabPanel>
          )}

          {/* Ride Calendar Tab */}
          {role === 'admin' && (
          <TabPanel value={tabValue} index={3} className={classes.tabPanel}>
            <div className={classes.calendarContainer1}>
              {/* Table Section - Left side on desktop, bottom on mobile */}
              <div className={classes.tableSection}>
                <div className={classes.calendarEvents}>
                  <div className={classes.eventsHeader}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                      <Typography variant="h5" style={{ fontWeight: 'bold', color: SECONDORY_COLOR }}>
                        {t("Rides for")} {selectedDate.format('MMMM D, YYYY')}
                      </Typography>
                      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                        <Button
                          variant={statusFilter === 'All' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'All')}
                          style={{
                            backgroundColor: statusFilter === 'All' ? SECONDORY_COLOR : 'transparent',
                            color: statusFilter === 'All' ? 'white' : SECONDORY_COLOR,
                            borderColor: SECONDORY_COLOR,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("All")}
                        </Button>
                        <Button
                          variant={statusFilter === 'NEW' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'NEW')}
                          style={{
                            backgroundColor: statusFilter === 'NEW' ? colors.YELLOW : 'transparent',
                            color: statusFilter === 'NEW' ? 'black' : colors.YELLOW,
                            borderColor: colors.YELLOW,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("NEW")}
                        </Button>
                        <Button
                          variant={statusFilter === 'ACCEPTED' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'ACCEPTED')}
                          style={{
                            backgroundColor: statusFilter === 'ACCEPTED' ? colors.GREEN : 'transparent',
                            color: statusFilter === 'ACCEPTED' ? 'white' : colors.GREEN,
                            borderColor: colors.GREEN,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("ACCEPT")}
                        </Button>
                        <Button
                          variant={statusFilter === 'ARRIVED' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'ARRIVED')}
                          style={{
                            backgroundColor: statusFilter === 'ARRIVED' ? colors.BLUE : 'transparent',
                            color: statusFilter === 'ARRIVED' ? 'white' : colors.BLUE,
                            borderColor: colors.BLUE,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("ARRIVED")}
                        </Button>
                        <Button
                          variant={statusFilter === 'STARTED' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'STARTED')}
                          style={{
                            backgroundColor: statusFilter === 'STARTED' ? colors.TAXIPRIMARY : 'transparent',
                            color: statusFilter === 'STARTED' ? 'white' : colors.TAXIPRIMARY,
                            borderColor: colors.TAXIPRIMARY,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("START")}
                        </Button>
                        <Button
                          variant={statusFilter === 'Pending' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'Pending')}
                          style={{
                            backgroundColor: statusFilter === 'Pending' ? colors.ORANGE : 'transparent',
                            color: statusFilter === 'Pending' ? 'white' : colors.ORANGE,
                            borderColor: colors.ORANGE,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("PENDING")}
                        </Button>
                        <Button
                          variant={statusFilter === 'COMPLETE' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'COMPLETE')}
                          style={{
                            backgroundColor: statusFilter === 'COMPLETE' ? colors.BLUE : 'transparent',
                            color: statusFilter === 'COMPLETE' ? 'white' : colors.BLUE,
                            borderColor: colors.BLUE,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("COMPLETE")}
                        </Button>
                        <Button
                          variant={statusFilter === 'CANCELLED' ? 'contained' : 'outlined'}
                          size="small"
                          onClick={() => handleStatusFilterChange(null, 'CANCELLED')}
                          style={{
                            backgroundColor: statusFilter === 'CANCELLED' ? colors.RED : 'transparent',
                            color: statusFilter === 'CANCELLED' ? 'white' : colors.RED,
                            borderColor: colors.RED,
                            fontSize: '0.75rem',
                            padding: '4px 8px',
                            minWidth: '60px',
                          }}
                        >
                          {t("CANCELLED")}
                        </Button>
                      </div>
                    </div>
                    <Typography variant="body2" color="textSecondary" style={{ marginTop: '4px' }}>
                      {getCalendarEvents().length} {t("booking(s) found")} {statusFilter !== 'All' && `(${t("filtered by")} ${t(statusFilter)})`}
                    </Typography>
                  </div>
                  
                  {getCalendarEvents().length > 0 ? (
                    <div className={classes.eventsTable}>
                      <MaterialTable
                        title=""
                        style={{
                          direction: isRTL === "rtl" ? "rtl" : "ltr",
                          borderRadius: "0",
                          boxShadow: "none",
                        }}
                        columns={calendarBookingColumns(t, settings, colors, handleAssignClick, handleReassignClick, handleCancelClick, role, driverNameById )}
                        data={getCalendarEvents()}
                        options={{
                          pageSize: 100,
                          pageSizeOptions: [100, 500, { value: data.length, label: t("All") }],
                          toolbarbuttonalignment: "right",
                          exportButton: true,
                          maxColumnSort: "all_columns",
                          maxBodyHeight: "400px",
                          headerStyle: {
                            backgroundColor: SECONDORY_COLOR,
                            color: colors.BLACK,
                            fontWeight: "bold",
                            textAlign: "center",
                            border: `1px solid ${colors.TABLE_BORDER}`,
                          },
                          cellStyle: {
                            border: `1px solid ${colors.TABLE_BORDER}`,
                            textAlign: "center",
                            padding: "12px 8px",
                          },
                          rowStyle: {
                            '&:hover': {
                              backgroundColor: '#f5f5f5',
                            },
                          },
                        }}
                        localization={{
                          toolbar: {
                            searchPlaceholder: t("search_bookings"),
                            exportTitle: t("export"),
                            exportCSVName: t("export_bookings"),
                          },
                          header: {
                            actions: t("actions"),
                          },
                          pagination: {
                            labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                            firstTooltip: t("first_page_tooltip"),
                            previousTooltip: t("previous_page_tooltip"),
                            nextTooltip: t("next_page_tooltip"),
                            lastTooltip: t("last_page_tooltip"),
                          },
                          body: {
                            emptyDataSourceMessage: t("No bookings for this date"),
                          },
                        }}
                      />
                    </div>
                  ) : (
                    <div className={classes.noEventsContainer}>
                      <div className={classes.noEventsIcon}>📅</div>
                      <Typography variant="h6" color="textSecondary" gutterBottom>
                        {t("No rides scheduled")}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t("No rides scheduled for this date")}
                      </Typography>
                    </div>
                  )}
                </div>
              </div>

              {/* Calendar Section - Right side on desktop, top on mobile */}
              <div className={classes.calendarSection}>
                <CustomCalendar 
                  selectedDate={selectedDate}
                  onDateSelect={setSelectedDate}
                  events={bookinglistdata.bookings || []}
                />
              </div>
            </div>
          </TabPanel>
          )}
        </Box>

        {/* PUTOUT Modal */}
        <Dialog open={modalOpen} onClose={handleModalClose} maxWidth="sm" fullWidth fullScreen={fullScreen}>
          <DialogContent>
            <FormControl component="fieldset" fullWidth>
              <FormLabel component="legend">{t('PUTOUT Reason')}</FormLabel>
              <RadioGroup
                aria-label="putout-reason"
                name="putout-reason"
                value={putoutReason}
                onChange={e => setPutoutReason(e.target.value)}
              >
                <FormControlLabel value="Not Available" control={<Radio />} label={t('Not Available')} />
                <FormControlLabel value="Car issue" control={<Radio />} label={t('Car issue')} />
                <FormControlLabel value="Personal reason" control={<Radio />} label={t('Personal reason')} />
                <FormControlLabel value="Other" control={<Radio />} label={t('Other')} />
              </RadioGroup>
            </FormControl>
          </DialogContent>
          <DialogActions style={{ padding: fullScreen ? 8 : 16 }}>
            <Button onClick={handleModalClose} color="secondary" variant="outlined" fullWidth={fullScreen}>
              {t('Cancel')}
            </Button>
            <Button onClick={handleModalConfirm} color="primary" variant="contained" disabled={!putoutReason.trim()} fullWidth={fullScreen}>
              {t('Confirm')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Driver Assignment Modal */}
        {users && selectedBooking && open ? (
          <Modal
            disablePortal
            disableEnforceFocus
            disableAutoFocus
            onClose={handleClose}
            open={open}
            className={classes.modal}
          >
            <div className={classes.paper}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography
                    component="h1"
                    variant="h5"
                    className={classes.title}
                    style={{ textAlign: isRTL === "rtl" ? "right" : "left", fontFamily: FONT_FAMILY}}
                  >
                    {selectedBooking && selectedBooking.driver_name ? t("reassign_driver") : t("select_driver")}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <UsersCombo
                    className={classes.items}
                    placeholder={t("select_user")}
                    users={users.filter(
                      (usr) => {
                        // For Sedan, show all drivers except current driver and already requested drivers
                        if (
                          selectedBooking.carType &&
                          selectedBooking.carType.toLowerCase() === 'sedan'
                        ) {
                          const driverUid = usr.uid || usr.id;
                          const isAlreadyRequested =
                            selectedBooking.requestedDrivers &&
                            selectedBooking.requestedDrivers[driverUid] === true;
                          return driverUid !== selectedBooking.driver && !isAlreadyRequested;
                        }
                        // For other car types, filter by matching car type and exclude current driver
                        return (
                          usr.carType &&
                          selectedBooking.carType &&
                          usr.carType.toLowerCase() === selectedBooking.carType.toLowerCase() &&
                          usr.uid !== selectedBooking.driver
                        );
                      }
                    )}
                    value={userCombo}
                    onChange={(event, newValue) => {
                      setUserCombo(newValue);
                    }}
                  />
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={12}
                  md={12}
                  lg={12}
                  style={{
                    direction: isRTL === "rtl" ? "rtl" : "ltr",
                    marginLeft: isRTL === "rtl" ? "65%" : 0,
                  }}
                >
                  <Button
                    onClick={handleClose}
                    variant="contained"
                    style={{ backgroundColor: colors.RED, fontFamily: FONT_FAMILY }}
                  >
                    {t("cancel")}
                  </Button>
                  <Button
                    onClick={assignDriver}
                    variant="contained"
                    color="primary"
                    style={{
                      ...(isRTL === "rtl" ? { marginRight: 10 } : { marginLeft: 10 }),
                      backgroundColor: colors.GREEN,
                      fontFamily: FONT_FAMILY,
                    }}
                  >
                    {t("assign")}
                  </Button>
                </Grid>
              </Grid>
            </div>
          </Modal>
        ) : null}

        {/* PUTOUT Confirmation Modal */}
        <Dialog open={openConfirm} onClose={() => setOpenConfirm(false)} maxWidth="sm" fullWidth fullScreen={fullScreen}>
          <DialogContent>
            <Typography variant="h6" gutterBottom>
              {t('Confirm Cancellation')}
            </Typography>
            <Typography variant="body1">
              {t('Are you sure you want to cancel this booking? This action cannot be undone.')}
            </Typography>
          </DialogContent>
          <DialogActions style={{ padding: fullScreen ? 8 : 16 }}>
            <Button onClick={() => setOpenConfirm(false)} color="secondary" variant="outlined" fullWidth={fullScreen}>
              {t('No, Keep It')}
            </Button>
            <Button onClick={handleCancelBooking} color="primary" variant="contained" fullWidth={fullScreen}>
              {t('Yes, Cancel It')}
            </Button>
          </DialogActions>
        </Dialog>

        <AlertDialog open={commonAlert.open} onClose={() => setCommonAlert({ open: false, msg: "" })}>
          {commonAlert.msg}
        </AlertDialog>
        {loading && <CircularLoading />}
      </ThemeProvider>
    </div>
  );
};

export default AssignedRide; 