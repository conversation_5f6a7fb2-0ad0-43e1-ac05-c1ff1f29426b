import React, { useState } from 'react';
import { Button, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

export default function AddPassengersPage({ navigation, route }) {
  const [addOther, setAddOther] = useState(false);
  const [otherName, setOtherName] = useState('');
  const [otherPhone, setOtherPhone] = useState('');

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Add Other Passengers</Text>
      <TouchableOpacity
        style={styles.checkboxRow}
        onPress={() => setAddOther(!addOther)}
      >
        <View style={[styles.checkbox, addOther && styles.checked]} />
        <Text style={styles.label}>Add for other person</Text>
      </TouchableOpacity>
      {addOther && (
        <>
          <TextInput
            style={styles.input}
            placeholder="Other Person's Name"
            value={otherName}
            onChangeText={setOtherName}
          />
          <TextInput
            style={styles.input}
            placeholder="Other Person's Phone Number"
            value={otherPhone}
            onChangeText={setOtherPhone}
            keyboardType="phone-pad"
          />
        </>
      )}
      <Button
        title="Next"
        onPress={() => navigation.navigate('FinalBookingDetails', {
          ...route.params,
          otherName: addOther ? otherName : null,
          otherPhone: addOther ? otherPhone : null,
        })}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, backgroundColor: '#fff' },
  title: { fontSize: 22, fontWeight: 'bold', marginBottom: 20 },
  checkboxRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 15 },
  checkbox: {
    width: 22, height: 22, borderWidth: 2, borderColor: '#007AFF',
    marginRight: 10, borderRadius: 4, backgroundColor: '#fff'
  },
  checked: { backgroundColor: '#007AFF' },
  label: { fontSize: 16 },
  input: { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 10, marginBottom: 15 },
});
