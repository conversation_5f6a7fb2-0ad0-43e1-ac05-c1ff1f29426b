#!/usr/bin/env node

/**
 * Build script to ensure 16KB memory page size support
 * This script validates and prepares the app for 16KB page size compatibility
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Preparing build for 16KB memory page size support...');

// Check if required configurations are in place
function validateConfig() {
  console.log('✅ Validating 16KB support configuration...');
  
  // Check app.config.js
  const appConfigPath = path.join(__dirname, '../app.config.js');
  if (!fs.existsSync(appConfigPath)) {
    console.error('❌ app.config.js not found');
    process.exit(1);
  }
  
  const appConfig = require(appConfigPath);
  
  // Validate expo-build-properties configuration
  const buildPropertiesPlugin = appConfig.default.plugins.find(plugin => 
    Array.isArray(plugin) && plugin[0] === 'expo-build-properties'
  );
  
  if (!buildPropertiesPlugin) {
    console.error('❌ expo-build-properties plugin not found');
    process.exit(1);
  }
  
  const androidConfig = buildPropertiesPlugin[1].android;
  if (!androidConfig || androidConfig.targetSdkVersion < 35) {
    console.error('❌ Android targetSdkVersion must be 35 or higher for 16KB support');
    process.exit(1);
  }
  
  console.log('✅ Configuration validation passed');
}

// Check if 16KB support plugin exists
function validate16KBPlugin() {
  const pluginPath = path.join(__dirname, '../plugins/android-16kb-support.js');
  if (!fs.existsSync(pluginPath)) {
    console.error('❌ 16KB support plugin not found');
    process.exit(1);
  }
  console.log('✅ 16KB support plugin found');
}

// Validate EAS configuration
function validateEASConfig() {
  const easConfigPath = path.join(__dirname, '../eas.json');
  if (!fs.existsSync(easConfigPath)) {
    console.error('❌ eas.json not found');
    process.exit(1);
  }
  
  const easConfig = JSON.parse(fs.readFileSync(easConfigPath, 'utf8'));
  
  if (!easConfig.build.production.android.buildType === 'app-bundle') {
    console.error('❌ Production build must use app-bundle for 16KB support');
    process.exit(1);
  }
  
  console.log('✅ EAS configuration validated');
}

// Main validation function
function main() {
  try {
    validateConfig();
    validate16KBPlugin();
    validateEASConfig();
    
    console.log('🎉 All 16KB support configurations are valid!');
    console.log('📱 Your app is ready for Android 15+ with 16KB memory page size support');
    console.log('🚀 You can now build with: eas build --platform android --profile production');
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

main();
