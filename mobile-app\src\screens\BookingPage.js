import React, { useState } from 'react';
import { Button, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function BookingPage({ navigation, route }) {
  const [paymentMethod, setPaymentMethod] = useState('wallet');
  // You can get form data from route.params if needed

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Booking Summary</Text>
      <View style={styles.row}>
        <Text style={styles.label}>Total Fare</Text>
        <Text style={styles.value}>$100</Text>
      </View>
      <Text style={styles.label}>Payment Method</Text>
      <View style={styles.radioGroup}>
        <TouchableOpacity
          style={styles.radioButton}
          onPress={() => setPaymentMethod('wallet')}
        >
          <View style={[styles.radioCircle, paymentMethod === 'wallet' && styles.selectedRadio]} />
          <Text style={styles.radioLabel}>Wallet</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.radioButton}
          onPress={() => setPaymentMethod('cash')}
        >
          <View style={[styles.radioCircle, paymentMethod === 'cash' && styles.selectedRadio]} />
          <Text style={styles.radioLabel}>Cash</Text>
        </TouchableOpacity>
      </View>
      <Button title="Next" onPress={() => navigation.navigate('AddPassengersPage', { ...route.params, paymentMethod })} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, backgroundColor: '#fff' },
  title: { fontSize: 22, fontWeight: 'bold', marginBottom: 20 },
  row: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 20 },
  label: { fontSize: 16, fontWeight: 'bold', marginBottom: 10 },
  value: { fontSize: 16, marginBottom: 10 },
  radioGroup: { flexDirection: 'row', marginBottom: 20 },
  radioButton: { flexDirection: 'row', alignItems: 'center', marginRight: 20 },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  selectedRadio: {
    backgroundColor: '#007AFF',
  },
  radioLabel: { fontSize: 16 },
});
