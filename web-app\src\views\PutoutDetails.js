import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import { Avatar, Box, Button, Card, Grid, Typography } from "@mui/material";
import { makeStyles } from "@mui/styles";
import moment from "moment/min/moment-with-locales";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { FONT_FAMILY, MAIN_COLOR, SECONDORY_COLOR } from "../common/sharedFunctions";
import { colors } from "../components/Theme/WebTheme";

const useStyles = makeStyles((theme) => ({
  card: {
    borderRadius: "10px",
    backgroundColor: colors.WHITE,
    minHeight: 100,
    marginTop: 5,
    marginBottom: 20,
    boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
  },
  txt: {
    padding: 10,
    fontWeight: "bold",
    minHeight: 60,
    backgroundColor: SECONDORY_COLOR,
    color: colors.BLACK,
    boxShadow: 3,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
}));

function PutoutDetails() {
  const { id } = useParams();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const navigate = useNavigate();
  const [data, setData] = useState(null);
  const settings = useSelector((state) => state.settingsdata.settings);
  const classes = useStyles();
  const role = useSelector(state => state.auth.profile.usertype);

  useEffect(() => {
    if (bookinglistdata.bookings) {
      const booking = bookinglistdata.bookings.find(
        (item) => item.id === id.toString()
      );
      if (booking) {
        setData(booking);
      } else {
        navigate("/404");
        setData(null);
      }
    }
  }, [bookinglistdata.bookings, id, navigate]);

  const putoutDriver = data && Array.isArray(data.putoutdrivers)
    ? data.putoutdrivers.find(d => d.putout_res)
    : null;

  const renderGridItem = (item, isRTL) => (
    <Grid
      key={item.key}
      container
      spacing={1}
      sx={{ direction: isRTL === "rtl" ? "rtl" : "ltr" }}
      style={{
        justifyContent: "center",
        alignItems: "center",
        minHeight: 60,
        ...item.addressGridStyle
      }}
    >
      <Grid item xs={4}>
        <Typography
          style={{
            fontSize: 16,
            padding: 2,
            textAlign: isRTL === "rtl" ? "right" : "left",
            fontFamily: FONT_FAMILY,
            ...item.typographyStyleAddress
          }}
        >
          {item.label}
        </Typography>
      </Grid>
      <Grid item xs={4}>
        <Typography
          style={{
            fontSize: 18,
            padding: 2,
            letterSpacing: -1,
            textAlign: "center",
            fontFamily: FONT_FAMILY,
          }}
        >
          -----
        </Typography>
      </Grid>
      <Grid item xs={4}>
        <Typography
          style={{
            fontSize: 16,
            padding: 2,
            textAlign: isRTL === "rtl" ? "left" : "right",
            wordBreak: 'break-word',
            fontFamily: FONT_FAMILY,
            ...item.style,
          }}
        >
          {item.value}
        </Typography>
      </Grid>
    </Grid>
  );

  const renderTypography = (text) => (
    <Typography
      className={classes.txt}
      style={{
        borderBottomRightRadius: isRTL ? "90px" : "",
        borderBottomLeftRadius: isRTL ? "" : "90px",
        fontFamily: FONT_FAMILY,
      }}
      variant="h5"
    >
      {text}
    </Typography>
  );

  if (!data) return null;

  return (
    <>
      <div dir={isRTL === "rtl" ? "rtl" : "ltr"} style={{ marginBottom: 20 }}>
        <Button
          variant="text"
          onClick={() => {
            navigate("/bookings?tab=putout");
          }}
        >
          <Typography
            style={{
              margin: "10px 10px 0 5px",
              textAlign: isRTL === "rtl" ? "right" : "left",
              fontWeight: "bold",
              color: MAIN_COLOR,
              fontFamily: FONT_FAMILY,
            }}
          >
            {`<<- ${t("go_back")}`}
          </Typography>
        </Button>
      </div>
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("ride_information"))}
                <Grid container direction="column" style={{ padding: 10 }}>
                  {[
                    { key: "reference", label: t("Reference"), value: data.reference },
                    { key: "status", label: t("Status"), value: t(data.status) },
                    { key: "pickupAddress", label: t("Pickup Address"), value: data.pickupAddress },
                    { key: "dropAddress", label: t("Drop Address"), value: data.dropAddress },
                    { key: "carType", label: t("Car Type"), value: data.carType },
                    { key: "trip_cost", label: t("Trip Cost"), value: data.trip_cost },
                    { key: "putout_res", label: t("Putout Details"), value: putoutDriver ? (
                      <span style={{
                        backgroundColor: colors.YELLOW,
                        color: colors.BLACK,
                        padding: '6px 14px',
                        borderRadius: '12px',
                        fontWeight: 'bold',
                        display: 'inline-block',
                        fontFamily: FONT_FAMILY,
                      }}>{putoutDriver.putout_res}</span>
                    ) : "" },
                  ].map((item) => item.value ? renderGridItem(item, isRTL) : null)}
                </Grid>
              </Card>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("payment_info"))}
                <Grid container direction="column" style={{ paddingRight: 15, paddingLeft: 15, paddingBottom: 15 }}>
                  {[
                    { key: "payment_mode", label: t("Payment Mode"), value: data.payment_mode },
                    { key: "cardPaymentAmount", label: t("Card Payment Amount"), value: data.cardPaymentAmount },
                    { key: "cashPaymentAmount", label: t("Cash Payment Amount"), value: data.cashPaymentAmount },
                  ].map((item) => item.value ? renderGridItem(item, isRTL) : null)}
                </Grid>
              </Card>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("driver_info"))}
                <Grid container direction="column" style={{ padding: 15 }}>
                  <Grid
                    item
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {putoutDriver && putoutDriver.driver_image ? (
                      <Avatar
                        alt="driver profile image"
                        src={putoutDriver.driver_image}
                        style={{ width: 100, height: 100, objectFit: "cover" }}
                      />
                    ) : (
                      <AccountCircleIcon sx={{ width: 100, height: 100 }} />
                    )}
                  </Grid>
                  {putoutDriver && [
                    { key: "firstName", label: t("First Name"), value: putoutDriver.firstName },
                    { key: "lastName", label: t("Last Name"), value: putoutDriver.lastName },
                    { key: "mobile", label: t("Contact"), value: putoutDriver.mobile },
                    { key: "email", label: t("Email"), value: putoutDriver.email },
                    { key: "carType", label: t("Car Type"), value: putoutDriver.carType },
                    { key: "vehicleNumber", label: t("Vehicle Number"), value: putoutDriver.vehicleNumber },
                  ].map((item) => item.value ? renderGridItem(item, isRTL) : null)}
                </Grid>
              </Card>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("customer_info"))}
                <Grid container direction="column" style={{ padding: 15 }}>
                  <Grid
                    item
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {data.customer_image ? (
                      <Avatar
                        alt="customer profile image"
                        src={data.customer_image}
                        style={{ width: 100, height: 100, objectFit: "cover" }}
                      />
                    ) : (
                      <AccountCircleIcon sx={{ width: 100, height: 100 }} />
                    )}
                  </Grid>
                  {[
                    { key: "customer_name", label: t("Customer Name"), value: data.customer_name },
                    { key: "customer_contact", label: t("Contact"), value: data.customer_contact },
                    { key: "customer_email", label: t("Email"), value: data.customer_email },
                  ].map((item) => item.value ? renderGridItem(item, isRTL) : null)}
                </Grid>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </>
  );
}

export default PutoutDetails;