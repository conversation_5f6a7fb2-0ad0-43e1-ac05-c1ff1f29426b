import { Box, Button, Grid, Typography } from "@mui/material";
import { makeStyles, ThemeProvider } from "@mui/styles";
import { api } from "common";
import MaterialTable from "material-table";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { bookingHistoryColumns, FONT_FAMILY, MAIN_COLOR, SECONDORY_COLOR } from "../common/sharedFunctions";
import AlertDialog from "../components/AlertDialog";
import CircularLoading from "../components/CircularLoading";
import { colors } from "../components/Theme/WebTheme";
import theme from "../styles/tableStyle";

const useStyles = makeStyles(() => ({
  root: {
    padding: 24,
    background: colors.WHITE,
    minHeight: '100vh',
    boxSizing: 'border-box',
    '@media (max-width: 900px)': {
      padding: 12,
    },
    '@media (max-width: 600px)': {
      padding: 4,
    },
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '20px',
    gap: '16px',
  },
  customerInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    marginBottom: '20px',
    padding: '16px',
    backgroundColor: '#f5f5f5',
    borderRadius: '8px',
  },
  tableContainer: {
    width: '100%',
    overflowX: 'auto',
    boxSizing: 'border-box',
    '@media (max-width: 600px)': {
      minWidth: 320,
    },
  },
  action: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
    justifyContent: "center",
    borderRadius: "20px",
    paddingLeft: "10px",
    paddingRight: "10px",
    width: "10rem",
    minHeight: "40px"
  },
}));

const CustomerRidesDetail = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const classes = useStyles();
  const { customerId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const role = useSelector((state) => state.auth.profile.usertype);
  const settings = useSelector((state) => state.settingsdata.settings);
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const userdata = useSelector((state) => state.usersdata);
  const auth = useSelector((state) => state.auth);
  const [data, setData] = useState([]);
  const [customerInfo, setCustomerInfo] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [loading, setLoading] = useState(false);
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: "" });
  const dispatch = useDispatch();
  const { fetchBookings } = api;

  // Read mode from query string
  const searchParams = new URLSearchParams(location.search);
  const mode = searchParams.get('mode') || 'active';

  useEffect(() => {
    // Find customer information
    if (userdata.users && customerId) {
      const customer = userdata.users.find(user => 
        user.usertype === "customer" && 
        (user.id === customerId || user.uid === customerId)
      );
      setCustomerInfo(customer);
    }
  }, [userdata.users, customerId]);

  useEffect(() => {
    if (bookinglistdata.bookings && customerId) {
      // Filter bookings for this specific customer depending on mode
      const allCustomerBookings = bookinglistdata.bookings.filter(booking => 
        (booking.customer === customerId || booking.customer === customerInfo?.id)
      );
      if (mode === 'history') {
        setData(allCustomerBookings);
      } else {
        const activeStatuses = ["ACCEPTED", "ARRIVED", "STARTED", "Pending"];
        const activeBookings = allCustomerBookings.filter(booking => activeStatuses.includes(booking.status));
        setData(activeBookings);
      }
    } else {
      setData([]);
    }
  }, [bookinglistdata.bookings, customerId, customerInfo, mode]);

  const handleBack = () => {
    navigate('/assignedride?tab=2');
  };

  const columns = [
    ...bookingHistoryColumns(role, settings, t, isRTL).map(col => {
      if (col.field === 'status' || col.field === 'booking_status_web') {
        return {
          ...col,
          cellStyle: rowData => ({
            backgroundColor: rowData.status === 'CANCELLED' ? colors.RED : undefined,
            color: rowData.status === 'CANCELLED' ? colors.WHITE : undefined,
            fontWeight: rowData.status === 'CANCELLED' ? 'bold' : undefined,
            borderRadius: rowData.status === 'CANCELLED' ? '10px' : undefined,
            textAlign: 'center',
            padding: 3,
          })
        };
      }
      return col;
    }),
    {
      title: t("actions"),
      field: "actions",
      editable: "never",
      render: (rowData) => (
        <Button
          variant="contained"
          onClick={() => {
            if (settings.AllowCriticalEditsAdmin && (role === "admin" || role === "fleetadmin")) {
              // Navigate to booking details
              navigate(`/bookings/bookingdetails/${rowData.id}`);
            } else {
              setCommonAlert({ open: true, msg: t("demo_mode") });
            }
          }}
          style={{
            backgroundColor: colors.Action_Button_Back,
            color: colors.Header_Background,
            fontFamily: FONT_FAMILY,
            fontSize: '0.8rem',
            padding: '4px 8px',
            minWidth: '80px',
          }}
        >
          {t("view_details")}
        </Button>
      ),
    }
  ];

  return (
    <div className={classes.root}>
      <ThemeProvider theme={theme}>
        <div>
          <Button
            variant="text" 
            onClick={handleBack} 
          >
            <Typography
              style={{
                margin: "10px 10px 0 5px",
                textAlign: isRTL === "rtl" ? "right" : "left",
                fontWeight: "bold",
                color: MAIN_COLOR,
                fontFamily: FONT_FAMILY,
              }}
            >
              {`<<- ${t("go_back")}`}
            </Typography>
          </Button>
        </div>

        {/* Customer Information Header */}
        {customerInfo && (
          <div className={classes.customerInfo}>
            <Typography variant="h5" style={{ fontFamily: FONT_FAMILY, fontWeight: 'bold', color: MAIN_COLOR }}>
              {t("Customer Information")}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1" style={{ fontFamily: FONT_FAMILY }}>
                  <strong>{t("name")}:</strong> {customerInfo.firstName}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1" style={{ fontFamily: FONT_FAMILY }}>
                  <strong>{t("mobile")}:</strong> {settings.AllowCriticalEditsAdmin ? customerInfo.mobile : t("hidden_demo")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1" style={{ fontFamily: FONT_FAMILY }}>
                  <strong>{t("email")}:</strong> {settings.AllowCriticalEditsAdmin ? customerInfo.email : t("hidden_demo")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1" style={{ fontFamily: FONT_FAMILY }}>
                  <strong>{t("total_rides")}:</strong> {data.length}
                </Typography>
              </Grid>
            </Grid>
          </div>
        )}

        <Box className={classes.tableContainer}>
          <MaterialTable
            title={mode === 'history' ? t("Customer_Ride_History") : t("Customer_Active_Rides")}
            style={{
              direction: isRTL === "rtl" ? "rtl" : "ltr",
              borderRadius: "8px",
              boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
              minWidth: 320,
            }}
            columns={columns}
            data={data}
            onRowClick={(evt, selectedRow) => setSelectedRow(selectedRow.tableData.id)}
            options={{
              pageSize: 100,
              pageSizeOptions: [100, 500, { value: data.length, label: t("All") }],
              toolbarbuttonalignment: "right",
              exportButton: true,
              maxColumnSort: "all_columns",
              rowStyle: rowData => ({
                backgroundColor: selectedRow === rowData.tableData.id ? colors.ROW_SELECTED : colors.WHITE
              }),
              maxBodyHeight: "calc(100vh - 199.27px)",
              headerStyle: {
                position: "sticky",
                top: "0px",
                backgroundColor: SECONDORY_COLOR,
                color: colors.Black,
                fontWeight: "bold ",
                textAlign: "center",
                zIndex: 1,
                border: `1px solid ${colors.TABLE_BORDER}`,
              },
              cellStyle: {
                border: `1px solid ${colors.TABLE_BORDER}`,
                textAlign: "center",
                margin: "auto",
                minWidth: 100,
                fontSize: '0.95rem',
                padding: 4,
              },
            }}
            localization={{
              toolbar: {
                searchPlaceholder: t("search"),
                exportTitle: t("export"),
                exportCSVName: t("export"),
              },
              header: {
                actions: t("actions"),
              },
              pagination: {
                labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                firstTooltip: t("first_page_tooltip"),
                previousTooltip: t("previous_page_tooltip"),
                nextTooltip: t("next_page_tooltip"),
                lastTooltip: t("last_page_tooltip"),
              },
              body: {
                emptyDataSourceMessage: t("No rides to display"),
              },
            }}
          />
        </Box>

        <AlertDialog open={commonAlert.open} onClose={() => setCommonAlert({ open: false, msg: "" })}>
          {commonAlert.msg}
        </AlertDialog>
        {loading && <CircularLoading />}
      </ThemeProvider>
    </div>
  );
};

export default CustomerRidesDetail;
