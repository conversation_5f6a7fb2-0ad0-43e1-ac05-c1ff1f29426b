import { FontAwesome, Fontisto, Ionicons, MaterialCommunityIcons, Octicons } from '@expo/vector-icons';
import i18n from 'i18n-js';
import moment from 'moment/min/moment-with-locales';
import React, { useEffect, useState } from 'react';
import { Alert, Animated, Dimensions, FlatList, Image, Linking, Modal, Platform, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { Avatar } from 'react-native-elements';
import SegmentedControlTab from 'react-native-segmented-control-tab';
import StarRating from 'react-native-star-rating-widget';
import { useSelector } from 'react-redux';
import { fonts } from '../common/font';
import { appConsts, MAIN_COLOR } from '../common/sharedFunctions';
import { colors } from '../common/theme';
import Button from '../components/Button';

const { width, height } = Dimensions.get('window');

export default function RideList(props) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    //const isRTL = true;
    const settings = useSelector(state => state.settingsdata.settings);
    const [tabIndex, setTabIndex] = useState(0);
    const auth = useSelector(state => state.auth);


    const [scaleAnim] = useState(new Animated.Value(0))
    useEffect(() => {
        Animated.spring(
            scaleAnim,
            {
                toValue: 1,
                friction: 3,
                useNativeDriver: true
            }
        ).start();
    }, [])

    const onPressButton = (item, index) => {
        props.onPressButton(item, index)
    }

    const onPressAction = (item, index) => {
        props.onPressAction(item, index)
    }

    const onChatAction = (item, index) => {
        props.onChatAction(item, index)
    }

    const [role, setRole] = useState();
    const [userInfoModalStatus, setUserInfoModalStatus] = useState(false);
    const [editingIndex, setEditingIndex] = useState(null);
    const [editedItem, setEditedItem] = useState({});

    useEffect(() => {
        if (auth.profile && auth.profile.usertype) {
            setRole(auth.profile.usertype);
        } else {
            setRole(null);
        }
    }, [auth.profile]);

    const onPressCall = (phoneNumber) => {
        let call_link = Platform.OS == 'android' ? 'tel:' + phoneNumber : 'telprompt:' + phoneNumber;
        Linking.openURL(call_link);
    }

    const onAlert = (item) => {
        Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_call'));
    }

    const onChatAlert = (item) => {
        Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_chat'));
    }

    const goHome = () => {
        props.goHome()
    }

    const handleEdit = (item, index) => {
        setEditingIndex(index);
        setEditedItem({
            pickup: { ...item.pickup },
            drop: { ...item.drop },
            trip_cost: item.trip_cost,
            flightNumber: item.flightNumber || ''
        });
    };

    const handleSave = (item, index) => {
        console.log("item",item)
        if (props.data && props.data.length > index) {
            props.data[index].pickup.add = editedItem.pickup.add;
            props.data[index].drop.add = editedItem.drop.add;
            props.data[index].trip_cost = editedItem.trip_cost;
            props.data[index].flightNumber = editedItem.flightNumber;
        }
        setEditingIndex(null);
        setEditedItem({});
    };

    const renderData = ({ item, index }) => {
        const isCustomer = auth && auth.profile && auth.profile.usertype === 'customer';
        const canEdit = tabIndex === 0 && isCustomer;
        const isEditing = editingIndex === index && canEdit;
        return (
            <TouchableOpacity activeOpacity={0.8} onPress={() => onPressButton(item, index)} style={[styles.BookingContainer, styles.elevation]} >
                <View style={[styles.box, { padding: 15 },]}>
                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1 }}>
                        <View style={{ flexDirection: 'column', flex: 1 }}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                <View style={{ width: 30, alignItems: 'center' }}>
                                    <Ionicons name="location-outline" size={24} color={colors.BALANCE_GREEN} />
                                    <View style={[styles.hbox2, { flex: 1, minHeight: 5 }]} />
                                </View>
                                <View style={{ flex: 1, marginBottom: 10 }}>
                                    {isEditing ? (
                                        <TextInput
                                            style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                            value={editedItem.pickup.add}
                                            onChangeText={text => setEditedItem({ ...editedItem, pickup: { ...editedItem.pickup, add: text } })}
                                        />
                                    ) : (
                                        <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{item.pickup.add} </Text>
                                    )}
                                </View>
                            </View>

                            {item && item.waypoints && item.waypoints.length > 0 ?
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <View style={{ width: 30, alignItems: 'center' }}>
                                        <Ionicons name="location-outline" size={24} color={colors.BOX_BG} />

                                        <View style={[styles.hbox2, { flex: 1, minHeight: 5 }]} />
                                    </View>
                                    <View style={{ flex: 1, marginBottom: 10 }}>
                                        <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{item.waypoints.length} {t('stops')}</Text>
                                    </View>
                                </View>
                                : null}

                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                <View style={{ width: 30, alignItems: 'center' }}>
                                    <Ionicons name="location-outline" size={24} color={colors.BUTTON_ORANGE} />
                                </View>
                                <View style={{ flex: 1, marginBottom: 10 }}>
                                    {isEditing ? (
                                        <TextInput
                                            style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                            value={editedItem.drop.add}
                                            onChangeText={text => setEditedItem({ ...editedItem, drop: { ...editedItem.drop, add: text } })}
                                        />
                                    ) : (
                                        <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{item.drop.add}</Text>
                                    )}
                                </View>
                            </View>

                            {tabIndex === 0 && (
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 10 }}>
                                    <Text style={[styles.textStyle, { width: 100 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>Flight Number:</Text>
                                    {isEditing ? (
                                        <TextInput
                                            style={[styles.textStyle, { flex: 1, borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                            value={editedItem.flightNumber}
                                            onChangeText={text => setEditedItem({ ...editedItem, flightNumber: text })}
                                            placeholder="Enter flight number"
                                        />
                                    ) : (
                                        <Text style={[styles.textStyle, { flex: 1 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>{item.flightNumber || '-'}</Text>
                                    )}
                                </View>
                            )}
                        </View>
                    </View>

                    <View style={{ flexDirection: 'column', flex: 1, minHeight: 60 }}>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, minHeight: 60 }}>
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Text style={{ fontFamily: fonts.Bold, fontSize: 24, color: MAIN_COLOR, opacity: 0.8 }}>{settings.symbol}</Text>
                                <Text style={styles.textStyleBold}>{item && item.trip_cost > 0 ? parseFloat(item.trip_cost).toFixed(settings.decimal) : item && item.estimate ? parseFloat(item.estimate).toFixed(settings.decimal) : 0}</Text>
                            </View>
                            <View style={[styles.hbox2, { minHeight: 5, width: 1, margin: 2 }]} />
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Fontisto name="map" size={26} color={MAIN_COLOR} style={{ opacity: 0.8 }} />
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <Text style={styles.textStyleBold}>{item && item.distance > 0 ? parseFloat(item.distance).toFixed(settings.decimal) : 0}</Text>
                                    <Text style={styles.textStyle}> {settings && settings.convert_to_mile ? t("mile") : t("km")} </Text>
                                </View>
                            </View>
                        </View>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, minHeight: 60 }}>
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row', borderBottomWidth: 0 }]}>
                                <Octicons name="clock" size={26} color={MAIN_COLOR} style={{ opacity: 0.8 }} />
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <Text style={styles.textStyleBold}>{(item && item.total_trip_time && item.total_trip_time > 0 ? parseFloat(item.total_trip_time / 60).toFixed(0) == 0 ? "1" : parseFloat(item.total_trip_time / 60).toFixed(0) : parseFloat(item.estimateTime / 60).toFixed(0))}</Text>
                                    <Text style={styles.textStyle}> {t("mins")} </Text>
                                </View>
                            </View>
                            <View style={[styles.hbox2, { minHeight: 5, width: 1, margin: 2 }]} />
                            <View style={[styles.clock, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                {item && item.trip_start_time && item.trip_end_time ?
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                        <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                            <Ionicons name="location-outline" size={28} color={colors.BALANCE_GREEN} />
                                            <View>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))).length == 2 ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : "0" + (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : ""}</Text>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))).length == 2 ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":")), ((item.trip_start_time).lastIndexOf(":"))) : ":0" + (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))) : ""}</Text>
                                                </View>
                                                <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{item.startTime ? moment(item.startTime).format('ll') : ''}</Text>
                                            </View>
                                        </View>
                                        <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                            <Ionicons name="location-outline" size={28} color={colors.BUTTON_ORANGE} />
                                            <View>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_end_time ? (item.trip_end_time).substring(0, ((item.trip_end_time).indexOf(":"))).length == 2 ? (item.trip_end_time).substring(0, ((item.trip_end_time).indexOf(":"))) : "0" + (item.trip_end_time).substring(0, ((item.trip_end_time).indexOf(":"))) : ""}</Text>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_end_time ? (item.trip_end_time).substring(((item.trip_end_time).indexOf(":") + 1), ((item.trip_end_time).lastIndexOf(":"))).length == 2 ? (item.trip_end_time).substring(((item.trip_end_time).indexOf(":")), ((item.trip_end_time).lastIndexOf(":"))) : ":0" + (item.trip_end_time).substring(((item.trip_end_time).indexOf(":") + 1), ((item.trip_end_time).lastIndexOf(":"))) : ""}</Text>
                                                </View>
                                                <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{item.endTime ? moment(item.endTime).format('ll') : ''}</Text>
                                            </View>
                                        </View>
                                    </View>
                                    : item && item.trip_start_time ?
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                            <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                                <Ionicons name="location-outline" size={28} color={colors.BALANCE_GREEN} />
                                                <View>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))).length == 2 ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : "0" + (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : ""}</Text>
                                                        <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))).length == 2 ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":")), ((item.trip_start_time).lastIndexOf(":"))) : ":0" + (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))) : ""}</Text>
                                                    </View>
                                                    <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{item.startTime ? moment(item.startTime).format('ll') : ''}</Text>
                                                </View>
                                            </View>
                                            <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                                <Ionicons name="location-outline" size={28} color={colors.BUTTON_ORANGE} />
                                                <Image source={require('../../assets/images/clock.gif')} style={{ width: 25, height: 25, alignSelf: 'center', resizeMode: 'center' }} />
                                            </View>
                                        </View>
                                        :
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                            <Text style={styles.textStyleBold}>{item && item.reason ? item.reason : t(item.status).toUpperCase()}</Text>
                                        </View>
                                }
                            </View>
                        </View>
                    </View>

                    {item ?
                        <View style={[styles.driverDetails, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', flex: 1 }}>
                                {item ?
                                    (!(item.driver_image == '' || item.driver_image == null || item.driver_image == 'undefined') && auth.profile.usertype == 'customer') ?
                                        <Avatar
                                            size="medium"
                                            rounded
                                            source={{ uri: item.driver_image }}
                                            activeOpacity={0.7}
                                        />
                                        :
                                        (!(item.customer_image == '' || item.customer_image == null || item.customer_image == 'undefined') && auth.profile.usertype == 'driver') ?
                                            <Avatar
                                                size="medium"
                                                rounded
                                                source={{ uri: item.customer_image }}
                                                activeOpacity={0.7}
                                            />
                                            : item.driver_name != '' ?

                                                <Avatar
                                                    size="medium"
                                                    rounded
                                                    source={require('../../assets/images/profilePic.png')}
                                                    activeOpacity={0.7}
                                                /> : null
                                    : null}
                                <View style={[styles.userView, { flex: 1, marginHorizontal: 5 }]}>
                                    {item && item.driver_name != '' && auth.profile.usertype == 'customer' ? <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{item.driver_name ? item.driver_name : t('no_name')}</Text> : null}

                                    {item && item.customer_name != '' && auth.profile.usertype == 'driver' ? <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{item.customer_name ? item.customer_name : t('no_name')}</Text> : null}

                                    {item && item.rating > 0 && item.driver_name && auth.profile.usertype == 'customer' ?
                                        <View>
                                            <StarRating
                                                maxStars={5}
                                                starSize={15}
                                                enableHalfStar={true}
                                                color={MAIN_COLOR}
                                                emptyColor={MAIN_COLOR}
                                                rating={item && item.rating ? parseFloat(item.rating) : 0}
                                                style={[styles.contStyle, isRTL ? { marginRight: 0, transform: [{ scaleX: -1 }] } : { marginLeft: -8 }]}
                                                onChange={() => {
                                                    //console.log('hello')
                                                }}
                                            />
                                        </View>
                                        : null}
                                </View>
                            </View>
                            {item && ((item.driver_contact || item.customer_contact)) ?
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                                    <TouchableOpacity onPress={() => (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(item.status) != -1) ? role == 'customer' ?
                                        onPressCall(item.driver_contact) : (item.otherPersonPhone && item.otherPersonPhone.length > 0 ? onPressCall(item.otherPersonPhone) : onPressCall(item.customer_contact)) : onAlert(item)}
                                        style={{ backgroundColor: MAIN_COLOR, height: 40, width: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', margin: 3 }}>
                                        <Ionicons name="call" size={24} color={colors.WHITE} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(item.status) != -1) ? onChatAction(item, index) : onChatAlert(item)} style={{ backgroundColor: MAIN_COLOR, height: 40, width: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', margin: 3 }}>
                                        <Ionicons name="chatbubble-ellipses-sharp" size={24} color={colors.WHITE} />
                                    </TouchableOpacity>
                                </View>
                                : null}
                        </View>
                        : null}

                    {(item && item.status && auth.profile && auth.profile.uid &&
                        (((['PAYMENT_PENDING', 'NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING', 'PAID'].indexOf(item.status) != -1) && auth.profile.usertype == 'customer') ||
                            ((['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED'].indexOf(item.status) != -1) && auth.profile.usertype == 'driver'))) ?
                        <Button
                            title={item.status == 'PAID' ? t('add_to_review') : item.status == 'PAYMENT_PENDING' ? t('paynow_button') : t('go_to_booking')}
                            loading={false}
                            loadingColor={{ color: colors.GREEN }}
                            buttonStyle={[styles.textStyleBold, { color: colors.WHITE }]}
                            style={{ backgroundColor: MAIN_COLOR, marginVertical: 10 }}
                            btnClick={() => { onPressAction(item, index) }}
                        />
                        : null}

                    <Modal
                        animationType="fade"
                        transparent={true}
                        visible={userInfoModalStatus}
                        onRequestClose={() => {
                            setUserInfoModalStatus(false);
                        }}
                    >
                        <View style={styles.centeredView}>
                            <View style={styles.modalView}>
                                <View style={{ width: '100%' }}>
                                    {item && item.deliveryPersonPhone ?
                                        <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                            <Text style={styles.textStyleBold}>{t('senderPersonPhone')}</Text>
                                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 10 }}>
                                                <Ionicons name="call" size={24} color={colors.BLACK} />
                                                <Text style={styles.textContent1} onPress={() => onPressCall(item.deliveryPersonPhone)}> {item.deliveryPersonPhone} </Text>
                                            </View>
                                        </View>
                                        : null}
                                    {item && item.customer_contact ?
                                        <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                            <Text style={styles.textStyleBold}>{t('senderPersonPhone')}</Text>
                                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 10 }}>
                                                <Ionicons name="call" size={24} color={colors.BLACK} />
                                                <Text style={styles.textContent1} onPress={() => onPressCall(item.customer_contact)}> {item.customer_contact} </Text>
                                            </View>
                                        </View>
                                        : null}
                                </View>
                                <TouchableOpacity
                                    loading={false}
                                    onPress={() => setUserInfoModalStatus(false)}
                                    style={styles.modalBtn}
                                >
                                    <Text style={styles.textStyleBold}>{t('ok')}</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </Modal>
                    {canEdit && (
                        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10 }}>
                            {isEditing ? (
                                <TouchableOpacity onPress={() => handleSave(item, index)} style={{ backgroundColor: '#34C759', padding: 8, borderRadius: 6, marginLeft: 8 }}>
                                    <Text style={{ color: '#fff', fontWeight: 'bold' }}>Save</Text>
                                </TouchableOpacity>
                            ) : (
                                <TouchableOpacity onPress={() => handleEdit(item, index)} style={{ backgroundColor: '#007AFF', padding: 8, borderRadius: 6 }}>
                                    <Text style={{ color: '#fff', fontWeight: 'bold' }}>Edit</Text>
                                </TouchableOpacity>
                            )}
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        )
    }

    // Remove navigation state for assigned cards
    // const [assignedCardIndex, setAssignedCardIndex] = useState(0);
    // assignedCards array remains
    const assignedCards = [
        {
            date: '22 Jun 2025 15:14',
            price: '$63.00',
            distance: '34.52 mi',
            duration: '37 mins',
            pickup: 'adidas Outlet Store Aurora, Chicago Premium Outlets, Premium Outlet Boulevard, Aurora, IL 60502, USA',
            drop: "O'Hare International Airport (ORD), West O'Hare Avenue, Chicago, IL 60666, USA",
            payment: 'Cash',
        },
        {
            date: '23 Jun 2025 10:45',
            price: '$48.50',
            distance: '18.20 mi',
            duration: '22 mins',
            pickup: 'Millennium Park, 201 E Randolph St, Chicago, IL 60602, USA',
            drop: 'Navy Pier, 600 E Grand Ave, Chicago, IL 60611, USA',
            payment: 'Card',
        },
    ];

    const renderAssignedCard = () => (
        <ScrollView style={{ flex: 1 }} contentContainerStyle={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 10 }}>
            {assignedCards.map((card, idx) => (
                <View key={idx} style={{
                    backgroundColor: '#FFFFFF',
                    borderRadius: 16,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.15,
                    shadowRadius: 6,
                    elevation: 4,
                    margin: 16,
                    width: 320,
                    alignSelf: 'center',
                }}>
                    {/* Header Section */}
                    <View style={{ backgroundColor: '#E8E3F3', paddingVertical: 18, paddingHorizontal: 14, alignItems: 'center', borderTopLeftRadius: 16, borderTopRightRadius: 16 }}>
                        <Text style={{ fontSize: 18, fontWeight: '600', color: '#000000', marginBottom: 6 }}>{card.date}</Text>
                        <Text style={{ fontSize: 36, fontWeight: 'bold', color: '#000000', marginBottom: 10 }}>{card.price}</Text>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%', paddingHorizontal: 24 }}>
                            <Text style={{ fontSize: 20, fontWeight: '500', color: '#000000' }}>{card.distance}</Text>
                            <Text style={{ fontSize: 20, fontWeight: '500', color: '#000000' }}>{card.duration}</Text>
                        </View>
                    </View>
                    {/* Location Details */}
                    <View style={{ paddingHorizontal: 14, paddingVertical: 14 }}>
                        <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 16 }}>
                            <View style={{ width: 36, height: 36, borderRadius: 18, backgroundColor: '#1a237e', justifyContent: 'center', alignItems: 'center', marginRight: 10, marginTop: 3 }}>
                                <View style={{ width: 14, height: 14, backgroundColor: '#FFFFFF', borderRadius: 7 }} />
                            </View>
                            <Text style={{ flex: 1, fontSize: 14, lineHeight: 18, color: '#000000', fontWeight: '400' }}>
                                {card.pickup}
                            </Text>
                        </View>
                        <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 16 }}>
                            <View style={{ width: 36, height: 36, borderRadius: 18, backgroundColor: '#1a237e', justifyContent: 'center', alignItems: 'center', marginRight: 10, marginTop: 3 }}>
                                <View style={{ width: 14, height: 14, backgroundColor: '#FFFFFF', borderRadius: 7 }} />
                            </View>
                            <Text style={{ flex: 1, fontSize: 14, lineHeight: 18, color: '#000000', fontWeight: '400' }}>
                                {card.drop}
                            </Text>
                        </View>
                    </View>
                    {/* Payment Mode */}
                    <View style={{ paddingHorizontal: 14, marginBottom: 18 }}>
                        <Text style={{ fontSize: 14, color: '#000000', fontWeight: '500' }}>
                            PAYMENT MODE - <Text style={{ fontWeight: 'bold' }}>{card.payment}</Text>
                        </Text>
                    </View>
                    {/* Action Buttons */}
                    <View style={{ flexDirection: 'row', paddingHorizontal: 14, paddingBottom: 18, gap: 10 }}>
                        <TouchableOpacity
                            style={{ flex: 1, backgroundColor: '#FF3B30', paddingVertical: 12, borderRadius: 10, alignItems: 'center', justifyContent: 'center' }}
                            onPress={() => { }} activeOpacity={0.8}
                        >
                            <Text style={{ color: '#FFFFFF', fontSize: 16, fontWeight: 'bold', letterSpacing: 1 }}>PUT OUT</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={{ flex: 1, backgroundColor: '#34C759', paddingVertical: 12, borderRadius: 10, alignItems: 'center', justifyContent: 'center' }}
                            onPress={() => { }} activeOpacity={0.8}
                        >
                            <Text style={{ color: '#FFFFFF', fontSize: 16, fontWeight: 'bold', letterSpacing: 1 }}>ACCEPT</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            ))}
        </ScrollView>
    );

    // Ride booking dummy data
    const rideBookingData = {
        id: "booking_001",
        status: "DRIVER_CANCELLED",
        pickup: {
            name: "O'Hare International Airport (ORD)",
            address: "West O'Hare Avenue, Chicago, IL 60666, USA",
            coordinates: {
                latitude: 41.9786,
                longitude: -87.9048
            }
        },
        drop: {
            name: "Millennium Park",
            address: "201 E Randolph St, Chicago, IL 60602, USA",
            coordinates: {
                latitude: 41.8826,
                longitude: -87.6226
            }
        },
        tripdate: '24 Jun 2025 09:30',
        trip_cost: 52.75,
        estimateDistance: 28.4,
        estimateTime: 29 * 60, // in seconds
        payment: 'Cash',
    };

    const renderPutOutCard = () => (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ alignItems: 'center', paddingVertical: 20, paddingHorizontal: 10 }}>
            {/* First dummy card */}
            <View style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.15,
                shadowRadius: 6,
                elevation: 4,
                margin: 16,
                width: 340,
                padding: 0,
                alignSelf: 'center',
            }}>
                {/* Location Section */}
                <View style={{ marginBottom: 30, width: '100%', padding: 20, paddingBottom: 0 }}>
                    {/* Origin */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#48BB78' }} />
                            <View style={{ width: 2, height: 40, backgroundColor: '#E2E8F0', marginTop: 5 }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            O'Hare International Airport (ORD), West O'Hare Avenue, Chicago, IL 60666, USA
                        </Text>
                    </View>
                    {/* Destination */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#ED8936' }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            adidas Outlet Store Aurora, Chicago Premium Outlets, Premium Outlet Boulevard, Aurora, IL 60502, USA
                        </Text>
                    </View>
                </View>
                {/* Trip Details */}
                <View style={{ backgroundColor: 'white', borderRadius: 8, padding: 20, marginBottom: 30, elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.1, shadowRadius: 3, width: '100%' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <FontAwesome name="dollar" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>63.00</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="map" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>33.39 mi</Text>
                        </View>
                    </View>
                    <View style={{ height: 1, backgroundColor: '#E2E8F0', marginVertical: 20 }} />
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="access-time" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>40 mins</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flex: 1, alignItems: 'flex-end' }}>
                            <Text style={{ fontSize: 16, fontWeight: '700', color: '#2D3748', textAlign: 'right', lineHeight: 20 }}>DRIVER CANCELLED{"\n"}BOOKING</Text>
                        </View>
                    </View>
                </View>
                {/* Bottom Section */}
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%', padding: 20, paddingTop: 0 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <Image
                            source={{ uri: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/photo_6244658431936022129_y.jpg-0ifrxCvCJp9xUtwzKyCsNcxAY3Yo9r.jpeg' }}
                            style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                        />
                        <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748' }}>test data</Text>
                    </View>
                    <View style={{ flexDirection: 'row', gap: 15 }}>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center', marginRight: 10 }}>
                            <MaterialCommunityIcons name="phone" size={24} color="white" />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center' }}>
                            <MaterialCommunityIcons name="chat" size={24} color="white" />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
            {/* Second dummy card */}
            <View style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.15,
                shadowRadius: 6,
                elevation: 4,
                margin: 16,
                width: 340,
                padding: 0,
                alignSelf: 'center',
            }}>
                {/* Location Section */}
                <View style={{ marginBottom: 30, width: '100%', padding: 20, paddingBottom: 0 }}>
                    {/* Origin */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#48BB78' }} />
                            <View style={{ width: 2, height: 40, backgroundColor: '#E2E8F0', marginTop: 5 }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            Union Station, 225 S Canal St, Chicago, IL 60606, USA
                        </Text>
                    </View>
                    {/* Destination */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#ED8936' }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            Willis Tower, 233 S Wacker Dr, Chicago, IL 60606, USA
                        </Text>
                    </View>
                </View>
                {/* Trip Details */}
                <View style={{ backgroundColor: 'white', borderRadius: 8, padding: 20, marginBottom: 30, elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.1, shadowRadius: 3, width: '100%' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <FontAwesome name="dollar" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>28.50</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="map" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>7.2 mi</Text>
                        </View>
                    </View>
                    <View style={{ height: 1, backgroundColor: '#E2E8F0', marginVertical: 20 }} />
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="access-time" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>15 mins</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flex: 1, alignItems: 'flex-end' }}>
                            <Text style={{ fontSize: 16, fontWeight: '700', color: '#2D3748', textAlign: 'right', lineHeight: 20 }}>DRIVER CANCELLED{"\n"}BOOKING</Text>
                        </View>
                    </View>
                </View>
                {/* Bottom Section */}
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%', padding: 20, paddingTop: 0 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <Image
                            source={{ uri: 'https://randomuser.me/api/portraits/men/32.jpg' }}
                            style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                        />
                        <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748' }}>John Doe</Text>
                    </View>
                    <View style={{ flexDirection: 'row', gap: 15 }}>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center', marginRight: 10 }}>
                            <MaterialCommunityIcons name="phone" size={24} color="white" />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center' }}>
                            <MaterialCommunityIcons name="chat" size={24} color="white" />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ScrollView>
    );

    const tabValues = [
        t('active_booking'),
        t('COMPLETE'),
        t('CANCELLED'),
    ];

    if (auth.profile && auth.profile.usertype === 'driver') {
        tabValues.push('PUT OUT', 'ASSIGNED RIDE');
    }

    return (
        <View style={styles.textView3}>
            <View style={{ backgroundColor: MAIN_COLOR }}>
                <SegmentedControlTab
                    values={tabValues}
                    selectedIndex={tabIndex}
                    onTabPress={(index) => setTabIndex(index)}
                    borderRadius={0}
                    tabsContainerStyle={[styles.segmentcontrol, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
                    tabStyle={{
                        borderWidth: 0,
                        backgroundColor: 'transparent',
                        borderColor: colors.WHITE
                    }}
                    activeTabStyle={{ borderBottomColor: colors.WHITE, backgroundColor: 'transparent', borderBottomWidth: 1.5 }}
                    tabTextStyle={{ color: appConsts.canCall ? colors.BORDER_TEXT : colors.PROFILE_PLACEHOLDER_CONTENT, fontFamily: fonts.Bold }}
                    activeTabTextStyle={{ color: colors.WHITE }}
                />
                <View style={{ height: '100%' }}>
                    <View style={{ height: 5 }} />
                    <View style={styles.listView}>
                        <View style={{ marginTop: 5, flex: 1, marginBottom: 100 }}>
                            {tabIndex === 4 ? (
                                renderAssignedCard()
                            ) : tabIndex === 3 ? (
                                renderPutOutCard()
                            ) : (
                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    scrollEnabled={true}
                                    keyExtractor={(item, index) => index.toString()}
                                    data={
                                        tabIndex === 0 ? props.data.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')) :
                                            tabIndex === 1 ? props.data.filter(item => item.status === 'COMPLETE') :
                                                tabIndex === 2 ? props.data.filter(item => item.status === 'CANCELLED') :
                                                    []
                                    }
                                    renderItem={renderData}
                                    ListEmptyComponent={
                                        <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                            <View style={styles.emptyBox}  >
                                                <Text style={styles.emptyText} >{t('no_data_available')}</Text>
                                            </View>
                                        </View>
                                    }
                                />
                            )}
                        </View>
                    </View>
                </View>

            </View>

        </View>
    );
};

const styles = StyleSheet.create({
    BookingContainer: {
        margin: 10,
        borderRadius: 10,
        shadowColor: "black",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 1,
    },
    box: {
        backgroundColor: colors.WHITE,
        borderRadius: 10,
    },
    elevation: {
        elevation: 5
    },
    dateStyle: {
        fontFamily: fonts.Bold,
        color: colors.HEADER,
        fontSize: 18
    },
    textView3: {
        flex: 1,
        backgroundColor: MAIN_COLOR
    },
    segmentcontrol: {
        color: colors.WHITE,
        fontSize: 18,
        fontFamily: fonts.Regular,
        marginTop: 0,
        alignSelf: "center",
        height: 50
    },
    location: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginVertical: 6,
        marginHorizontal: 5
    },
    hbox2: {
        width: 1,
        backgroundColor: MAIN_COLOR
    },
    textStyle: {
        fontSize: 16,
        fontFamily: fonts.Regular
    },
    textStyleBold: {
        fontSize: 16,
        fontFamily: fonts.Bold
    },
    centeredView: {
        flex: 1,
        justifyContent: "center",
        backgroundColor: colors.BACKGROUND
    },
    modalView: {
        margin: 20,
        backgroundColor: "white",
        borderRadius: 20,
        padding: 35,
        alignItems: "flex-start",
        shadowColor: colors.BLACK,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    textContainerStyle: {
        flexDirection: 'column',
        marginBottom: 12,
    },
    details: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        borderBottomWidth: .6,
        borderBottomColor: MAIN_COLOR
    },
    clock: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        minHeight: 60
    },
    section: {
        flex: 1,
        justifyContent: 'space-evenly',
        alignItems: 'center'
    },
    driverDetails: {
        flex: 1,
        alignItems: 'center',
        marginTop: 10,
        paddingVertical: 10
    },
    modalBtn: {
        flexDirection: 'row',
        alignSelf: 'center',
        borderWidth: 1,
        minWidth: 80,
        padding: 5,
        justifyContent: 'center',
        borderRadius: 10
    },
    listView: {
        flex: 1,
        backgroundColor: colors.WHITE,
        width: '100%',
        borderTopRightRadius: 10,
        borderTopLeftRadius: 10
    },
    emptyListContainer: {
        width: width,
        justifyContent: "center",
        alignItems: "center"
    },
    emptyBox: {
        backgroundColor: MAIN_COLOR,
        borderRadius: 10
    },
    emptyText: {
        fontFamily: fonts.Bold,
        color: colors.WHITE,
        padding: 15,
        fontSize: 18
    },
    textContent1: {
        fontFamily: fonts.Regular
    }
});