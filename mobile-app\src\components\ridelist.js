import { FontAwesome, Fontisto, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { api } from 'common';
import i18n from 'i18n-js';
import moment from 'moment/min/moment-with-locales';
import { useEffect, useState } from 'react';
import { Alert, Animated, Dimensions, FlatList, Image, Linking, Modal, Platform, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Avatar } from 'react-native-elements';
import SegmentedControlTab from 'react-native-segmented-control-tab';
import StarRating from 'react-native-star-rating-widget';
import { useDispatch, useSelector } from 'react-redux';
import { fonts } from '../common/font';
import { appConsts, MAIN_COLOR } from '../common/sharedFunctions';
import { colors } from '../common/theme';
import Button from '../components/Button';
const { GoogleMapApiConfig } = require('../../config/GoogleMapApiConfig');

const { width, height } = Dimensions.get('window');

export default function RideList(props) {
    const {
        updateBooking,
        fetchTasks,
        acceptTask,
        updateProfile
    } = api;
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    //const isRTL = true;
    const settings = useSelector(state => state.settingsdata.settings);
    const [tabIndex, setTabIndex] = useState(
        typeof props.tabIndex === 'number' ? props.tabIndex : 0
    );
    const auth = useSelector(state => state.auth);
    const dispatch = useDispatch();
    const tripdata = useSelector(state => state.tripdata);
    const tasks = useSelector(state => state.taskdata.tasks);
    const farelistdata = useSelector(state => state.farelist.fare);
    const cartypes = useSelector(state => state.cartypes);
    // Remove subTabIndex state and related logic 
    //const [subTabIndex, setSubTabIndex] = useState(0);

    const [scaleAnim] = useState(new Animated.Value(0))
    useEffect(() => {
        Animated.spring(
            scaleAnim,
            {
                toValue: 1,
                friction: 3,
                useNativeDriver: true
            }
        ).start();
    }, [])

    useEffect(() => {
        if (editingIndex !== null && editedItem && tripdata) {
            // Only update the specific address that was changed, preserve the other
            setEditedItem(prev => {
                const updated = { ...prev };

                // If tripdata.pickup exists and has changed, update pickup
                if (tripdata.pickup && tripdata.pickup.add) {
                    updated.pickup = {
                        ...updated.pickup,
                        ...tripdata.pickup,
                        // Ensure postal code is preserved or updated
                        postalCode: tripdata.pickup.postalCode || updated.pickup.postalCode
                    };
                }

                // If tripdata.drop exists and has changed, update drop
                if (tripdata.drop && tripdata.drop.add) {
                    updated.drop = {
                        ...updated.drop,
                        ...tripdata.drop,
                        // Ensure postal code is preserved or updated
                        postalCode: tripdata.drop.postalCode || updated.drop.postalCode
                    };
                }

                return updated;
            });
        }
        // Only run when tripdata.pickup or tripdata.drop changes
    }, [tripdata.pickup, tripdata.drop]);

    const onPressButton = (item, index) => {
        // If in Put Out tab, pass the flag
        if (auth.profile && auth.profile.usertype === 'driver' && tabIndex === 3) {
            props.navigation.push('RideDetails', { data: item, fromPutOutTab: true });
        } else {
            props.navigation.push('RideDetails', { data: item });
        }
    }

    const onPressAction = (item, index) => {
        props.onPressAction(item, index)
    }

    const onChatAction = (item, index) => {
        props.onChatAction(item, index)
    }

    const [role, setRole] = useState();
    const [userInfoModalStatus, setUserInfoModalStatus] = useState(false);
    const [editingIndex, setEditingIndex] = useState(null);
    const [editedItem, setEditedItem] = useState({});
    const [tripDatePickerOpen, setTripDatePickerOpen] = useState(false);
    const [openDatePickerIndex, setOpenDatePickerIndex] = useState(null);

    // State
    const [putOutModalVisible, setPutOutModalVisible] = useState(false);
    const [putOutReason, setPutOutReason] = useState('');
    const [putOutCard, setPutOutCard] = useState(null);

    useEffect(() => {
        if (auth.profile && auth.profile.usertype) {
            setRole(auth.profile.usertype);
        } else {
            setRole(null);
        }
    }, [auth.profile]);

    const onPressCall = (phoneNumber) => {
        let call_link = Platform.OS == 'android' ? 'tel:' + phoneNumber : 'telprompt:' + phoneNumber;
        Linking.openURL(call_link);
    }

    const onAlert = (item) => {
        Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_call'));
    }

    const onChatAlert = (item) => {
        Alert.alert(t('alert'), t('booking_is') + item.status + "." + t('not_chat'));
    }

    const goHome = () => {
        props.goHome()
    }

    const handleEdit = (item, index) => {
        setEditingIndex(index);
        setEditedItem({
            pickup: {
                ...item.pickup,
                add: item.pickup && item.pickup.add ? item.pickup.add : (item.pickupAddress || '')
            },
            drop: {
                ...item.drop,
                add: item.drop && item.drop.add ? item.drop.add : (item.dropAddress || '')
            },
            trip_cost: item.trip_cost,
            flightNumber: item.flightNumber || '',
            tripdate: item.tripdate || null,
            returnFlightNumber: item.returnFlightNumber || '',
            noteText: item.noteText || '', // Add noteText to editedItem
            carType: item.carType || '', // Add carType to editedItem
            otherPerson: item.otherPerson || '',
            otherPersonPhone: item.otherPersonPhone || ''
        });
    };

    const handleSave = async (item, index) => {
        // Use tripdata for updated pickup/drop if editing
        // 1) Validate pincodes if pickup/drop changed via edit
        const extractPostalCode = (address) => {
            if (!address || typeof address !== 'string') return '';
            const match = address.match(/\b\d{5}(?:-\d{4})?\b/);
            return match ? match[0] : '';
        };

        // Determine effective addresses that will be saved
        const effectivePickupAddress = (editingIndex !== null && tripdata.pickup && tripdata.pickup.add)
            ? tripdata.pickup.add
            : (editedItem && editedItem.pickup && editedItem.pickup.add) || (item.pickup && item.pickup.add) || item.pickupAddress;
        const effectiveDropAddress = (editingIndex !== null && tripdata.drop && tripdata.drop.add)
            ? tripdata.drop.add
            : (editedItem && editedItem.drop && editedItem.drop.add) || (item.drop && item.drop.add) || item.dropAddress;

        // Only run pincode validation when editing
        if (editingIndex !== null) {
            let pickupPostal = (tripdata.pickup && tripdata.pickup.postalCode) || extractPostalCode(effectivePickupAddress);
            let dropPostal = (tripdata.drop && tripdata.drop.postalCode) || extractPostalCode(effectiveDropAddress);

            // If postal codes are missing, try to get them from Google Geocoding API
            console.log("effectivePickupAddress", effectivePickupAddress);
            console.log("effectiveDropAddress", effectiveDropAddress);
            if (!pickupPostal && effectivePickupAddress) {
                pickupPostal = await getPostalCodeFromAddress(effectivePickupAddress);
            }
            if (!dropPostal && effectiveDropAddress) {
                dropPostal = await getPostalCodeFromAddress(effectiveDropAddress);
            }

            console.log("pickupPostal", pickupPostal);
            console.log("dropPostal", dropPostal);

            if (!pickupPostal || !dropPostal) {
                Alert.alert(t('alert'), t('postal_code_error_booking_not_available'));
                return;
            }
            var matchedFareValue = null;
            if (Array.isArray(farelistdata) && farelistdata.length > 0) {
                const matchedFare = farelistdata.find(
                    fare => (fare.pincode1 === pickupPostal && fare.pincode2 === dropPostal) ||
                        (fare.pincode1 === dropPostal && fare.pincode2 === pickupPostal)
                );
                console.log("matchedFare", matchedFare);
                if (!matchedFare) {
                    Alert.alert(t('alert'), t('booking_not_available_for_location'));
                    return;
                }
                console.log("matchedFare", matchedFare);
                matchedFareValue = matchedFare && matchedFare.fare ? parseFloat(matchedFare.fare) : null;
                console.log("matchedFareValue", matchedFareValue);
            }
            console.log("formattedTripCost3");
        }
        console.log("formattedTripCost2");
        const updatedPickup = (() => {
            const hasEdit = editingIndex !== null && tripdata.pickup;
            const lat = hasEdit && typeof tripdata.pickup.lat === 'number' ? tripdata.pickup.lat : (item.pickup && typeof item.pickup.lat === 'number' ? item.pickup.lat : undefined);
            const lng = hasEdit && typeof tripdata.pickup.lng === 'number' ? tripdata.pickup.lng : (item.pickup && typeof item.pickup.lng === 'number' ? item.pickup.lng : undefined);
            return {
                ...item.pickup,
                add: editedItem.pickup.add,
                lat,
                lng
            };
        })();

        const updatedDrop = (() => {
            const hasEdit = editingIndex !== null && tripdata.drop;
            const lat = hasEdit && typeof tripdata.drop.lat === 'number' ? tripdata.drop.lat : (item.drop && typeof item.drop.lat === 'number' ? item.drop.lat : undefined);
            const lng = hasEdit && typeof tripdata.drop.lng === 'number' ? tripdata.drop.lng : (item.drop && typeof item.drop.lng === 'number' ? item.drop.lng : undefined);
            return {
                ...item.drop,
                add: editedItem.drop.add,
                lat,
                lng
            };
        })();
        console.log("formattedTripCost4", matchedFareValue);
        const decimals = Number.isFinite(settings?.decimal) ? settings.decimal : 2;
        const baseCostRaw = matchedFareValue ?? (editedItem?.trip_cost ?? item.trip_cost ?? 0);
        const baseCostNum = parseFloat(baseCostRaw) || 0;
        const formattedTripCost = baseCostNum.toFixed(decimals);
        console.log("formattedTripCost5");
        const updatedBooking = {
            ...item,
            pickup: updatedPickup,
            drop: updatedDrop,
            pickupAddress: tripdata.pickup && editingIndex !== null ? tripdata.pickup.add : item.pickupAddress,
            dropAddress: tripdata.drop && editingIndex !== null ? tripdata.drop.add : item.dropAddress,
            coords: (() => {
                const havePickupCoords = typeof updatedPickup.lat === 'number' && typeof updatedPickup.lng === 'number';
                const haveDropCoords = typeof updatedDrop.lat === 'number' && typeof updatedDrop.lng === 'number';
                if (editingIndex !== null && havePickupCoords && haveDropCoords) {
                    return [
                        { latitude: updatedPickup.lat, longitude: updatedPickup.lng },
                        { latitude: updatedDrop.lat, longitude: updatedDrop.lng }
                    ];
                }
                return item.coords;
            })(),
            trip_cost: String(formattedTripCost),
            flightNumber: editedItem.flightNumber,
            tripdate: editedItem.tripdate || item.tripdate,
            returnFlightNumber: editedItem.returnFlightNumber !== undefined ? editedItem.returnFlightNumber : item.returnFlightNumber || '',
            noteText: editedItem.noteText || item.noteText || '', // Add noteText to updated booking
            carType: editedItem.carType || item.carType || '', // Add carType to updated booking
            otherPerson: editedItem.otherPerson || item.otherPerson || '',
            otherPersonPhone: editedItem.otherPersonPhone || item.otherPersonPhone || ''
        };
        console.log("updatedBooking", updatedBooking);
        dispatch(updateBooking(updatedBooking));
        setEditingIndex(null);
        setEditedItem({});
    };

    const tapAddress = (selection, item) => {
        // Use navigation from props if available
        const navigation = props.navigation;
        console.log("navigation", navigation)
        let savedAddresses = [];
        let allAddresses = auth.profile && auth.profile.savedAddresses ? auth.profile.savedAddresses : {};
        for (let key in allAddresses) {
            savedAddresses.push(allAddresses[key]);
        }
        if (selection === 'drop') {
            navigation.navigate('Search', { locationType: "drop", addParam: savedAddresses, item });
        } else {
            navigation.navigate('Search', { locationType: "pickup", addParam: savedAddresses, item });
        }
    };

    const renderData = ({ item, index }) => {
        const isCustomer = auth && auth.profile && auth.profile.usertype === 'customer';
        const canEdit = tabIndex === 0 && isCustomer && item.status !== 'STARTED';
        const isEditing = editingIndex === index && canEdit;
        const isWithinTwoHours = item.tripdate && (moment(item.tripdate).diff(moment(), 'hours') < 2);
        const isPutOutTab = tabIndex === 3 && auth.profile && auth.profile.usertype === 'driver';

        // Find the putout reason for the current driver
        let putoutReason = '';
        if (isPutOutTab && Array.isArray(item.putoutdrivers)) {
            const driverPutout = item.putoutdrivers.find(
                driver => driver.driver === auth.profile.uid
            );
            if (driverPutout) {
                putoutReason = driverPutout.putout_res;
            }
        }

        return (
            <TouchableOpacity activeOpacity={0.8} onPress={() => onPressButton(item, index)} style={[styles.BookingContainer, styles.elevation]} >

<View style={[styles.box, { padding: 15, backgroundColor: getStatusBgColor(item.status) },]}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8, justifyContent: 'space-between' }}>
                        {/* Reference display */}
                        {item.reference ? (
                            <Text style={{ fontSize: 14, color: '#888', marginRight: 10 }}>
                                {t('booking_ref')}: {item.reference}
                            </Text>
                        ) : null}
                        {/* Round trip icon */}
                        {(item.roundTrip && item.roundTrip !== 'false') ? (
                            <MaterialCommunityIcons
                                name="repeat"
                                size={22}
                                color="#007AFF"
                                style={{ marginLeft: 4 }}
                                accessibilityLabel="Round Trip"
                            />
                        ) : null}
                    </View>
                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1 }}>
                        <View style={{ flexDirection: 'column', flex: 1 }}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                <View style={{ width: 30, alignItems: 'center' }}>
                                    <Ionicons name="location-outline" size={24} color={colors.BALANCE_GREEN} />
                                    <View style={[styles.hbox2, { flex: 1, minHeight: 5 }]} />
                                </View>
                                <View style={{ flex: 1, marginBottom: 10 }}>
                                    {isEditing ? (
                                        <TouchableOpacity onPress={() => tapAddress('pickup', item)}>
                                            <TextInput
                                                style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                                value={editedItem.pickup && editedItem.pickup.add ? editedItem.pickup.add : ''}
                                                editable={false}
                                                pointerEvents="none"
                                            />
                                        </TouchableOpacity>
                                    ) : (
                                        <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{item.pickup.add} </Text>
                                    )}
                                </View>
                            </View>

                            {item && item.waypoints && item.waypoints.length > 0 ?
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <View style={{ width: 30, alignItems: 'center' }}>
                                        <Ionicons name="location-outline" size={24} color={colors.BOX_BG} />

                                        <View style={[styles.hbox2, { flex: 1, minHeight: 5 }]} />
                                    </View>
                                    <View style={{ flex: 1, marginBottom: 10 }}>
                                        <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{item.waypoints.length} {t('stops')}</Text>
                                    </View>
                                </View>
                                : null}

                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                <View style={{ width: 30, alignItems: 'center' }}>
                                    <Ionicons name="location-outline" size={24} color={colors.BUTTON_ORANGE} />
                                </View>
                                <View style={{ flex: 1, marginBottom: 10 }}>
                                    {isEditing ? (
                                        <TouchableOpacity onPress={() => tapAddress('drop', item)}>
                                            <TextInput
                                                style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                                value={editedItem.drop && editedItem.drop.add ? editedItem.drop.add : ''}
                                                editable={false}
                                                pointerEvents="none"
                                            />
                                        </TouchableOpacity>
                                    ) : (
                                        <Text style={[styles.textStyle, isRTL ? { marginRight: 6, textAlign: 'right' } : { marginLeft: 6, textAlign: 'left' }]}>{item.drop.add}</Text>
                                    )}
                                </View>
                            </View>

                            {/* Flight number fields: show only the correct one for each trip */}
                            {tabIndex === 0 && (
                                <>
                                    {/* Onward trip: show flightNumber only */}
                                    {(!item.isReturnTrip && item.flightNumber) ? (
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 10 }}>
                                            {isEditing ? (
                                                <>
                                                    <Text style={[styles.textStyle, { width: 100 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>Flight Number:</Text>
                                                    <TextInput
                                                        style={[styles.textStyle, { flex: 1, borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                                        value={editedItem.flightNumber}
                                                        onChangeText={text => setEditedItem({ ...editedItem, flightNumber: text })}
                                                        placeholder="Enter flight number"
                                                    />
                                                </>
                                            ) : (
                                                <></>
                                                // <Text style={[styles.textStyle, { flex: 1 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>{item.flightNumber || '-'}</Text>
                                            )}
                                        </View>
                                    ) : null}
                                    {/* Return trip: show returnFlightNumber only */}
                                    {(item.isReturnTrip && item.returnFlightNumber) ? (
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', marginBottom: 10 }}>
                                            {isEditing ? (
                                                <>
                                                    <Text style={[styles.textStyle, { width: 150 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>Return Flight Number:</Text>
                                                    <TextInput
                                                        style={[styles.textStyle, { flex: 1, borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                                        value={editedItem.returnFlightNumber}
                                                        onChangeText={text => setEditedItem({ ...editedItem, returnFlightNumber: text })}
                                                        placeholder="Enter return flight number"
                                                    />
                                                </>
                                            ) : (
                                                <></>
                                                // <Text style={[styles.textStyle, { flex: 1 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>{item.returnFlightNumber}</Text>
                                            )}
                                        </View>
                                    ) : null}
                                </>
                            )}

                            {/* Note text - show in all tabs */}
                            {item.flightNumber ? (
                                <>
                                    {isEditing ? (
                                        <>
                                            <Text style={{ fontSize: 13, color: '#888', fontWeight: '500' }}>
                                                <TextInput
                                                    style={[styles.textStyle, { flex: 1, borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 4 }]}
                                                    value={editedItem.flightNumber}
                                                    onChangeText={text => setEditedItem({ ...editedItem, flightNumber: text })}
                                                    placeholder="Enter flight number"
                                                />
                                            </Text>
                                        </>
                                    ) : (
                                        <Text style={{ fontSize: 13, color: '#888', fontWeight: '500' }}>
                                            Flight: {item.flightNumber}
                                        </Text>
                                    )}
                                </>

                            ) : null}

                            {/* Note field - editable when in edit mode */}
                            {isEditing ? (
                                <View style={{ marginBottom: 10 }}>
                                    <Text style={[styles.textStyle, { marginBottom: 5 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>
                                        Note:
                                    </Text>
                                    <TextInput
                                        style={[styles.textStyle, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 8, minHeight: 40 }]}
                                        value={editedItem.noteText || ''}
                                        onChangeText={text => setEditedItem({ ...editedItem, noteText: text })}
                                        placeholder="Enter note"
                                        multiline={true}
                                        numberOfLines={2}
                                    />
                                </View>
                            ) : item.noteText ? (
                                <Text style={{ fontSize: 13, color: '#888', fontWeight: '500' }}>
                                    Note: {item.noteText}
                                </Text>
                            ) : null}

                            {/* Car Type field - editable when in edit mode */}
                            {isEditing ? (
                                <View style={{ marginBottom: 10 }}>
                                    <Text style={[styles.textStyle, { marginBottom: 5 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>
                                        Car Type:
                                    </Text>
                                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginVertical: 5 }}>
                                        {cartypes && cartypes.cars ? cartypes.cars.map((car, idx) => (
                                            <TouchableOpacity
                                                key={car.name}
                                                style={{
                                                    padding: 8,
                                                    marginRight: 8,
                                                    borderWidth: editedItem.carType === car.name ? 2 : 1,
                                                    borderColor: editedItem.carType === car.name ? MAIN_COLOR : '#ccc',
                                                    borderRadius: 5,
                                                    backgroundColor: editedItem.carType === car.name ? '#f0f8ff' : colors.WHITE
                                                }}
                                                onPress={() => setEditedItem({ ...editedItem, carType: car.name })}
                                            >
                                                <Text style={{
                                                    fontFamily: fonts.Medium,
                                                    fontSize: 12,
                                                    color: editedItem.carType === car.name ? MAIN_COLOR : '#666'
                                                }}>
                                                    {car.name}
                                                </Text>
                                            </TouchableOpacity>
                                        )) : (
                                            <TextInput
                                                style={[styles.textStyle, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 8, minWidth: 150 }]}
                                                value={editedItem.carType || ''}
                                                onChangeText={text => setEditedItem({ ...editedItem, carType: text })}
                                                placeholder="Enter car type"
                                            />
                                        )}
                                    </ScrollView>
                                </View>
                            ) : item.carType ? (
                                <Text style={{ fontSize: 13, color: '#888', fontWeight: '500' }}>
                                    Car Type: {item.carType}
                                </Text>
                            ) : null}
                        </View>
                    </View>

                    <View style={{ flexDirection: 'column', flex: 1, minHeight: 60 }}>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, minHeight: 60 }}>
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Text style={{ fontFamily: fonts.Bold, fontSize: 24, color: MAIN_COLOR, opacity: 0.8 }}>{settings.symbol}</Text>
                                <Text style={styles.textStyleBold}>
                                    {(() => {
                                        // Defensive: parseFloat and .toFixed only on numbers, and tips may be string or undefined
                                        let tips = 0;
                                        // Only add tips if status is not 'PENDING'
                                        if (item && item.tips && item.status && item.status.toLowerCase() !== 'pending') {
                                            tips = parseFloat(item.tips);
                                        }
                                        if (item && item.trip_cost > 0) {
                                            const tripCost = parseFloat(item.trip_cost) || 0;
                                            return (tripCost + tips).toFixed(settings.decimal);
                                        } else if (item && item.estimate) {
                                            const estimate = parseFloat(item.estimate) || 0;
                                            return (estimate + tips).toFixed(settings.decimal);
                                        } else {
                                            return tips.toFixed(settings.decimal);
                                        }
                                    })()}
                                </Text>
                            </View>
                            <View style={[styles.hbox2, { minHeight: 5, width: 1, margin: 2 }]} />
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                <Fontisto name="map" size={26} color={MAIN_COLOR} style={{ opacity: 0.8 }} />
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    <Text style={styles.textStyleBold}>{item && item.distance > 0 ? parseFloat(item.distance).toFixed(settings.decimal) : 0}</Text>
                                    <Text style={styles.textStyle}> {settings && settings.convert_to_mile ? t("mile") : t("km")} </Text>
                                </View>
                            </View>
                        </View>
                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', flex: 1, minHeight: 60 }}>
                            <View style={[styles.details, { flexDirection: isRTL ? 'row-reverse' : 'row', borderBottomWidth: 0 }]}>
                                <Ionicons name="calendar-outline" size={26} color={MAIN_COLOR} style={{ opacity: 0.8 }} />
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                    {isEditing ? (
                                        <TouchableOpacity onPress={() => setOpenDatePickerIndex(index)}>
                                            <Text style={styles.tripdate}>
                                                {editedItem.tripdate
                                                    ? `${moment(editedItem.tripdate).format('MM/DD/YYYY')} ${moment(editedItem.tripdate).format('hh:mm A')}`
                                                    : t('select_date')}
                                            </Text>
                                        </TouchableOpacity>
                                    ) : (
                                        <Text style={styles.textStyleBold}>
                                            {item && item.tripdate
                                                ? `${moment(item.tripdate).format('MM/DD/YYYY')}
${moment(item.tripdate).format('hh:mm A')}`
                                                : item && item.startTime
                                                    ? `${moment(item.startTime).format('MM/DD/YYYY')} ${moment(item.startTime).format('hh:mm A')}`
                                                    : '-'}
                                        </Text>
                                    )}
                                    {isEditing && openDatePickerIndex === index && (
                                        <DatePicker
                                            modal
                                            open={openDatePickerIndex === index}
                                            date={editedItem.tripdate ? new Date(editedItem.tripdate) : new Date()}
                                            onConfirm={date => {
                                                setOpenDatePickerIndex(null);
                                                setEditedItem({ ...editedItem, tripdate: date });
                                            }}
                                            onCancel={() => setOpenDatePickerIndex(null)}
                                            minimumDate={new Date()}
                                            theme='light'
                                        />
                                    )}
                                </View>
                            </View>
                            <View style={[styles.hbox2, { minHeight: 5, width: 1, margin: 2 }]} />
                            <View style={[styles.clock, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                {item && item.trip_start_time && item.trip_end_time ?
                                    <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                        <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                            <Ionicons name="location-outline" size={28} color={colors.BALANCE_GREEN} />
                                            <View>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))).length == 2 ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : "0" + (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : ""}</Text>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))).length == 2 ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":")), ((item.trip_start_time).lastIndexOf(":"))) : ":0" + (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))) : ""}</Text>
                                                </View>
                                                <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{item.startTime ? moment(item.startTime).format('ll') : ''}</Text>
                                            </View>
                                        </View>
                                        <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                            <Ionicons name="location-outline" size={28} color={colors.BUTTON_ORANGE} />
                                            <View>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_end_time ? (item.trip_end_time).substring(0, ((item.trip_end_time).indexOf(":"))).length == 2 ? (item.trip_end_time).substring(0, ((item.trip_end_time).indexOf(":"))) : "0" + (item.trip_end_time).substring(0, ((item.trip_end_time).indexOf(":"))) : ""}</Text>
                                                    <Text style={styles.textStyleBold}>{item && item.trip_end_time ? (item.trip_end_time).substring(((item.trip_end_time).indexOf(":") + 1), ((item.trip_end_time).lastIndexOf(":"))).length == 2 ? (item.trip_end_time).substring(((item.trip_end_time).indexOf(":")), ((item.trip_end_time).lastIndexOf(":"))) : ":0" + (item.trip_end_time).substring(((item.trip_end_time).indexOf(":") + 1), ((item.trip_end_time).lastIndexOf(":"))) : ""}</Text>
                                                </View>
                                                <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{item.endTime ? moment(item.endTime).format('ll') : ''}</Text>
                                            </View>
                                        </View>
                                    </View>
                                    : item && item.trip_start_time ?
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                            <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                                <Ionicons name="location-outline" size={28} color={colors.BALANCE_GREEN} />
                                                <View>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))).length == 2 ? (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : "0" + (item.trip_start_time).substring(0, ((item.trip_start_time).indexOf(":"))) : ""}</Text>
                                                        <Text style={styles.textStyleBold}>{item && item.trip_start_time ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))).length == 2 ? (item.trip_start_time).substring(((item.trip_start_time).indexOf(":")), ((item.trip_start_time).lastIndexOf(":"))) : ":0" + (item.trip_start_time).substring(((item.trip_start_time).indexOf(":") + 1), ((item.trip_start_time).lastIndexOf(":"))) : ""}</Text>
                                                    </View>
                                                    <Text style={{ textAlign: isRTL ? "right" : "left", fontSize: 8 }}>{item.startTime ? moment(item.startTime).format('ll') : ''}</Text>
                                                </View>
                                            </View>
                                            <View style={[styles.section, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                                                <Ionicons name="location-outline" size={28} color={colors.BUTTON_ORANGE} />
                                                <Image source={require('../../assets/images/clock.gif')} style={{ width: 25, height: 25, alignSelf: 'center', resizeMode: 'center' }} />
                                            </View>
                                        </View>
                                        :
                                        <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                                                {isPutOutTab ? (
                                                    <Text style={styles.textStyleBold}>
                                                        {putoutReason ? putoutReason : t('putout_no_reason')}
                                                    </Text>
                                                ) : (
                                                    <Text style={styles.textStyleBold}>
                                                        {item && item.reason ? item.reason : t(item.status).toUpperCase()}
                                                    </Text>
                                                )}
                                            </View>
                                        </View>
                                }
                            </View>
                        </View>
                    </View>

                    {item ?
                        <View style={[styles.driverDetails, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center', flex: 1 }}>
                                {item ?
                                    (!(item.driver_image == '' || item.driver_image == null || item.driver_image == 'undefined') && auth.profile.usertype == 'customer') ?
                                        <Avatar
                                            size="medium"
                                            rounded
                                            source={{ uri: item.driver_image }}
                                            activeOpacity={0.7}
                                        />
                                        :
                                        (!(item.customer_image == '' || item.customer_image == null || item.customer_image == 'undefined') && auth.profile.usertype == 'driver') ?
                                            <Avatar
                                                size="medium"
                                                rounded
                                                source={{ uri: item.customer_image }}
                                                activeOpacity={0.7}
                                            />
                                            : item.driver_name != '' ?

                                                <Avatar
                                                    size="medium"
                                                    rounded
                                                    source={require('../../assets/images/profilePic.png')}
                                                    activeOpacity={0.7}
                                                /> : null
                                    : null}
                                <View style={[styles.userView, { flex: 1, marginHorizontal: 5 }]}>
                                    {item && item.driver_name != '' && auth.profile.usertype == 'customer' ? <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{item.driver_name ? item.driver_name : t('no_name')}</Text> : null}

                                    {item && item.customer_name != '' && auth.profile.usertype == 'driver' ? <Text style={[styles.textStyleBold, { textAlign: isRTL ? 'right' : 'left' }]}>{item.customer_name ? item.customer_name : t('no_name')}</Text> : null}

                                                                       {/* ADDED: Show other person + phone if available / editable */}
                                                                       {isEditing ? (
                                        <View style={{ marginTop: 6 }}>
                                            <Text style={[styles.textStyle, { marginBottom: 4 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>
                                                Other Person:
                                            </Text>
                                            <TextInput
                                                style={[styles.textStyle, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 6, marginBottom: 8 }]}
                                                value={editedItem.otherPerson || ''}
                                                onChangeText={text => setEditedItem({ ...editedItem, otherPerson: text })}
                                                placeholder="Enter other person's name"
                                            />
                                            <Text style={[styles.textStyle, { marginBottom: 4 }, isRTL ? { textAlign: 'right' } : { textAlign: 'left' }]}>
                                                Other Person Phone:
                                            </Text>
                                            <TextInput
                                                style={[styles.textStyle, { borderWidth: 1, borderColor: '#ccc', borderRadius: 5, padding: 6 }]}
                                                value={editedItem.otherPersonPhone || ''}
                                                onChangeText={text => setEditedItem({ ...editedItem, otherPersonPhone: text })}
                                                placeholder="Enter phone number"
                                                keyboardType="phone-pad"
                                            />
                                        </View>
                                    ) : (item?.otherPerson || item?.otherPersonPhone) ? (
                                        <TouchableOpacity
                                            onPress={() => item.otherPersonPhone ? onPressCall(item.otherPersonPhone) : null}
                                            activeOpacity={item.otherPersonPhone ? 0.6 : 1}
                                        >
                                            <Text style={{ fontSize: 12, color: '#888', fontWeight: '500', textAlign: isRTL ? 'right' : 'left' }}>
                                                Other Person: {item.otherPerson || '-'}
                                            </Text>
                                        </TouchableOpacity>
                                    ) : null}

                                    {item && item.rating > 0 && item.driver_name && auth.profile.usertype == 'customer' ?
                                        <View>
                                            <StarRating
                                                maxStars={5}
                                                starSize={15}
                                                enableHalfStar={true}
                                                color={MAIN_COLOR}
                                                emptyColor={MAIN_COLOR}
                                                rating={item && item.rating ? parseFloat(item.rating) : 0}
                                                style={[styles.contStyle, isRTL ? { marginRight: 0, transform: [{ scaleX: -1 }] } : { marginLeft: -8 }]}
                                                onChange={() => {
                                                    //console.log('hello')
                                                }}
                                            />
                                        </View>
                                        : null}
                                </View>
                            </View>
                            {item && ((item.driver_contact || item.customer_contact)) && !(auth.profile && auth.profile.usertype === 'driver' && tabIndex === 3) ?
                                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', alignItems: 'center' }}>
                                    <TouchableOpacity onPress={() => (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(item.status) != -1) ? role == 'customer' ?
                                        onPressCall(item.driver_contact) : (item.otherPersonPhone && item.otherPersonPhone.length > 0 ? onPressCall(item.otherPersonPhone) : onPressCall(item.customer_contact)) : onAlert(item)}
                                        style={{ backgroundColor: MAIN_COLOR, height: 40, width: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', margin: 3 }}>
                                        <Ionicons name="call" size={24} color={colors.WHITE} />
                                    </TouchableOpacity>
                                    <TouchableOpacity onPress={() => (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(item.status) != -1) ? onChatAction(item, index) : onChatAlert(item)} style={{ backgroundColor: MAIN_COLOR, height: 40, width: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', margin: 3 }}>
                                        <Ionicons name="chatbubble-ellipses-sharp" size={24} color={colors.WHITE} />
                                    </TouchableOpacity>
                                </View>
                                : null}
                        </View>
                        : null}

                    {(item && item.status && auth.profile && auth.profile.uid &&
                        (((['PAYMENT_PENDING', 'NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED', 'PENDING'].indexOf(item.status) != -1) && auth.profile.usertype == 'customer') ||
                            ((['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED'].indexOf(item.status) != -1) && auth.profile.usertype == 'driver'))
                        && !(auth.profile && auth.profile.usertype === 'driver' && tabIndex === 3)
                    ) ? (
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginVertical: 10 }}>
                            <Button
                                title={item.status == 'PAID' ? t('add_to_review') : item.status == 'PAYMENT_PENDING' ? t('paynow_button') : t('go_to_booking')}
                                loading={false}
                                loadingColor={{ color: colors.GREEN }}
                                buttonStyle={[styles.textStyleBold, { color: colors.WHITE }]}
                                style={{ backgroundColor: MAIN_COLOR, flex: 1, marginRight: 8 }}
                                btnClick={() => { onPressAction(item, index) }}
                            />
                            {auth.profile.usertype === 'driver' && !isWithinTwoHours ? (
                                <Button
                                    title="Put Out"
                                    loading={false}
                                    loadingColor={{ color: colors.WHITE }}
                                    buttonStyle={[styles.textStyleBold, { color: colors.WHITE }]}
                                    style={{ backgroundColor: '#FF3B30', flex: 1 }}
                                    btnClick={() => {
                                        setPutOutCard(item);
                                        setPutOutModalVisible(true);
                                    }}
                                />
                            ) : auth.profile.usertype === 'driver' && isWithinTwoHours ? (
                                <Button
                                    title={'Contact'}
                                    buttonStyle={[styles.textStyleBold, { color: colors.WHITE }]}
                                    style={{ backgroundColor: '#888', flex: 1 }}
                                    btnClick={() => {
                                        onPressCall('+1669-899-8887');
                                    }}
                                />
                            ) : null}
                        </View>
                    ) : null}

                    <Modal
                        animationType="fade"
                        transparent={true}
                        visible={userInfoModalStatus}
                        onRequestClose={() => {
                            setUserInfoModalStatus(false);
                        }}
                    >
                        <View style={styles.centeredView}>
                            <View style={styles.modalView}>
                                <View style={{ width: '100%' }}>
                                    {item && item.deliveryPersonPhone ?
                                        <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                            <Text style={styles.textStyleBold}>{t('senderPersonPhone')}</Text>
                                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 10 }}>
                                                <Ionicons name="call" size={24} color={colors.BLACK} />
                                                <Text style={styles.textContent1} onPress={() => onPressCall(item.deliveryPersonPhone)}> {item.deliveryPersonPhone} </Text>
                                            </View>
                                        </View>
                                        : null}
                                    {item && item.customer_contact ?
                                        <View style={[styles.textContainerStyle, { alignItems: isRTL ? "flex-end" : "flex-start" }]}>
                                            <Text style={styles.textStyleBold}>{t('senderPersonPhone')}</Text>
                                            <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row', marginVertical: 10 }}>
                                                <Ionicons name="call" size={24} color={colors.BLACK} />
                                                <Text style={styles.textContent1} onPress={() => onPressCall(item.customer_contact)}> {item.customer_contact} </Text>
                                            </View>
                                        </View>
                                        : null}
                                </View>
                                <TouchableOpacity
                                    loading={false}
                                    onPress={() => setUserInfoModalStatus(false)}
                                    style={styles.modalBtn}
                                >
                                    <Text style={styles.textStyleBold}>{t('ok')}</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </Modal>
                    {canEdit && (
                        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10 }}>
                            {isEditing ? (
                                <TouchableOpacity onPress={() => handleSave(item, index)} style={{ backgroundColor: '#34C759', padding: 8, borderRadius: 6, marginLeft: 8 }}>
                                    <Text style={{ color: '#fff', fontWeight: 'bold' }}>Save</Text>
                                </TouchableOpacity>
                            ) : (
                                <TouchableOpacity onPress={() => handleEdit(item, index)} style={{ backgroundColor: '#007AFF', padding: 8, borderRadius: 6 }}>
                                    <Text style={{ color: '#fff', fontWeight: 'bold' }}>Edit</Text>
                                </TouchableOpacity>
                            )}
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        )
    }

        // Add sorting function for tripdate
        const sortByTripDate = (data, order = 'asc') => {
            const sorted = [...data].sort((a, b) => {
                const dateA = a.tripdate ? new Date(a.tripdate) : new Date(a.startTime || 0);
                const dateB = b.tripdate ? new Date(b.tripdate) : new Date(b.startTime || 0);
                return dateA - dateB; // Sort in ascending order (earliest first)
            });
            return order === 'desc' ? sorted.reverse() : sorted;
        };
    // Status color helper
    const getStatusBgColor = (status) => {
        const s = (status || '').toUpperCase();
        switch (s) {
            case 'PAYMENT_PENDING': return '#FFF4E5'; // light orange
            case 'NEW': return '#FFFDE7'; // light yellow
            case 'ACCEPTED': return '#E6F7FF'; // light blue
            case 'ARRIVED': return '#E8F5E9'; // light green
            case 'STARTED': return '#E3F2FD'; // light blue 2
            case 'REACHED': return '#F3E5F5'; // light purple
            case 'PENDING': return '#FFFDE7'; // light yellow
            case 'COMPLETE': return '#F1F8E9'; // pale green
            case 'CANCELLED': return '#FFEBEE'; // light red
            default: return colors.WHITE;
        }
    };
    // Airport header color helper
    const getAirportHeaderColor = (pickupAdd) => {
        if (!pickupAdd) return '#F6F3FF';
        const a = (pickupAdd || '').toLowerCase();
        if (a.includes("o'hare") || a.includes('ohare') || a.includes('ord')) return '#E6F7FF';
        if (a.includes('midway') || a.includes('mdw')) return '#FFF5E6';
        return '#F6F3FF';
    };
    // Fetch assigned rides from Redux store
    const assignedCards = tasks ? sortByTripDate(tasks.filter(item => item.status === 'NEW')) : [];
    const assignedCount = assignedCards.length;
    const activeBookingCount = props.data
        ? props.data.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')).length
        : 0;

    // Update tab values to have the correct order: active_ride, assigned_ride, complete, put_out
    let tabValues;
    if (auth.profile && auth.profile.usertype === 'driver') {
        const assignedCount = assignedCards.length;
        tabValues = [
            t('active_ride'),
            assignedCount > 0 ? `${t('ASSIGNED_RIDE')} (${assignedCount})` : t('ASSIGNED_RIDE'),
            t('COMPLETE'),
            t('PUT_OUT')
        ];
    } else {
        tabValues = [
            t('active_booking'),
            t('COMPLETE'),
            t('CANCELLED'),
        ];
    }

    // Calculate active rides count at component level
    const activeRides = props.data ? props.data.filter(item =>
        !(item.status === 'CANCELLED' || item.status === 'COMPLETE')
    ) : [];
    const activeRidesCount = activeRides.length;

    /**
     * props.onPressAccept(card, idx) - optional callback for when a ride is accepted.
     * If not provided, will dispatch acceptTask(card) by default.
     */
    const handleAcceptAssignedRide = (card, idx) => {
        // Check if driver is active
        if (!auth.profile.driverActiveStatus) {
            Alert.alert(
                'Driver Not Active',
                'You need to be active to accept rides. Please go to Home and activate your status.',
                [
                    {
                        text: 'OK'
                    },
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    }
                ]
            );
            return;
        }

        // Check if the assigned ride is more than 1 hour away
        const isMoreThan1Hour = card.tripdate && (moment(card.tripdate).diff(moment(), 'hours') > 1);

        // Calculate hours until trip
        const hoursUntilTrip = card.tripdate ? moment(card.tripdate).diff(moment(), 'hours') : 0;

        // Check if the assigned ride is less than 60 minutes away
        const isLessThan60Min = card.tripdate && (moment(card.tripdate).diff(moment(), 'minutes') < 60);

        // If trip is more than 1 hour away, show alert with hours info
        if (isMoreThan1Hour) {
            Alert.alert(
                'Cannot Accept Ride',
                `This ride is ${hoursUntilTrip} hours away. You cannot accept rides more than 1 hour in advance.`,
                [{ text: 'OK' }]
            );
            return;
        }

        // If driver has active rides and the new ride is less than 60 minutes away, show confirmation
        if (activeRidesCount > 0 && isLessThan60Min) {
            Alert.alert(
                'Confirm Ride Acceptance',
                'You already have an active ride. Are you sure you want to accept this ride that is less than 60 minutes away?',
                [
                    {
                        text: 'Cancel',
                        style: 'cancel'
                    },
                    {
                        text: 'Accept',
                        onPress: () => {
                            const cardWithDevice = { ...card, driverDeviceId: deviceId };
                            if (props.onPressAccept) {
                                props.onPressAccept(cardWithDevice, idx);
                            } else {
                                dispatch(acceptTask(cardWithDevice));
                                Alert.alert('Accepted', `Accepted ride: ${card.pickup && card.pickup.add} → ${card.drop && card.drop.add}`);
                            }
                        }
                    }
                ]
            );
        } else {
            // Normal acceptance flow
            const cardWithDevice = { ...card, driverDeviceId: deviceId };
            if (props.onPressAccept) {
                props.onPressAccept(cardWithDevice, idx);
            } else {
                dispatch(acceptTask(cardWithDevice));
                Alert.alert('Accepted', `Accepted ride: ${card.pickup && card.pickup.add} → ${card.drop && card.drop.add}`);
            }
        }
    };

    const handleActiveBookingPutOut = (booking, reason) => {
        if (!booking || !auth.profile) return;
        const userId = auth.profile.uid;

        // Clone the booking object
        console.log("updatedBooking", booking);
        const updatedBooking = { ...booking };
        console.log("updatedBooking 1", updatedBooking);
        // Add to putoutdrivers
        if (!updatedBooking.putoutdrivers) updatedBooking.putoutdrivers = [];
        updatedBooking.putoutdrivers.push({
            driver: userId,
            putout_res: reason,
            driver_name: auth.profile.firstName + ' ' + auth.profile.lastName,
            driver_contact: auth.profile.mobile,
            driver_image: auth.profile.profile_image || '',
            driverDeviceId: auth.profile.deviceId || '',
            driver_token: auth.profile.pushToken || '',
            driverRating: auth.profile.rating || '',
            driver_share: updatedBooking.driver_share || '',
        });

        // Remove driver assignment fields
        delete updatedBooking.driver;
        delete updatedBooking.driver_name;
        delete updatedBooking.driver_contact;
        delete updatedBooking.driver_image;
        delete updatedBooking.driverDeviceId;
        delete updatedBooking.driver_token;
        delete updatedBooking.driverRating;
        delete updatedBooking.fleetadmin;
        delete updatedBooking.fleetCommission;
        delete updatedBooking.car_image;
        delete updatedBooking.vehicle_number;
        delete updatedBooking.vehicleModel;
        delete updatedBooking.vehicleMake;

        // Set status to 'NEW'
        updatedBooking.status = 'NEW';

        // Update the booking
        console.log("requestedDrivers", updatedBooking);
        dispatch(updateBooking(updatedBooking));

        // Update the user's queue property to false
        dispatch(updateProfile({ queue: false }));

        setPutOutModalVisible(false);
        setPutOutReason('');
        setPutOutCard(null);
        Alert.alert('Put Out', 'You have been put out from this booking.');
    };

    const handlePutOutConfirm = async () => {
        if (!putOutCard) return;
        // If active booking (assigned to driver)
        if (['ACCEPTED', 'ARRIVED', 'STARTED', 'REACHED'].includes(putOutCard.status)) {
            handleActiveBookingPutOut(putOutCard, putOutReason);
        } else {
            const userId = auth.profile.uid;
            const updatedBooking = { ...putOutCard };
            if (updatedBooking.requestedDrivers && userId) {
                updatedBooking.requestedDrivers = { ...updatedBooking.requestedDrivers, [userId]: false };
            }
            if (!updatedBooking.putoutdrivers) updatedBooking.putoutdrivers = [];
            updatedBooking.putoutdrivers.push({
                driver: userId,
                putout_res: putOutReason,
                ...auth.profile
            });
            dispatch(updateBooking(updatedBooking));
            setPutOutModalVisible(false);
            setPutOutReason('');
            setPutOutCard(null);
            Alert.alert('Put Out', 'You have been put out from this booking.');
        }
    };

    const renderAssignedCard = () => (
        assignedCards.length === 0 ? (
            <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                <View style={styles.emptyBox}>
                    <Text style={styles.emptyText}>{t('no_data_available')}</Text>
                </View>
            </View>
        ) : (
            <ScrollView
                style={{ flex: 1 }}
                contentContainerStyle={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingVertical: 10,
                }}
            >
                               {assignedCards.map((card, idx) => (
                    <View
                        key={card.id || idx}
                        style={{
                            backgroundColor: getStatusBgColor(card.status),
                            borderRadius: 18,
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.10,
                            shadowRadius: 8,
                            elevation: 4,
                            marginVertical: 14,
                            width: '90%',
                            maxWidth: 500,
                            alignSelf: 'center',
                            borderWidth: 1,
                            borderColor: '#f0f0f0',
                        }}
                    >
                        {/* Header: Date, Price, Badges */}
                        <View style={{
                            backgroundColor: getAirportHeaderColor(card && card.pickup && card.pickup.add),
                            borderTopLeftRadius: 18,
                            borderTopRightRadius: 18,
                            padding: 16,
                            alignItems: 'center',
                            borderBottomWidth: 1,
                            borderBottomColor: '#eee',
                        }}>
                            <Text style={{ fontSize: 16, color: '#6C63FF', fontWeight: 'bold', marginBottom: 2 }}>
                                {card.tripdate ? moment(card.tripdate).format('ddd, MMM D, YYYY • hh:mm A') : ''}
                            </Text>
                            <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#222', marginBottom: 6 }}>
                                {card.trip_cost
                                    ? `$${(parseFloat(card.trip_cost) + (card.tips ? parseFloat(card.tips) : 0)).toFixed(2)}`
                                    : ''}
                            </Text>
                            {card.tips && parseFloat(card.tips) > 0 ? (
                                <Text style={{ fontSize: 14, color: '#34C759', fontWeight: '600', marginBottom: 2 }}>
                                    + {t('Tips')}: ${parseFloat(card.tips).toFixed(2)}
                                </Text>
                            ) : null}
                            <View style={{ flexDirection: 'row', gap: 8, marginTop: 4 }}>
                                {card.reference ? (
                                    <View style={{
                                        backgroundColor: '#E0E7FF',
                                        borderRadius: 8,
                                        paddingHorizontal: 8,
                                        paddingVertical: 2,
                                        marginRight: 4,
                                    }}>
                                        <Text style={{ fontSize: 12, color: '#6C63FF', fontWeight: 'bold' }}>
                                            Ref: {card.reference}
                                        </Text>
                                    </View>
                                ) : null}
                                {card.carType ? (
                                    <View style={{
                                        backgroundColor: '#E0F7FA',
                                        borderRadius: 8,
                                        paddingHorizontal: 8,
                                        paddingVertical: 2,
                                    }}>
                                        <Text style={{ fontSize: 12, color: '#0097A7', fontWeight: 'bold' }}>
                                            {card.carType}
                                        </Text>
                                    </View>
                                ) : null}
                            </View>
                        </View>

                        {/* Route: Pickup & Drop */}
                        <View style={{ flexDirection: 'row', alignItems: 'flex-start', padding: 18, paddingBottom: 0 }}>
                            <View style={{ alignItems: 'center', marginRight: 12 }}>
                                <Ionicons name="location-sharp" size={24} color="#4CAF50" />
                                <View style={{ width: 2, height: 32, backgroundColor: '#E0E0E0', marginVertical: 2 }} />
                                <Ionicons name="flag" size={22} color="#FF7043" />
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{ fontSize: 15, color: '#222', fontWeight: '600', marginBottom: 8 }}>
                                    {card.pickup && card.pickup.add}
                                </Text>
                                <Text style={{ fontSize: 15, color: '#222', fontWeight: '600' }}>
                                    {card.drop && card.drop.add}
                                </Text>
                            </View>
                        </View>

                        {/* Trip Info: Distance, Duration, Payment */}
                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            paddingHorizontal: 18,
                            paddingTop: 12,
                            paddingBottom: 8,
                        }}>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}>
                                <MaterialCommunityIcons name="map-marker-distance" size={20} color="#6C63FF" />
                                <Text style={{ fontSize: 15, color: '#444', fontWeight: '500' }}>
                                    {card.distance ? `${parseFloat(card.distance).toFixed(2)} mi` : ''}
                                </Text>
                            </View>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}>
                                <MaterialCommunityIcons name="clock-outline" size={20} color="#6C63FF" />
                                <Text style={{ fontSize: 15, color: '#444', fontWeight: '500' }}>
                                    {card.estimateTime ? `${Math.round(card.estimateTime / 60)} mins` : ''}
                                </Text>
                            </View>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}>
                                <FontAwesome name="credit-card" size={18} color="#6C63FF" />
                                <Text style={{ fontSize: 15, color: '#444', fontWeight: '500' }}>
                                    {card.payment_mode ? card.payment_mode.toUpperCase() : ''}
                                </Text>
                            </View>
                        </View>

                        {/* Customer & Flight Info */}
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            paddingHorizontal: 18,
                            paddingBottom: 10,
                            paddingTop: 2,
                        }}>
                            {/* Avatar or initials */}
                            <View style={{
                                width: 40,
                                height: 40,
                                borderRadius: 20,
                                backgroundColor: '#E0E7FF',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: 10,
                            }}>
                                {card.customer_image ? (
                                    <Image
                                        source={{ uri: card.customer_image }}
                                        style={{ width: 40, height: 40, borderRadius: 20 }}
                                    />
                                ) : (
                                    <Text style={{ fontSize: 18, color: '#6C63FF', fontWeight: 'bold' }}>
                                        {card.customer_name ? card.customer_name.split(' ').map(n => n[0]).join('').toUpperCase() : '?'}
                                    </Text>
                                )}
                            </View>
                            <View style={{ flex: 1 }}>
                                <Text style={{ fontSize: 15, color: '#222', fontWeight: '600' }}>
                                    {card.customer_name}
                                </Text>
                                {/* ADDED: Show other person + phone if available */}
                                {(card?.otherPerson || card?.otherPersonPhone) ? (
                                        <Text style={{ fontSize: 13, color: '#888', fontWeight: '500' }}>
                                            Other Person: {card.otherPerson || '-'}
                                        </Text>
                                ) : null}
                                {card.flightNumber ? (
                                    <Text style={{ fontSize: 13, color: '#888', fontWeight: '500' }}>
                                        Flight: {card.flightNumber}
                                    </Text>
                                ) : null}
                                {card.noteText ? (
                                    <View style={{ paddingHorizontal: 18, paddingTop: 8 }}>
                                        <Text style={{ fontSize: 13, color: '#666', fontStyle: 'italic' }}>
                                            Note: {card.noteText}
                                        </Text>
                                    </View>
                                ) : null}

                            </View>
                        </View>

                        {/* Action Buttons */}
                        <View style={{
                            flexDirection: 'row',
                            paddingHorizontal: 18,
                            paddingBottom: 18,
                            gap: 10,
                        }}>
                            <TouchableOpacity
                                style={{
                                    flex: 1,
                                    backgroundColor: '#FF3B30',
                                    paddingVertical: 12,
                                    borderRadius: 10,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                                onPress={() => {
                                    setPutOutCard(card);
                                    setPutOutModalVisible(true);
                                }}
                                activeOpacity={0.8}
                            >
                                <Text style={{ color: '#FFFFFF', fontSize: 16, fontWeight: 'bold', letterSpacing: 1 }}>
                                    PUT OUT
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={{
                                    flex: 1,
                                    backgroundColor: (activeRidesCount > 1 || (card.tripdate && moment(card.tripdate).diff(moment(), 'hours') > 2)) ? '#CCCCCC' : '#34C759',
                                    paddingVertical: 12,
                                    borderRadius: 10,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                                onPress={() => {
                                    // Check if driver is active first
                                    if (!auth.profile.driverActiveStatus) {
                                        Alert.alert(
                                            'Driver Not Active',
                                            'You need to be active to accept rides. Please go to Home and activate your status.',
                                            [
                                                {
                                                    text: 'OK'
                                                },
                                                {
                                                    text: 'Cancel',
                                                    style: 'cancel'
                                                }
                                            ]
                                        );
                                        return;
                                    }

                                    // Check if trip is more than 1 hour away
                                    const isMoreThan1Hour = card.tripdate && (moment(card.tripdate).diff(moment(), 'hours') > 1);

                                    // Calculate hours until trip
                                    const hoursUntilTrip = card.tripdate ? moment(card.tripdate).diff(moment(), 'hours') : 0;

                                    if (isMoreThan1Hour) {
                                        Alert.alert(
                                            'Cannot Accept Ride',
                                            `This ride is ${hoursUntilTrip} hours away. You cannot accept rides more than 1 hour in advance.`,
                                            [{ text: 'OK' }]
                                        );
                                    } else if (activeRidesCount > 1) {
                                        Alert.alert(
                                            'Maximum Active Rides Reached',
                                            'You already have more than 2 active rides. Please complete some rides before accepting new ones.',
                                            [{ text: 'OK' }]
                                        );
                                    } else {
                                        handleAcceptAssignedRide(card, idx);
                                    }
                                }}
                                activeOpacity={(activeRidesCount > 1 || (card.tripdate && moment(card.tripdate).diff(moment(), 'hours') > 1)) ? 1 : 0.8}
                            >
                                <Text style={{
                                    color: (activeRidesCount > 1 || (card.tripdate && moment(card.tripdate).diff(moment(), 'hours') > 1)) ? '#888888' : '#FFFFFF',
                                    fontSize: 16,
                                    fontWeight: 'bold',
                                    letterSpacing: 1
                                }}>
                                    ACCEPT
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                ))}
            </ScrollView>
        )
    );

    const renderPutOutCard = () => (
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ alignItems: 'center', paddingVertical: 20, paddingHorizontal: 10 }}>
            {/* First dummy card */}
            <View style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.15,
                shadowRadius: 6,
                elevation: 4,
                margin: 16,
                width: 340,
                padding: 0,
                alignSelf: 'center',
            }}>
                {/* Location Section */}
                <View style={{ marginBottom: 30, width: '100%', padding: 20, paddingBottom: 0 }}>
                    {/* Origin */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#48BB78' }} />
                            <View style={{ width: 2, height: 40, backgroundColor: '#E2E8F0', marginTop: 5 }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            O'Hare International Airport (ORD), West O'Hare Avenue, Chicago, IL 60666, USA
                        </Text>
                    </View>
                    {/* Destination */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#ED8936' }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            adidas Outlet Store Aurora, Chicago Premium Outlets, Premium Outlet Boulevard, Aurora, IL 60502, USA
                        </Text>
                    </View>
                </View>
                {/* Trip Details */}
                <View style={{ backgroundColor: 'white', borderRadius: 8, padding: 20, marginBottom: 30, elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.1, shadowRadius: 3, width: '100%' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <FontAwesome name="dollar" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>63.00</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="map" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>33.39 mi</Text>
                        </View>
                    </View>
                    <View style={{ height: 1, backgroundColor: '#E2E8F0', marginVertical: 20 }} />
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="access-time" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>40 mins</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flex: 1, alignItems: 'flex-end' }}>
                            <Text style={{ fontSize: 16, fontWeight: '700', color: '#2D3748', textAlign: 'right', lineHeight: 20 }}>DRIVER CANCELLED{"\n"}BOOKING</Text>
                        </View>
                    </View>
                </View>
                {/* Bottom Section */}
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%', padding: 20, paddingTop: 0 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <Image
                            source={{ uri: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/photo_6244658431936022129_y.jpg-0ifrxCvCJp9xUtwzKyCsNcxAY3Yo9r.jpeg' }}
                            style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                        />
                        <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748' }}>test data</Text>
                    </View>
                    <View style={{ flexDirection: 'row', gap: 15 }}>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center', marginRight: 10 }}>
                            <MaterialCommunityIcons name="phone" size={24} color="white" />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center' }}>
                            <MaterialCommunityIcons name="chat" size={24} color="white" />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
            {/* Second dummy card */}
            <View style={{
                backgroundColor: '#FFFFFF',
                borderRadius: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.15,
                shadowRadius: 6,
                elevation: 4,
                margin: 16,
                width: 340,
                padding: 0,
                alignSelf: 'center',
            }}>
                {/* Location Section */}
                <View style={{ marginBottom: 30, width: '100%', padding: 20, paddingBottom: 0 }}>
                    {/* Origin */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#48BB78' }} />
                            <View style={{ width: 2, height: 40, backgroundColor: '#E2E8F0', marginTop: 5 }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            Union Station, 225 S Canal St, Chicago, IL 60606, USA
                        </Text>
                    </View>
                    {/* Destination */}
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 20 }}>
                        <View style={{ alignItems: 'center', marginRight: 15 }}>
                            <View style={{ width: 20, height: 20, borderRadius: 10, borderWidth: 3, borderColor: 'white', backgroundColor: '#ED8936' }} />
                        </View>
                        <Text style={{ flex: 1, fontSize: 16, color: '#2D3748', lineHeight: 22 }}>
                            Willis Tower, 233 S Wacker Dr, Chicago, IL 60606, USA
                        </Text>
                    </View>
                </View>
                {/* Trip Details */}
                <View style={{ backgroundColor: 'white', borderRadius: 8, padding: 20, marginBottom: 30, elevation: 2, shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.1, shadowRadius: 3, width: '100%' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <FontAwesome name="dollar" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>28.50</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="map" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>7.2 mi</Text>
                        </View>
                    </View>
                    <View style={{ height: 1, backgroundColor: '#E2E8F0', marginVertical: 20 }} />
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                            <MaterialCommunityIcons name="access-time" size={24} color="#4A5568" />
                            <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748', marginLeft: 10 }}>15 mins</Text>
                        </View>
                        <View style={{ width: 1, height: 40, backgroundColor: '#E2E8F0', marginHorizontal: 20 }} />
                        <View style={{ flex: 1, alignItems: 'flex-end' }}>
                            <Text style={{ fontSize: 16, fontWeight: '700', color: '#2D3748', textAlign: 'right', lineHeight: 20 }}>DRIVER CANCELLED{"\n"}BOOKING</Text>
                        </View>
                    </View>
                </View>
                {/* Bottom Section */}
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%', padding: 20, paddingTop: 0 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                        <Image
                            source={{ uri: 'https://randomuser.me/api/portraits/men/32.jpg' }}
                            style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                        />
                        <Text style={{ fontSize: 18, fontWeight: '600', color: '#2D3748' }}>John Doe</Text>
                    </View>
                    <View style={{ flexDirection: 'row', gap: 15 }}>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center', marginRight: 10 }}>
                            <MaterialCommunityIcons name="phone" size={24} color="white" />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ width: 50, height: 50, borderRadius: 25, backgroundColor: '#1A202C', alignItems: 'center', justifyContent: 'center' }}>
                            <MaterialCommunityIcons name="chat" size={24} color="white" />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </ScrollView>
    );

    const driverId = auth.profile && auth.profile.uid;
    const putOutBookings = props.data
        ? props.data.filter(
            booking =>
                Array.isArray(booking.putoutdrivers) &&
                booking.putoutdrivers.some(driver => driver.driver === driverId)
        )
        : [];

    useEffect(() => {
        if (auth.profile && auth.profile.usertype === 'customer') {
            // console.log('Customer data:', props.data);
            if (props.data) {
                // console.log('Active bookings:', props.data.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')));
            }
        }
    }, [props.data, auth.profile]);

    const [deviceId, setDeviceId] = useState();
    useEffect(() => {
        AsyncStorage.getItem('deviceId', (err, result) => {
            if (result) {
                setDeviceId(result);
            }
        });
    }, []);

    const getFallbackPostalCode = (address) => {
        if (!address) return '';

        const addr = address.toLowerCase();

        // Common airport postal codes
        if (addr.includes("o'hare") || addr.includes('ohare') || addr.includes('ord')) {
            return '60666'; // O'Hare Airport
        }
        if (addr.includes('midway') || addr.includes('mdw')) {
            return '60638'; // Midway Airport
        }

        // Add more known locations as needed

        return '';
    };

    const getPostalCodeFromAddress = async (address) => {
        try {
            // First try to extract from the address string
            const match = address.match(/\b\d{5}(?:-\d{4})?\b/);
            if (match) return match[0];

            // Try fallback postal codes for known locations
            const fallbackCode = getFallbackPostalCode(address);
            if (fallbackCode) return fallbackCode;

            // If still no postal code, use Google Geocoding API
            // If still no postal code, use Google Geocoding API
            const apiKey = Platform.OS === 'ios'
                ? GoogleMapApiConfig.ios
                : GoogleMapApiConfig.android;
            const encodedAddress = encodeURIComponent(address);
            const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedAddress}&key=${apiKey}`;

            const response = await fetch(url);
            const data = await response.json();
            console.log("geocodedata from address", data);

            if (data.results && data.results.length > 0) {
                const result = data.results[0];
                // Look for postal code in address components
                for (const component of result.address_components) {
                    if (component.types.includes('postal_code')) {
                        return component.long_name;
                    }
                }
            }

            return '';
        } catch (error) {
            console.log('Error getting postal code:', error);
            return '';
        }
    };

    useEffect(() => {
        if (!farelistdata) {
            dispatch(api.fetchFare());
        }
    }, [farelistdata, dispatch]);

    return (
        <View style={styles.textView3}>
            <View style={{ backgroundColor: MAIN_COLOR }}>
                <SegmentedControlTab
                    values={tabValues}
                    selectedIndex={tabIndex}
                    onTabPress={(index) => setTabIndex(index)}
                    borderRadius={0}
                    tabsContainerStyle={[styles.segmentcontrol, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
                    tabStyle={{
                        borderWidth: 0,
                        backgroundColor: 'transparent',
                        borderColor: colors.WHITE
                    }}
                    activeTabStyle={{ borderBottomColor: colors.WHITE, backgroundColor: 'transparent', borderBottomWidth: 1.5 }}
                    tabTextStyle={{ color: appConsts.canCall ? colors.BORDER_TEXT : colors.PROFILE_PLACEHOLDER_CONTENT, fontFamily: fonts.Bold }}
                    activeTabTextStyle={{ color: colors.WHITE }}
                />
                <View style={{ height: '100%' }}>
                    <View style={{ height: 5 }} />
                    <View style={styles.listView}>
                        <View style={{ marginTop: 5, flex: 1, marginBottom: 100 }}>
                            {auth.profile && auth.profile.usertype === 'driver' && tabIndex === 0 ? (
                                // Active rides tab (tabIndex 0)
                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    scrollEnabled={true}
                                    keyExtractor={(item, index) => index.toString()}
                                    data={sortByTripDate(props.data.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')))}
                                    renderItem={renderData}
                                    ListEmptyComponent={
                                        <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                            <View style={styles.emptyBox}  >
                                                <Text style={styles.emptyText} >{t('no_data_available')}</Text>
                                            </View>
                                        </View>
                                    }
                                />
                            ) : auth.profile && auth.profile.usertype === 'driver' && tabIndex === 1 ? (
                                // ASSIGNED_RIDE tab (tabIndex 1)
                                renderAssignedCard()
                            ) : auth.profile && auth.profile.usertype === 'driver' && tabIndex === 2 ? (
                                // COMPLETE tab (tabIndex 2)
                                <FlatList
                                showsVerticalScrollIndicator={false}
                                scrollEnabled={true}
                                keyExtractor={(item, index) => index.toString()}
                                data={sortByTripDate(props.data.filter(item => item.status === 'COMPLETE'), 'desc')}
                                renderItem={renderData}
                                    ListEmptyComponent={
                                        <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                            <View style={styles.emptyBox}  >
                                                <Text style={styles.emptyText} >{t('no_data_available')}</Text>
                                            </View>
                                        </View>
                                    }
                                />
                            ) : auth.profile && auth.profile.usertype === 'driver' && tabIndex === 3 ? (
                                // PUT_OUT tab (tabIndex 3)
                                putOutBookings.length === 0 ? (
                                    <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                        <View style={styles.emptyBox}>
                                            <Text style={styles.emptyText}>{t('no_data_available')}</Text>
                                        </View>
                                    </View>
                                ) : (
                                    <FlatList
                                        showsVerticalScrollIndicator={false}
                                        scrollEnabled={true}
                                        keyExtractor={(item, index) => item.id || index.toString()}
                                        data={sortByTripDate(putOutBookings)}
                                        renderItem={renderData}
                                    />
                                )
                            ) : auth.profile && auth.profile.usertype === 'customer' && tabIndex === 0 ? (
                                // Customer active bookings
                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    scrollEnabled={true}
                                    keyExtractor={(item, index) => index.toString()}
                                    data={sortByTripDate(props.data.filter(item => !(item.status === 'CANCELLED' || item.status === 'COMPLETE')))}
                                    renderItem={renderData}
                                    ListEmptyComponent={
                                        <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                            <View style={styles.emptyBox}  >
                                                <Text style={styles.emptyText} >{t('no_data_available')}</Text>
                                            </View>
                                        </View>
                                    }
                                />
                            ) : tabIndex === 1 ? (
                                // Customer COMPLETE tab
                                <FlatList
                                showsVerticalScrollIndicator={false}
                                scrollEnabled={true}
                                keyExtractor={(item, index) => index.toString()}
                                data={sortByTripDate(props.data.filter(item => item.status === 'COMPLETE'), 'desc')}
                                renderItem={renderData}
                                    ListEmptyComponent={
                                        <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                            <View style={styles.emptyBox}  >
                                                <Text style={styles.emptyText} >{t('no_data_available')}</Text>
                                            </View>
                                        </View>
                                    }
                                />
                            ) : tabIndex === 2 ? (
                                // Customer CANCELLED tab
                                <FlatList
                                showsVerticalScrollIndicator={false}
                                scrollEnabled={true}
                                keyExtractor={(item, index) => index.toString()}
                                data={sortByTripDate(props.data.filter(item => item.status === 'CANCELLED'), 'desc')}
                                renderItem={renderData}
                                    ListEmptyComponent={
                                        <View style={[styles.emptyListContainer, { marginTop: height / 2.8 }]}>
                                            <View style={styles.emptyBox}  >
                                                <Text style={styles.emptyText} >{t('no_data_available')}</Text>
                                            </View>
                                        </View>
                                    }
                                />
                            ) : null}
                        </View>
                    </View>
                </View>
            </View>

            <Modal
                visible={putOutModalVisible}
                transparent
                animationType="slide"
                onRequestClose={() => setPutOutModalVisible(false)}
            >
                <View style={{
                    flex: 1,
                    backgroundColor: 'rgba(0,0,0,0.4)',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <View style={{
                        backgroundColor: '#fff',
                        borderRadius: 12,
                        padding: 24,
                        width: 300,
                        alignItems: 'center'
                    }}>
                        <Text style={{ fontWeight: 'bold', fontSize: 18, marginBottom: 16 }}>Select Reason</Text>
                        {['Not available', 'Car issue', 'Personal reason', 'Other'].map(reason => (
                            <TouchableOpacity
                                key={reason}
                                style={{
                                    padding: 10,
                                    backgroundColor: putOutReason === reason ? '#eee' : '#fff',
                                    borderRadius: 6,
                                    marginBottom: 6,
                                    width: '100%',
                                    alignItems: 'center'
                                }}
                                onPress={() => setPutOutReason(reason)}
                            >
                                <Text>{reason}</Text>
                            </TouchableOpacity>
                        ))}
                        <View style={{ flexDirection: 'row', marginTop: 16 }}>
                            <TouchableOpacity
                                style={{
                                    backgroundColor: '#FF3B30',
                                    padding: 10,
                                    borderRadius: 6,
                                    marginRight: 10
                                }}
                                onPress={() => setPutOutModalVisible(false)}
                            >
                                <Text style={{ color: '#fff' }}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={{
                                    backgroundColor: '#34C759',
                                    padding: 10,
                                    borderRadius: 6
                                }}
                                onPress={handlePutOutConfirm}
                            >
                                <Text style={{ color: '#fff' }}>Confirm</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>

        </View>
    );
};

const styles = StyleSheet.create({
    BookingContainer: {
        margin: 10,
        borderRadius: 10,
        shadowColor: "black",
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.23,
        shadowRadius: 1,
    },
    box: {
        backgroundColor: colors.WHITE,
        borderRadius: 10,
    },
    elevation: {
        elevation: 5
    },
    dateStyle: {
        fontFamily: fonts.Bold,
        color: colors.HEADER,
        fontSize: 18
    },
    textView3: {
        flex: 1,
        backgroundColor: MAIN_COLOR
    },
    segmentcontrol: {
        color: colors.WHITE,
        fontSize: 18,
        fontFamily: fonts.Regular,
        marginTop: 0,
        alignSelf: "center",
        height: 50
    },
    location: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginVertical: 6,
        marginHorizontal: 5
    },
    hbox2: {
        width: 1,
        backgroundColor: MAIN_COLOR
    },
    textStyle: {
        fontSize: 16,
        fontFamily: fonts.Regular
    },
    textStyleBold: {
        fontSize: 16,
        fontFamily: fonts.Bold
    },
    tripdate: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        padding: 4
    },
    centeredView: {
        flex: 1,
        justifyContent: "center",
        backgroundColor: colors.BACKGROUND
    },
    modalView: {
        margin: 20,
        backgroundColor: "white",
        borderRadius: 20,
        padding: 35,
        alignItems: "flex-start",
        shadowColor: colors.BLACK,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    textContainerStyle: {
        flexDirection: 'column',
        marginBottom: 12,
    },
    details: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        borderBottomWidth: .6,
        borderBottomColor: MAIN_COLOR
    },
    clock: {
        flex: 1,
        justifyContent: 'space-around',
        alignItems: 'center',
        minHeight: 60
    },
    section: {
        flex: 1,
        justifyContent: 'space-evenly',
        alignItems: 'center'
    },
    driverDetails: {
        flex: 1,
        alignItems: 'center',
        marginTop: 10,
        paddingVertical: 10
    },
    modalBtn: {
        flexDirection: 'row',
        alignSelf: 'center',
        borderWidth: 1,
        minWidth: 80,
        padding: 5,
        justifyContent: 'center',
        borderRadius: 10
    },
    listView: {
        flex: 1,
        backgroundColor: colors.WHITE,
        width: '100%',
        borderTopRightRadius: 10,
        borderTopLeftRadius: 10
    },
    emptyListContainer: {
        width: width,
        justifyContent: "center",
        alignItems: "center"
    },
    emptyBox: {
        backgroundColor: MAIN_COLOR,
        borderRadius: 10
    },
    emptyText: {
        fontFamily: fonts.Bold,
        color: colors.WHITE,
        padding: 15,
        fontSize: 18
    },
    textContent1: {
        fontFamily: fonts.Regular
    }
});