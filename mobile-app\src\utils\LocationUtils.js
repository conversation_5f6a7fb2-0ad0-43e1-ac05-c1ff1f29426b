// Utility functions for location calculations without API calls

// Calculate distance between two points using Haversine formula
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return distance;
};

// Calculate bearing between two points
export const calculateBearing = (lat1, lon1, lat2, lon2) => {
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    
    const y = Math.sin(dLon) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLon);
    
    let bearing = Math.atan2(y, x) * 180 / Math.PI;
    bearing = (bearing + 360) % 360;
    
    return bearing;
};

// Check if location is within radius of another location
export const isWithinRadius = (lat1, lon1, lat2, lon2, radiusKm) => {
    const distance = calculateDistance(lat1, lon1, lat2, lon2);
    return distance <= radiusKm;
};

// Smooth location updates to reduce jitter
export const smoothLocationUpdate = (newLocation, lastLocation, smoothingFactor = 0.3) => {
    if (!lastLocation) return newLocation;
    
    return {
        lat: lastLocation.lat + (newLocation.lat - lastLocation.lat) * smoothingFactor,
        lng: lastLocation.lng + (newLocation.lng - lastLocation.lng) * smoothingFactor,
        timestamp: newLocation.timestamp,
        accuracy: newLocation.accuracy,
        speed: newLocation.speed
    };
};

// Validate location data
export const isValidLocation = (location) => {
    return location && 
           typeof location.lat === 'number' && 
           typeof location.lng === 'number' &&
           !isNaN(location.lat) && 
           !isNaN(location.lng) &&
           location.lat >= -90 && location.lat <= 90 &&
           location.lng >= -180 && location.lng <= 180;
};
