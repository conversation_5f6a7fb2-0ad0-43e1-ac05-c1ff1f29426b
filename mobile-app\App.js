import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  FirebaseProvider,
  store
} from 'common';
import { Asset } from 'expo-asset';
import * as Font from 'expo-font';
import * as Notifications from 'expo-notifications';
import * as SplashScreen from 'expo-splash-screen';
import * as Updates from 'expo-updates';
import React, { useEffect, useState, useRef } from 'react';
import {
  ActivityIndicator,
  AppState,
  ImageBackground,
  LogBox,
  NativeModules,
  Platform,
  StyleSheet,
  View
} from "react-native";
import { Provider } from "react-redux";
import AppCommon from './AppCommon';
import { FirebaseConfig } from './config/FirebaseConfig';
import { colors } from './src/common/theme';
import AppContainer from './src/navigation/AppNavigator';
import locationTracker from './src/services/LocationTracker';

SplashScreen.preventAutoHideAsync();

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Component to handle keep-awake functionality based on driver status
function KeepAwakeManager() {
  const [isKeepAwakeActive, setIsKeepAwakeActive] = useState(false);
  const appState = useRef(AppState.currentState);
  const storeSubscription = useRef(null);

  useEffect(() => {
    // Handle app state changes
    const handleAppStateChange = (nextAppState) => {
      console.log(`🔄 App State Change: ${appState.current} → ${nextAppState}`);
      
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        console.log('📱 App returned to foreground');
        
        // Small delay to ensure app is fully active before managing keep-awake
        setTimeout(() => {
          const state = store.getState();
          const auth = state.auth;
          const shouldKeepAwake = auth.profile && 
                                 auth.profile.usertype === 'driver' && 
                                 auth.profile.driverActiveStatus;
          
          if (shouldKeepAwake && !isKeepAwakeActive) {
            activateKeepAwake();
          }
        }, 1000);
      } else if (appState.current === 'active' && nextAppState.match(/inactive|background/)) {
        // App is going to background
        console.log('📱 App going to background');
        
        // Deactivate keep-awake when going to background to save resources
        if (isKeepAwakeActive) {
          deactivateKeepAwake();
        }
      }
      
      appState.current = nextAppState;
    };

    // Subscribe to app state changes
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Subscribe to store changes to monitor driver status
    storeSubscription.current = store.subscribe(() => { 
      const state = store.getState();
      const auth = state.auth;
      
      // Only manage keep-awake when app is active
      if (appState.current === 'active') {
        const shouldKeepAwake = auth.profile && 
                               auth.profile.usertype === 'driver' && 
                               auth.profile.driverActiveStatus;

        if (shouldKeepAwake && !isKeepAwakeActive) {
          activateKeepAwake();
        } else if (!shouldKeepAwake && isKeepAwakeActive) {
          deactivateKeepAwake();
        }
      }
    });

    return () => {
      // Cleanup subscriptions
      if (subscription) {
        subscription.remove();
      }
      if (storeSubscription.current) {
        storeSubscription.current();
      }
      
      // Cleanup: deactivate keep-awake when component unmounts
      if (isKeepAwakeActive) {
        deactivateKeepAwake();
      }
    };
  }, [isKeepAwakeActive]);

  const activateKeepAwake = () => {
    try {
      const { activateKeepAwake } = require('expo-keep-awake');
      activateKeepAwake();
      console.log(`🔋 Keep-awake activated for ${Platform.OS} - driver active`);
      setIsKeepAwakeActive(true);
    } catch (error) {
      console.log('Error activating keep-awake:', error);
      // Fallback for Android if expo-keep-awake fails
      if (Platform.OS === 'android' && NativeModules.KeepAwake) {
        try {
          NativeModules.KeepAwake.activate();
          console.log('🔋 Android fallback keep-awake activated');
          setIsKeepAwakeActive(true);
        } catch (fallbackError) {
          console.log('Android fallback also failed:', fallbackError);
        }
      }
    }
  };

  const deactivateKeepAwake = () => {
    try {
      const { deactivateKeepAwake } = require('expo-keep-awake');
      deactivateKeepAwake();
      console.log(`🔋 Keep-awake deactivated for ${Platform.OS} - driver not active`);
      setIsKeepAwakeActive(false);
    } catch (error) {
      console.log('Error deactivating keep-awake:', error);
      // Fallback for Android if expo-keep-awake fails
      if (Platform.OS === 'android' && NativeModules.KeepAwake) {
        try {
          NativeModules.KeepAwake.deactivate();
          console.log('🔋 Android fallback keep-awake deactivated');
          setIsKeepAwakeActive(false);
        } catch (fallbackError) {
          console.log('Android fallback deactivation also failed:', fallbackError);
        }
      }
    }
  };

  return null; // This component doesn't render anything
}

export default function App() {
  const [assetsLoaded, setAssetsLoaded] = useState(false);

  useEffect(() => {
    LogBox.ignoreAllLogs(true);
    LogBox.ignoreLogs([
      'Setting a timer',
      'SplashScreen.show'
    ])

    const ReactNative = require('react-native');
    try {
        ReactNative.I18nManager.allowRTL(false);
    } catch (e) {
        console.log(e);
    }

    

    onLoad();

    // Cleanup location tracking on app unmount to avoid leaks
    return () => {
      try {
        locationTracker.cleanup();
      } catch (e) {
        console.log('Error cleaning up location tracker:', e);
      }
    };
  }, []);

  const _loadResourcesAsync = async () => {
    return Promise.all([
      Asset.loadAsync([
        require('./assets/images/background.jpg'),
        require('./assets/images/logo.png'),
        require('./assets/images/taxi2fly.png'),
        require('./assets/images/TAXI2FLYbg.jpeg'),
        require('./assets/images/TAXI2FLYintro.png'),
        require('./assets/images/g4.gif'),
        require('./assets/images/lodingDriver.gif')
      ]),
      Font.loadAsync({
        'Roboto-Bold': require('./assets/fonts/Roboto-Bold.ttf'),
        'Roboto-Regular': require('./assets/fonts/Roboto-Regular.ttf'),
        'Roboto-Medium': require('./assets/fonts/Roboto-Medium.ttf'),
        'Roboto-Light': require('./assets/fonts/Roboto-Light.ttf'),
        'Ubuntu-Regular': require('./assets/fonts/Ubuntu-Regular.ttf'),
        'Ubuntu-Medium': require('./assets/fonts/Ubuntu-Medium.ttf'),
        'Ubuntu-Light': require('./assets/fonts/Ubuntu-Light.ttf'),
        'Ubuntu-Bold': require('./assets/fonts/Ubuntu-Bold.ttf'),
        "DancingScript-Bold":require('./assets/fonts/DancingScript-Bold.ttf'),
        "DancingScript-Medium":require('./assets/fonts/DancingScript-Medium.ttf'),
        "DancingScript-SemiBold":require('./assets/fonts/DancingScript-SemiBold.ttf')
      }),
    ]);
  };

  const onLoad = async () => {
    if (__DEV__) {
      _loadResourcesAsync().then(() => {
        setAssetsLoaded(true);
      });
    } else {
      try {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          await Updates.fetchUpdateAsync();
          await Updates.reloadAsync();
        }
        _loadResourcesAsync().then(() => {
          setAssetsLoaded(true);
        })
      } catch (error) {
        _loadResourcesAsync().then(() => {
          setAssetsLoaded(true);
        })
      }
    }
  }

  if (!assetsLoaded) {
    return <View style={styles.container}>
      <ImageBackground
        source={require('./assets/images/TAXI2FLYintro.png')}
        resizeMode="stretch"
        style={styles.imagebg}
      >
        <ActivityIndicator style={{ paddingBottom: 100 }} color={colors.INDICATOR_BLUE} size='large' />
      </ImageBackground>
    </View>
  }

  return (
    <Provider store={store}>
      <FirebaseProvider config={FirebaseConfig} AsyncStorage={AsyncStorage}>
        <AppCommon>
          <KeepAwakeManager />
          <AppContainer />
        </AppCommon>
      </FirebaseProvider>
    </Provider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  imagebg: {
    flex:1,
    justifyContent: "flex-end",
    alignItems: 'center'
  }
});