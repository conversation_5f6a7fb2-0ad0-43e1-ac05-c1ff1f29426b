import * as Location from 'expo-location';
import { store, api } from 'common';
import { calculateDistance, isValidLocation } from '../utils/LocationUtils';
import apiMonitor from '../utils/ApiMonitor';

class SimpleLocationTracker {
  constructor() {
    this.watchId = null;
    this.isTracking = false;
    this.lastLocation = null;
    this.routeCache = new Map();
    this.lastRouteFetch = 0;
    this.routeFetchInterval = 2 * 60 * 1000; // 2 minutes between route fetches (BEST CASE)
    this.minDistanceForNewRoute = 2000; // 2km minimum for new route (BEST CASE)
    this.apiCallCount = 0; // Track API calls for monitoring
    this.currentBookingId = null; // Track current booking for API storage
    this.bookingApiCounts = new Map(); // Store API counts per booking
    this.routePredictionCache = new Map(); // Cache for route predictions
    this.lastSignificantLocation = null; // Track last significant location change
  }

  async startTracking(onLocationUpdate, onError, bookingId = null) {
    try {
      // Set current booking ID for API tracking
      this.currentBookingId = bookingId;
      
      // Initialize API count for this booking
      if (bookingId && !this.bookingApiCounts.has(bookingId)) {
        this.bookingApiCounts.set(bookingId, {
          totalCalls: 0,
          startTime: Date.now(),
          lastCallTime: null
        });
      }

      // Check permissions
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
        if (newStatus !== 'granted') {
          throw new Error('Location permission denied');
        }
      }

      // Clear existing watcher
      if (this.watchId) {
        await Location.clearWatchAsync(this.watchId);
      }

      // Start watching with optimized settings for live tracking
      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 5000, // 5 seconds for smooth updates
          distanceInterval: 10, // 10 meters for responsive tracking
          mayShowUserSettingsDialog: false,
        },
        (location) => {
          const newLocation = {
            lat: location.coords.latitude,
            lng: location.coords.longitude,
            timestamp: Date.now(),
            accuracy: location.coords.accuracy,
            speed: location.coords.speed || 0,
          };

          if (isValidLocation(newLocation)) {
            this.lastLocation = newLocation;
            
            // Update Redux store
            store.dispatch({
              type: 'UPDATE_GPS_LOCATION',
              payload: newLocation,
            });

            // Call callback
            if (onLocationUpdate) {
              onLocationUpdate(newLocation);
            }
          }
        },
        (error) => {
          console.error('Location tracking error:', error);
          if (onError) {
            onError(error);
          }
        }
      );

      this.isTracking = true;
      return true;
    } catch (error) {
      console.error('Failed to start location tracking:', error);
      if (onError) {
        onError(error);
      }
      return false;
    }
  }

  async stopTracking() {
    if (this.watchId) {
      await Location.clearWatchAsync(this.watchId);
      this.watchId = null;
    }
    this.isTracking = false;
  }

  // Get route with 2-minute API throttling and smart caching (BEST CASE)
  async getCachedRoute(startLoc, destLoc, waypoints = '') {
    const routeKey = `${startLoc.lat},${startLoc.lng}_${destLoc.lat},${destLoc.lng}`;
    const now = Date.now();

    // Check if enough time has passed since last API call (2 minutes)
    const timeSinceLastCall = now - this.lastRouteFetch;
    if (timeSinceLastCall < this.routeFetchInterval) {
      // Return cached route if available
      if (this.routeCache.has(routeKey)) {
        console.log(`📋 Using cached route (${Math.round(timeSinceLastCall/1000)}s since last API call)`);
        return this.routeCache.get(routeKey).data;
      }
      // If no cache and too soon, return null to avoid API call
      console.log(`⏰ API call throttled (${Math.round(timeSinceLastCall/1000)}s < 120s)`);
      return null;
    }

    // Check if we need a new route based on distance (2km threshold)
    let shouldFetch = true;
    if (this.lastSignificantLocation && this.lastRouteFetch > 0) {
      const distanceMoved = calculateDistance(
        startLoc.lat, startLoc.lng,
        this.lastSignificantLocation.lat, this.lastSignificantLocation.lng
      ) * 1000; // Convert to meters

      shouldFetch = distanceMoved >= this.minDistanceForNewRoute;
      
      if (!shouldFetch) {
        console.log(`📍 Not enough movement (${Math.round(distanceMoved)}m < 2000m)`);
      }
    }

    if (!shouldFetch && this.routeCache.has(routeKey)) {
      console.log(`📋 Using cached route (insufficient movement)`);
      return this.routeCache.get(routeKey).data;
    }

    // Check for route prediction cache
    const predictionKey = `prediction_${destLoc.lat},${destLoc.lng}`;
    if (this.routePredictionCache.has(predictionKey)) {
      const prediction = this.routePredictionCache.get(predictionKey);
      const timeSincePrediction = now - prediction.timestamp;
      
      // Use prediction if it's less than 5 minutes old
      if (timeSincePrediction < 5 * 60 * 1000) {
        console.log(`🔮 Using route prediction (${Math.round(timeSincePrediction/1000)}s old)`);
        return prediction.data;
      }
    }

    try {
      // Make API call only every 2 minutes
      console.log(`🚀 Making API call #${++this.apiCallCount} at ${new Date().toISOString()}`);
      
      const routeData = await api.getDirectionsApi(
        `${startLoc.lat},${startLoc.lng}`,
        `${destLoc.lat},${destLoc.lng}`,
        waypoints
      );

      // Log successful API call
      apiMonitor.logApiCall('Directions', true);

      // Update booking API count
      if (this.currentBookingId) {
        const bookingStats = this.bookingApiCounts.get(this.currentBookingId);
        if (bookingStats) {
          bookingStats.totalCalls++;
          bookingStats.lastCallTime = now;
        }
      }

      // Cache the route with extended TTL
      this.routeCache.set(routeKey, {
        data: routeData,
        timestamp: now,
        ttl: 10 * 60 * 1000 // 10 minutes TTL
      });

      // Cache route prediction for destination
      this.routePredictionCache.set(predictionKey, {
        data: routeData,
        timestamp: now
      });

      // Update significant location
      this.lastSignificantLocation = { lat: startLoc.lat, lng: startLoc.lng };
      this.lastRouteFetch = now;

      // Clean old cache entries (keep only last 5 routes)
      if (this.routeCache.size > 5) {
        const entries = Array.from(this.routeCache.entries());
        entries.sort((a, b) => b[1].timestamp - a[1].timestamp);
        this.routeCache.clear();
        entries.slice(0, 5).forEach(([key, value]) => {
          this.routeCache.set(key, value);
        });
      }

      return routeData;
    } catch (error) {
      console.error('Failed to fetch route:', error);
      // Log failed API call
      apiMonitor.logApiCall('Directions', false);
      // Return cached route if available
      return this.routeCache.get(routeKey)?.data;
    }
  }

  getCurrentLocation() {
    return this.lastLocation;
  }

  isActive() {
    return this.isTracking;
  }

  clearCache() {
    this.routeCache.clear();
    this.routePredictionCache.clear();
    this.lastSignificantLocation = null;
  }

  // Clear prediction cache only
  clearPredictionCache() {
    this.routePredictionCache.clear();
  }

  // Get API call statistics for monitoring
  getApiCallStats() {
    const monitorStats = apiMonitor.getStats();
    const costs = apiMonitor.getCostEstimate();
    
    return {
      totalCalls: this.apiCallCount,
      lastCallTime: this.lastRouteFetch,
      cacheSize: this.routeCache.size,
      isTracking: this.isTracking,
      monitorStats,
      costs
    };
  }

  // Reset API call counter
  resetApiCallCount() {
    this.apiCallCount = 0;
  }

  // Get API count for specific booking
  getBookingApiCount(bookingId) {
    return this.bookingApiCounts.get(bookingId) || {
      totalCalls: 0,
      startTime: null,
      lastCallTime: null
    };
  }

  // Get all booking API counts
  getAllBookingApiCounts() {
    return Array.from(this.bookingApiCounts.entries()).map(([bookingId, stats]) => ({
      bookingId,
      ...stats,
      duration: stats.startTime ? Date.now() - stats.startTime : 0,
      cost: stats.totalCalls * 0.005 // $0.005 per API call
    }));
  }

  // Store API count in booking data
  async storeApiCountInBooking(bookingId) {
    try {
      const bookingStats = this.getBookingApiCount(bookingId);
      const apiData = {
        totalApiCalls: bookingStats.totalCalls,
        apiCost: bookingStats.totalCalls * 0.005,
        trackingStartTime: bookingStats.startTime,
        lastApiCallTime: bookingStats.lastCallTime,
        duration: bookingStats.startTime ? Date.now() - bookingStats.startTime : 0
      };

      // Try direct Firebase update first
      try {
        const { firebase } = require('common');
        const { update } = require('firebase/database');
        
        await update(firebase.singleBookingRef(bookingId), {
          apiUsageData: apiData
        });

        console.log(`📊 Stored API data for booking ${bookingId}:`, apiData);
        return apiData;
      } catch (firebaseError) {
        console.warn('Direct Firebase update failed, trying API method:', firebaseError);
        
        // Fallback to API method
        const { api } = require('common');
        await api.updateBooking({
          id: bookingId,
          apiUsageData: apiData
        });

        console.log(`📊 Stored API data for booking ${bookingId} (via API):`, apiData);
        return apiData;
      }
    } catch (error) {
      console.error('Failed to store API count in booking:', error);
      return null;
    }
  }

  // End tracking for a booking and store final API count
  async endBookingTracking(bookingId) {
    try {
      const apiData = await this.storeApiCountInBooking(bookingId);
      
      // Remove from active tracking
      if (this.currentBookingId === bookingId) {
        this.currentBookingId = null;
      }
      
      return apiData;
    } catch (error) {
      console.error('Failed to end booking tracking:', error);
      return null;
    }
  }
}

const simpleLocationTracker = new SimpleLocationTracker();
export default simpleLocationTracker;
