import { Alert } from 'react-native';

class ErrorHandler {
  static locationErrors = {
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    POSITION_UNAVAILABLE: 'POSITION_UNAVAILABLE',
    TIMEOUT: 'TIMEOUT',
    PLAY_SERVICES_NOT_AVAILABLE: 'PLAY_SERVICES_NOT_AVAILABLE',
    SETTINGS_NOT_SATISFIED: 'SETTINGS_NOT_SATISFIED',
    INTERNAL_ERROR: 'INTERNAL_ERROR'
  };

  static mapLocationError(error) {
    if (!error) return null;

    const errorMessage = error.message?.toLowerCase() || '';
    const errorCode = error.code;

    // Common location error patterns
    if (errorCode === 1 || errorMessage.includes('permission')) {
      return {
        type: this.locationErrors.PERMISSION_DENIED,
        message: 'Location permission is required for this feature',
        userFriendly: 'Please enable location permissions in your device settings',
        actionable: true,
        action: 'openSettings'
      };
    }

    if (errorCode === 2 || errorMessage.includes('unavailable') || errorMessage.includes('network')) {
      return {
        type: this.locationErrors.POSITION_UNAVAILABLE,
        message: 'Location is temporarily unavailable',
        userFriendly: 'Unable to get your location. Please check your GPS and internet connection',
        actionable: true,
        action: 'retry'
      };
    }

    if (errorCode === 3 || errorMessage.includes('timeout')) {
      return {
        type: this.locationErrors.TIMEOUT,
        message: 'Location request timed out',
        userFriendly: 'Location request is taking too long. Please try again',
        actionable: true,
        action: 'retry'
      };
    }

    if (errorMessage.includes('play services') || errorMessage.includes('google play')) {
      return {
        type: this.locationErrors.PLAY_SERVICES_NOT_AVAILABLE,
        message: 'Google Play Services not available',
        userFriendly: 'Please update Google Play Services to use location features',
        actionable: true,
        action: 'updatePlayServices'
      };
    }

    if (errorMessage.includes('settings') || errorMessage.includes('resolution')) {
      return {
        type: this.locationErrors.SETTINGS_NOT_SATISFIED,
        message: 'Location settings need to be enabled',
        userFriendly: 'Please enable high accuracy location in your device settings',
        actionable: true,
        action: 'openLocationSettings'
      };
    }

    // Default to internal error
    return {
      type: this.locationErrors.INTERNAL_ERROR,
      message: error.message || 'Unknown location error',
      userFriendly: 'An unexpected error occurred with location services',
      actionable: false,
      action: null
    };
  }

  static handleLocationError(error, context = 'location') {
    const mappedError = this.mapLocationError(error);
    
    console.error(`[${context}] Location error:`, {
      original: error,
      mapped: mappedError,
      timestamp: new Date().toISOString()
    });

    // Log to crash reporting service if available
    if (global.crashlytics) {
      global.crashlytics.recordError(error);
    }

    return mappedError;
  }

  static showLocationErrorAlert(error, onRetry, onOpenSettings) {
    const mappedError = this.handleLocationError(error);
    
    const buttons = [{ text: 'OK' }];
    
    if (mappedError.actionable) {
      switch (mappedError.action) {
        case 'retry':
          if (onRetry) {
            buttons.unshift({ text: 'Retry', onPress: onRetry });
          }
          break;
        case 'openSettings':
        case 'openLocationSettings':
          if (onOpenSettings) {
            buttons.unshift({ text: 'Open Settings', onPress: onOpenSettings });
          }
          break;
      }
    }

    Alert.alert(
      'Location Error',
      mappedError.userFriendly,
      buttons
    );

    return mappedError;
  }

  static async safeLocationCall(locationFunction, fallback = null) {
    try {
      return await locationFunction();
    } catch (error) {
      const mappedError = this.handleLocationError(error);
      
      if (fallback && typeof fallback === 'function') {
        try {
          return await fallback();
        } catch (fallbackError) {
          console.error('Fallback also failed:', fallbackError);
          throw mappedError;
        }
      }
      
      throw mappedError;
    }
  }

  // Memory leak prevention
  static createSafeTimeout(callback, delay) {
    const timeoutId = setTimeout(() => {
      try {
        callback();
      } catch (error) {
        console.error('Safe timeout callback error:', error);
      }
    }, delay);

    return {
      clear: () => clearTimeout(timeoutId),
      id: timeoutId
    };
  }

  static createSafeInterval(callback, interval) {
    const intervalId = setInterval(() => {
      try {
        callback();
      } catch (error) {
        console.error('Safe interval callback error:', error);
      }
    }, interval);

    return {
      clear: () => clearInterval(intervalId),
      id: intervalId
    };
  }

  // React component error boundary helper
  static getErrorBoundary() {
    return class LocationErrorBoundary extends React.Component {
      constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
      }

      static getDerivedStateFromError(error) {
        return { hasError: true, error };
      }

      componentDidCatch(error, errorInfo) {
        console.error('Location component error:', error, errorInfo);
        ErrorHandler.handleLocationError(error, 'component');
      }

      render() {
        if (this.state.hasError) {
          return this.props.fallback || null;
        }

        return this.props.children;
      }
    };
  }
}

export default ErrorHandler;
