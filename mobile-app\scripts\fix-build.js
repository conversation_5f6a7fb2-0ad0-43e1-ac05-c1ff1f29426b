#!/usr/bin/env node

/**
 * Build fix script for Kotlin compilation issues
 * This script helps resolve common build issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing build issues...');

function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
}

function cleanBuildArtifacts() {
  console.log('🧹 Cleaning build artifacts...');
  
  const pathsToClean = [
    'node_modules',
    '.expo',
    'android',
    'ios'
  ];
  
  pathsToClean.forEach(pathToClean => {
    const fullPath = path.join(process.cwd(), pathToClean);
    if (fs.existsSync(fullPath)) {
      console.log(`🗑️ Removing ${pathTo<PERSON>lean}...`);
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
  });
}

function main() {
  try {
    console.log('🚀 Starting build fix process...');
    
    // Step 1: Clean everything
    cleanBuildArtifacts();
    
    // Step 2: Reinstall dependencies
    runCommand('npm install', 'Installing dependencies');
    
    // Step 3: Clear Expo cache
    runCommand('npx expo install --fix', 'Fixing Expo dependencies');
    
    // Step 4: Prebuild with clean
    runCommand('npx expo prebuild --clean --platform android', 'Prebuilding Android');
    
    console.log('🎉 Build fix completed successfully!');
    console.log('📱 You can now try building again with:');
    console.log('   eas build --platform android --profile production');
    
  } catch (error) {
    console.error('❌ Build fix failed:', error.message);
    console.log('\n🔍 Troubleshooting suggestions:');
    console.log('1. Check your Expo SDK version compatibility');
    console.log('2. Verify all dependencies are compatible with Expo 51');
    console.log('3. Try building with --clear-cache flag');
    console.log('4. Check EAS CLI version: npx eas-cli@latest');
    process.exit(1);
  }
}

main();
