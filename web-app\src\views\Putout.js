import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import MaterialTable from "material-table";
import { Grid, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ThemeProvider } from '@mui/material/styles';
import InfoIcon from '@mui/icons-material/Info';
import { useNavigate } from "react-router-dom";
import theme from "../styles/tableStyle";
import { SECONDORY_COLOR, downloadCsv } from "../common/sharedFunctions";
import { colors } from "../components/Theme/WebTheme";

const Putout = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const [putoutRides, setPutoutRides] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (bookinglistdata.bookings) {
      // Filter rides that have at least one putoutdrivers entry with putout_res
      const filtered = bookinglistdata.bookings.filter((ride) =>
        Array.isArray(ride.putoutdrivers) &&
        ride.putoutdrivers.some((drv) => drv.putout_res)
      );
      setPutoutRides(filtered);
    } else {
      setPutoutRides([]);
    }
  }, [bookinglistdata.bookings]);

  const columns = [
    { title: t("Booking Reference"), field: "reference" },
    { title: t("Customer Name"), field: "customer_name" },
    { title: t("Pickup Address"), field: "pickupAddress" },
    { title: t("Drop Address"), field: "dropAddress" },
    { title: t("Car Type"), field: "carType" },
    { title: t("Putout Details"),
      render: rowData => {
        const drv = (rowData.putoutdrivers || []).find(d => d.putout_res);
        return drv ? drv.putout_res : t("N/A");
      }
    },
  ];

  const actions = [
    {
      icon: () => <InfoIcon color="black" />,
      tooltip: t("Details"),
      onClick: (event, rowData) => {
        navigate(`/putoutdetails/${rowData.id}`);
      }
    }
  ];

  return (
    <Grid container spacing={2} style={{ padding: 20 }}>
      <Grid item xs={12}>
        {/* <Typography variant="h5" style={{ marginBottom: 20 }}>
          {t("Putout Trips")}
        </Typography> */}
        <ThemeProvider theme={theme}>
          <MaterialTable
            title={t("Putout Trips")}
            style={{
              direction: isRTL === "rtl" ? "rtl" : "ltr",
              borderRadius: "8px",
              boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
            }}
            columns={columns}
            data={putoutRides}
            onRowClick={(evt, selectedRow) => setSelectedRow(selectedRow.tableData.id)}
            actions={actions}
            options={{
              pageSize: 100,
              pageSizeOptions: [100, 500, { value: putoutRides.length, label: t("All") }],
              toolbarbuttonalignment: "right",
              exportCsv: (columns, data) => {
                let hArray = [];
                const headerRow = columns.map((col) => {
                  if (typeof col.title === "object") {
                    return col.title.props.text;
                  }
                  hArray.push(col.field);
                  return col.title;
                });
                const dataRows = data.map(({ tableData, ...row }) => {
                  let dArr = [];
                  for (let i = 0; i < hArray.length; i++) {
                    dArr.push(row[hArray[i]]);
                  }
                  return Object.values(dArr);
                });
                const { exportDelimiter } = ",";
                const delimiter = exportDelimiter ? exportDelimiter : ",";
                const csvContent = [headerRow, ...dataRows]
                  .map((e) => e.join(delimiter))
                  .join("\n");
                const csvFileName = "putout.csv";
                downloadCsv(csvContent, csvFileName);
              },
              exportButton: true,
              maxColumnSort: "all_columns",
              rowStyle: (rowData) => ({
                backgroundColor:
                  selectedRow === rowData.tableData.id ? colors.ROW_SELECTED : colors.WHITE
              }),
              maxBodyHeight: "calc(100vh - 199.27px)",
              headerStyle: {
                position: "sticky",
                top: "0px",
                backgroundColor: SECONDORY_COLOR,
                color: colors.Black,
                fontWeight: "bold ",
                textAlign: "center",
                zIndex: 1,
                border: `1px solid ${colors.TABLE_BORDER}`,
              },
              cellStyle: {
                border: `1px solid ${colors.TABLE_BORDER}`,
                textAlign: "center",
                margin: "auto",
              },
              actionsColumnIndex: -1,
            }}
            localization={{
              toolbar: {
                searchPlaceholder: t("search"),
                exportTitle: t("export"),
                exportCSVName: t("export"),
              },
              header: {
                actions: t("actions"),
              },
              pagination: {
                labelDisplayedRows: "{from}-{to} " + t("of") + " {count}",
                firstTooltip: t("first_page_tooltip"),
                previousTooltip: t("previous_page_tooltip"),
                nextTooltip: t("next_page_tooltip"),
                lastTooltip: t("last_page_tooltip"),
              },
            }}
          />
        </ThemeProvider>
      </Grid>
    </Grid>
  );
};

export default Putout;