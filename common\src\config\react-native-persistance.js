function getReactNativePersistence(storage) {
    const STORAGE_AVAILABLE_KEY = '__sak';
    const LOCAL = 'LOCAL';
    
    class Persistence {
      static type = LOCAL;
      type = LOCAL;
      
      async _isAvailable() {
        try {
          if (!storage) {
            return false;
          }
          await storage.setItem(STORAGE_AVAILABLE_KEY, '1');
          await storage.removeItem(STORAGE_AVAILABLE_KEY);
          return true;
        } catch (error) {
          console.warn('AsyncStorage not available:', error);
          return false;
        }
      }
  
      _set(key, value) {
        try {
          return storage.setItem(key, JSON.stringify(value));
        } catch (error) {
          console.warn('Failed to set item in AsyncStorage:', error);
          return Promise.resolve();
        }
      }
  
      async _get(key) {
        try {
          const json = await storage.getItem(key);
          return json ? JSON.parse(json) : null;
        } catch (error) {
          console.warn('Failed to get item from AsyncStorage:', error);
          return null;
        }
      }
  
      _remove(key) {
        try {
          return storage.removeItem(key);
        } catch (error) {
          console.warn('Failed to remove item from AsyncStorage:', error);
          return Promise.resolve();
        }
      }
  
      _addListener(_key, _listener) {
        return;
      }
  
      _removeListener(_key, _listener) {
        return;
      }
    }
  
    return Persistence;
  }

  export { getReactNativePersistence };