import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors } from '../common/theme';
import { fonts } from '../common/font';

const ApiUsageDisplay = ({ bookingData, showDetails = false }) => {
  if (!bookingData?.apiUsageData) {
    return null;
  }

  const { apiUsageData } = bookingData;
  const { totalApiCalls, apiCost, duration, trackingStartTime, lastApiCallTime } = apiUsageData;

  const formatDuration = (ms) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleTimeString();
  };

  // Calculate efficiency metrics for BEST CASE scenario
  const efficiencyRating = totalApiCalls <= 2 ? '🟢 EXCELLENT' : 
                         totalApiCalls <= 4 ? '🟡 GOOD' : 
                         totalApiCalls <= 6 ? '🟠 FAIR' : '🔴 POOR';

  const costSavings = Math.max(0, (15 - totalApiCalls) * 0.005); // Assuming 15 calls would be worst case
  const efficiencyPercentage = Math.max(0, Math.min(100, ((15 - totalApiCalls) / 15) * 100));

  return (
    <View style={styles.container}>
      <Text style={styles.title}>📊 API Usage (BEST CASE)</Text>
      
      <View style={styles.row}>
        <Text style={styles.label}>Total Calls:</Text>
        <Text style={styles.value}>{totalApiCalls}</Text>
      </View>
      
      <View style={styles.row}>
        <Text style={styles.label}>Cost:</Text>
        <Text style={styles.value}>${apiCost.toFixed(4)}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Efficiency:</Text>
        <Text style={styles.value}>{efficiencyRating}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Savings:</Text>
        <Text style={styles.value}>${costSavings.toFixed(4)}</Text>
      </View>

      <View style={styles.row}>
        <Text style={styles.label}>Optimization:</Text>
        <Text style={styles.value}>{efficiencyPercentage.toFixed(0)}%</Text>
      </View>
      
      {showDetails && (
        <>
          <View style={styles.row}>
            <Text style={styles.label}>Duration:</Text>
            <Text style={styles.value}>{formatDuration(duration)}</Text>
          </View>
          
          <View style={styles.row}>
            <Text style={styles.label}>Started:</Text>
            <Text style={styles.value}>{formatTime(trackingStartTime)}</Text>
          </View>
          
          <View style={styles.row}>
            <Text style={styles.label}>Last Call:</Text>
            <Text style={styles.value}>{formatTime(lastApiCallTime)}</Text>
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    padding: 10,
    margin: 5,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.LIGHT_GRAY || '#E0E0E0',
  },
  title: {
    fontSize: 16,
    fontFamily: fonts.Bold,
    color: colors.BLACK,
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.Regular,
    color: colors.BLACK,
  },
  value: {
    fontSize: 14,
    fontFamily: fonts.Bold,
    color: colors.INDICATOR_BLUE || '#007AFF',
  },
});

export default ApiUsageDisplay;
