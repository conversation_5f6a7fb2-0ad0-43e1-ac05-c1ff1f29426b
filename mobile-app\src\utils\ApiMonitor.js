// API Usage Monitor for tracking Google Maps API calls
class ApiMonitor {
  constructor() {
    this.callHistory = [];
    this.maxHistorySize = 100;
    this.startTime = Date.now();
  }

  // Log an API call
  logApiCall(apiType = 'Directions', success = true) {
    const call = {
      timestamp: Date.now(),
      apiType,
      success,
      timeSinceStart: Date.now() - this.startTime
    };
    
    this.callHistory.push(call);
    
    // Keep only recent calls
    if (this.callHistory.length > this.maxHistorySize) {
      this.callHistory.shift();
    }
    
    console.log(`🔍 API Call: ${apiType} - ${success ? 'Success' : 'Failed'} at ${new Date().toISOString()}`);
  }

  // Get current statistics
  getStats() {
    const now = Date.now();
    const sessionDuration = (now - this.startTime) / 1000; // seconds
    const recentCalls = this.callHistory.filter(call => 
      now - call.timestamp < 300000 // Last 5 minutes
    );
    
    return {
      totalCalls: this.callHistory.length,
      recentCalls: recentCalls.length,
      sessionDuration: Math.round(sessionDuration),
      callsPerMinute: Math.round((this.callHistory.length / sessionDuration) * 60),
      successRate: this.callHistory.filter(call => call.success).length / this.callHistory.length * 100,
      lastCall: this.callHistory.length > 0 ? this.callHistory[this.callHistory.length - 1] : null
    };
  }

  // Get cost estimate (assuming $0.005 per call)
  getCostEstimate() {
    const stats = this.getStats();
    const costPerCall = 0.005;
    return {
      totalCost: stats.totalCalls * costPerCall,
      costPerMinute: (stats.callsPerMinute * costPerCall),
      projectedHourlyCost: (stats.callsPerMinute * 60 * costPerCall)
    };
  }

  // Reset monitoring
  reset() {
    this.callHistory = [];
    this.startTime = Date.now();
  }

  // Print detailed report
  printReport() {
    const stats = this.getStats();
    const costs = this.getCostEstimate();
    
    console.log('📊 API Usage Report:');
    console.log(`Total Calls: ${stats.totalCalls}`);
    console.log(`Recent Calls (5min): ${stats.recentCalls}`);
    console.log(`Session Duration: ${stats.sessionDuration}s`);
    console.log(`Calls/Minute: ${stats.callsPerMinute}`);
    console.log(`Success Rate: ${stats.successRate.toFixed(1)}%`);
    console.log(`Total Cost: $${costs.totalCost.toFixed(4)}`);
    console.log(`Projected Hourly Cost: $${costs.projectedHourlyCost.toFixed(2)}`);
  }
}

// Singleton instance
const apiMonitor = new ApiMonitor();
export default apiMonitor;
