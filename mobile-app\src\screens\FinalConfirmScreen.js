import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch } from 'react-redux';
import { addBooking } from '../../../common/src/actions/bookingactions';
// You can use react-native-vector-icons or your own icon components
import { CommonActions } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

function formatTripDate(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

export default function FinalConfirmScreen({ route, navigation }) {
  const dispatch = useDispatch();
  const { bookingData } = route.params;

  const handleFinalConfirm = () => {
    dispatch(addBooking(bookingData));
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: 'TabRoot',
            state: {
              index: 0,
              routes: [
                { name: 'RideList' }
              ]
            }
          }
        ]
      })
    );
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.card}>
        {/* Pickup */}
        <View style={styles.row}>
          <Icon name="place" size={20} color="#4CAF50" />
          <Text style={styles.pickupText}>
            <Text style={styles.bold}>{bookingData.pickup?.description}</Text>
          </Text>
        </View>
        {/* Drop */}
        <View style={styles.row}>
          <Icon name="place" size={20} color="#FFA726" />
          <Text style={styles.dropText}>{bookingData.drop?.description}</Text>
        </View>
        {/* Fare, Tips, Total */}
        <View style={styles.amountSection}>
          <View style={styles.amountRow}>
            <View style={styles.estimateFare}> 
            <Text style={styles.amountLabel}>  {bookingData.symbol || '$'} Fare:</Text>
            </View>
            <Text style={styles.amountValue}>
              {bookingData.symbol || '$'}{bookingData.estimate?.estimateFare || 0}
            </Text>
          </View>
          <View style={styles.amountRow}>
            <View style={styles.estimateFare}>
            <Icon name="card-giftcard" size={20} color="#FFA726" />
            <Text style={styles.amountLabel}>Tips:</Text>
            </View>
            <Text style={styles.amountValue}>
              {bookingData.symbol || '$'}{bookingData.tips || 0}
            </Text>
          </View>
          <View style={styles.divider} />
          <View style={styles.amountRow}>
            <View  style={styles.estimateFare}>
            <Icon name="payments" size={22} color="#4CAF50" />
            <Text style={styles.totalLabel}>Total:</Text>
            </View>
            <Text style={styles.totalValue}>
              {bookingData.symbol || '$'}
              {(
                (parseFloat(bookingData.estimate?.estimateFare) || 0) +
                (parseFloat(bookingData.tips) || 0)
              ).toFixed(2)}
            </Text>
          </View>
        </View>
        {/* Fare, Car, Date */}
        <View style={styles.infoRow}> 
        <View style={styles.infoItem}>
            <Icon name="payment" size={20} color="#009688" />
            <Text style={styles.infoText}>
              {bookingData.payment_mode ? bookingData.payment_mode.charAt(0).toUpperCase() + bookingData.payment_mode.slice(1) : 'N/A'}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Icon name="directions-car" size={20} color="#222" />
            <Text style={styles.infoText}>{bookingData.carDetails?.name}</Text>
          </View>
          <View style={styles.infoItem}>
            <Icon name="date-range" size={20} color="#222" />
            <Text style={styles.infoText}>
              {formatTripDate(bookingData.tripdate)}
        {bookingData.returnDate ? (
            <>
            <Text>{"\n"}</Text>
            <Icon name="autorenew" size={20} color="#4CAF50" style={styles.dateIcon} />
      <Text>{"\n"}</Text>
          <Text style={styles.infoText}>
            {formatTripDate(bookingData.returnDate)}
          </Text></>
        ) : null}
            </Text>
          </View>
        </View>
        {/* Flight Number */}
        {bookingData.flightNumber ? (
          <Text style={styles.label}>
            Flight Number: <Text style={styles.bold}>{bookingData.flightNumber}</Text>
          </Text>
        ) : null}
        {bookingData.returnFlightNumber ? (
          <Text style={styles.label}>
            Return Flight Number: <Text style={styles.bold}>{bookingData.returnFlightNumber}</Text>
          </Text>
        ) : null}
        {/* Note */}
        {bookingData.noteText ? (
          <Text style={styles.label}>
            Note: <Text style={styles.bold}>{bookingData.noteText}</Text>
          </Text>
        ) : null}
        {/* User Info */}
        <View style={styles.userRow}>
          <Icon name="account-circle" size={32} color="#2196F3" />
          <View>
            <Text style={styles.bold}>{bookingData.userDetails.firstName + ' ' + bookingData.userDetails.lastName}</Text>
            <Text>{bookingData.userDetails.mobile? bookingData.userDetails.mobile: ' '}</Text>
          </View>
        </View>
      </View>
      {/* Confirm Button */}
      <TouchableOpacity style={styles.confirmButton} onPress={handleFinalConfirm}>
        <Text style={styles.confirmButtonText}>Confirm</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flexGrow: 1, justifyContent: 'center', alignItems: 'center', padding: 20, backgroundColor: '#fafafa' },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 30,
    width: '100%',
    elevation: 4,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  row: { flexDirection: 'row', alignItems: 'flex-start', marginBottom: 4 },
  pickupText: { marginLeft: 8, flex: 1, fontSize: 16 },
  dropText: { marginLeft: 8, flex: 1, fontSize: 15, color: '#222' },
  bold: { fontWeight: 'bold' },
  infoRow: { flexDirection: 'row', justifyContent: 'space-between', marginVertical: 16 },
  infoItem: { alignItems: 'center', flex: 1 },
  infoText: { fontWeight: 'bold', fontSize: 16, textAlign: 'center' },
  label: { fontSize: 15, marginBottom: 2 },
  userRow: { flexDirection: 'row', alignItems: 'center', marginTop: 16 },
  confirmButton: {
    backgroundColor: '#111',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    width: '100%',
  },
  confirmButtonText: { color: '#fff', fontSize: 20, fontWeight: 'bold' },
  amountSection: {
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    padding: 15,
    marginTop: 15,
    marginBottom: 15,
    width: '100%',
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  estimateFare:{
    display: 'flex',
    flexDirection:'row'
  },
  amountLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
  },
  amountValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  divider: {
    height: 1,
    backgroundColor: '#ccc',
    marginVertical: 10,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
});
